<?php
/**
 * Test script for fee-based quantity discount system
 * Add this to wp-config.php or run in WordPress admin context
 */

function test_fee_based_quantity_discount() {
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }
    
    echo "<h1>Fee-Based Quantity Discount Test</h1>";
    
    // Step 1: Check if classes and methods exist
    echo "<h2>Step 1: Class and Method Availability</h2>";
    
    if (class_exists('FSQP_Public')) {
        echo "<p style='color: green;'>✅ FSQP_Public class exists</p>";
        
        $fsqp_public = new FSQP_Public();
        $required_methods = [
            'add_quantity_discount_fee',
            'display_cart_item_discount',
            'display_discount_details_in_checkout',
            'display_discount_details_in_cart'
        ];
        
        foreach ($required_methods as $method) {
            if (method_exists($fsqp_public, $method)) {
                echo "<p style='color: green;'>✅ Method $method exists</p>";
            } else {
                echo "<p style='color: red;'>❌ Method $method missing</p>";
            }
        }
    } else {
        echo "<p style='color: red;'>❌ FSQP_Public class not found</p>";
        return;
    }
    
    // Step 2: Check hooks registration
    echo "<h2>Step 2: Hooks Registration</h2>";
    
    global $wp_filter;
    
    $hooks_to_check = [
        'woocommerce_cart_calculate_fees' => 'add_quantity_discount_fee',
        'woocommerce_cart_item_price' => 'display_cart_item_discount',
        'woocommerce_review_order_before_order_total' => 'display_discount_details_in_checkout',
        'woocommerce_cart_totals_before_order_total' => 'display_discount_details_in_cart'
    ];
    
    foreach ($hooks_to_check as $hook => $expected_method) {
        if (isset($wp_filter[$hook])) {
            echo "<p style='color: green;'>✅ Hook '$hook' is registered</p>";
            
            // Check if our method is attached
            $found_method = false;
            foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback) {
                    if (is_array($callback['function']) && 
                        $callback['function'][0] instanceof FSQP_Public &&
                        $callback['function'][1] === $expected_method) {
                        $found_method = true;
                        echo "<p>&nbsp;&nbsp;✅ Method '$expected_method' found at priority $priority</p>";
                        break 2;
                    }
                }
            }
            
            if (!$found_method) {
                echo "<p style='color: orange;'>&nbsp;&nbsp;⚠️ Method '$expected_method' not found</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Hook '$hook' not registered</p>";
        }
    }
    
    // Step 3: Test discount calculation
    echo "<h2>Step 3: Discount Calculation Test</h2>";
    
    if (class_exists('FSQP_Database')) {
        $database = new FSQP_Database();
        
        // Test with a sample product and quantity
        $test_product_id = 38; // Beanie with Logo
        $test_quantity = 5;
        $test_price = 20.00;
        
        echo "<p>Testing with Product ID: $test_product_id, Quantity: $test_quantity, Price: $test_price</p>";
        
        $discounted_price = $fsqp_public->calculate_quantity_discount($test_product_id, $test_quantity, $test_price);
        
        if ($discounted_price < $test_price) {
            $discount_amount = $test_price - $discounted_price;
            $total_discount = $discount_amount * $test_quantity;
            
            echo "<p style='color: green;'>✅ Discount calculation working:</p>";
            echo "<p>&nbsp;&nbsp;- Original price per item: $" . number_format($test_price, 2) . "</p>";
            echo "<p>&nbsp;&nbsp;- Discounted price per item: $" . number_format($discounted_price, 2) . "</p>";
            echo "<p>&nbsp;&nbsp;- Discount per item: $" . number_format($discount_amount, 2) . "</p>";
            echo "<p>&nbsp;&nbsp;- Total discount for $test_quantity items: $" . number_format($total_discount, 2) . "</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ No discount applied (may be normal if no rules exist)</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ FSQP_Database class not found</p>";
    }
    
    // Step 4: Test fee structure
    echo "<h2>Step 4: Fee Structure Test</h2>";
    
    // Simulate cart with quantity discount
    $mock_cart_data = [
        [
            'product_id' => 38,
            'quantity' => 5,
            'original_price' => 20.00,
            'discounted_price' => 18.00,
            'discount_per_item' => 2.00,
            'total_discount' => 10.00
        ]
    ];
    
    echo "<p>Mock cart data:</p>";
    foreach ($mock_cart_data as $item) {
        echo "<p>&nbsp;&nbsp;- Product {$item['product_id']}: {$item['quantity']} items</p>";
        echo "<p>&nbsp;&nbsp;- Discount per item: $" . number_format($item['discount_per_item'], 2) . "</p>";
        echo "<p>&nbsp;&nbsp;- Total discount: $" . number_format($item['total_discount'], 2) . "</p>";
    }
    
    // Step 5: Test session storage
    echo "<h2>Step 5: Session Storage Test</h2>";
    
    if (WC()->session) {
        echo "<p style='color: green;'>✅ WooCommerce session available</p>";
        
        // Test storing discount details
        $test_discount_details = [
            [
                'product_name' => 'Test Product',
                'quantity' => 5,
                'discount_per_item' => 2.00,
                'total_discount' => 10.00
            ]
        ];
        
        WC()->session->set('fsqp_discount_details', $test_discount_details);
        $retrieved_details = WC()->session->get('fsqp_discount_details');
        
        if ($retrieved_details && count($retrieved_details) === 1) {
            echo "<p style='color: green;'>✅ Session storage working</p>";
            echo "<p>&nbsp;&nbsp;- Stored and retrieved discount details successfully</p>";
        } else {
            echo "<p style='color: red;'>❌ Session storage not working</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ WooCommerce session not available</p>";
    }
    
    // Step 6: Test removed price modification hooks
    echo "<h2>Step 6: Price Modification Hooks Check</h2>";
    
    $removed_hooks = [
        'woocommerce_product_get_price',
        'woocommerce_product_variation_get_price',
        'woocommerce_before_calculate_totals'
    ];
    
    $hooks_still_present = [];
    
    foreach ($removed_hooks as $hook) {
        if (isset($wp_filter[$hook])) {
            foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback) {
                    if (is_array($callback['function']) && 
                        $callback['function'][0] instanceof FSQP_Public) {
                        $method_name = $callback['function'][1];
                        if (in_array($method_name, ['modify_product_price', 'modify_cart_item_prices'])) {
                            $hooks_still_present[] = "$hook::$method_name";
                        }
                    }
                }
            }
        }
    }
    
    if (empty($hooks_still_present)) {
        echo "<p style='color: green;'>✅ Old price modification hooks successfully removed</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Some old hooks still present:</p>";
        foreach ($hooks_still_present as $hook) {
            echo "<p>&nbsp;&nbsp;- $hook</p>";
        }
    }
    
    // Step 7: Summary and recommendations
    echo "<h2>Step 7: Summary</h2>";
    
    echo "<p><strong>New Fee-Based System Benefits:</strong></p>";
    echo "<ul>";
    echo "<li>✅ No price conflicts with core plugin or other addons</li>";
    echo "<li>✅ Discounts appear as separate line items in cart/checkout</li>";
    echo "<li>✅ Clear discount breakdown for customers</li>";
    echo "<li>✅ Proper order meta storage for reporting</li>";
    echo "<li>✅ Better compatibility with WooCommerce ecosystem</li>";
    echo "</ul>";
    
    echo "<p><strong>How it works:</strong></p>";
    echo "<ol>";
    echo "<li>Products maintain their original prices</li>";
    echo "<li>Quantity discounts are calculated and applied as negative fees</li>";
    echo "<li>Cart shows original price + discount fee separately</li>";
    echo "<li>Checkout shows detailed discount breakdown</li>";
    echo "<li>Orders store complete discount information</li>";
    echo "</ol>";
    
    echo "<p><strong>Testing Instructions:</strong></p>";
    echo "<ol>";
    echo "<li>Create a quantity promotion campaign</li>";
    echo "<li>Add products with quantity ranges</li>";
    echo "<li>Add products to cart with qualifying quantities</li>";
    echo "<li>Check cart totals for 'Quantity Discount' fee</li>";
    echo "<li>Proceed to checkout and verify discount details</li>";
    echo "<li>Complete order and check order meta</li>";
    echo "</ol>";
}

// Add admin page
add_action('admin_menu', function() {
    if (current_user_can('manage_options')) {
        add_submenu_page(
            null,
            'Test Fee-Based Discount',
            'Test Fee-Based Discount',
            'manage_options',
            'test-fee-based-discount',
            'test_fee_based_quantity_discount'
        );
    }
});

// Add quick access
add_action('admin_notices', function() {
    if (current_user_can('manage_options') && 
        (strpos($_SERVER['REQUEST_URI'], 'flashsale') !== false || 
         strpos($_SERVER['REQUEST_URI'], 'campaign') !== false)) {
        echo '<div class="notice notice-success"><p>';
        echo '<strong>New Fee-Based System:</strong> ';
        echo '<a href="?page=test-fee-based-discount" target="_blank" class="button button-small">Test Fee-Based Discount</a>';
        echo '</p></div>';
    }
});
