/**
 * Public styles for FlashSale Core
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

/* Promotion Info */
.fs-promotion-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.fs-promotion-item {
    margin-bottom: 20px;
}

.fs-promotion-item:last-child {
    margin-bottom: 0;
}

.fs-promotion-title {
    color: #dc3545;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 10px;
}

.fs-promotion-description {
    color: #6c757d;
    margin: 0 0 15px;
}

/* Promotion Badge */
.fs-promotion-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 10;
}

.fs-badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    border-radius: 4px;
    color: #fff;
}

.fs-badge-sale {
    background: #dc3545;
}

.fs-badge-limited {
    background: #fd7e14;
}

.fs-badge-new {
    background: #28a745;
}

/* Price Display */
.fs-original-price {
    color: #6c757d;
    text-decoration: line-through;
}

.fs-sale-price {
    color: #dc3545;
    font-weight: 600;
}

.fs-savings {
    color: #28a745;
    font-weight: 600;
    font-size: 14px;
}

/* Countdown Timer */
.fs-countdown-timer {
    background: #dc3545;
    color: #fff;
    padding: 15px;
    border-radius: 6px;
    text-align: center;
    margin: 15px 0;
}

.fs-countdown-label {
    display: block;
    font-size: 14px;
    margin-bottom: 10px;
    opacity: 0.9;
}

.fs-countdown-display {
    font-size: 20px;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.fs-countdown-display span {
    background: rgba(255, 255, 255, 0.1);
    padding: 5px 8px;
    border-radius: 4px;
    margin: 0 2px;
}

/* Stock Progress */
.fs-stock-progress {
    margin: 15px 0;
}

.fs-stock-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.fs-stock-sold {
    color: #dc3545;
    font-weight: 600;
}

.fs-stock-remaining {
    color: #28a745;
    font-weight: 600;
}

.fs-progress-bar {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.fs-progress-fill {
    height: 100%;
    background: #dc3545;
    transition: width 0.3s ease;
}

/* Promotion Gifts */
.fs-promotion-gifts {
    background: #e8f5e8;
    border: 1px solid #c3e6c3;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.fs-gifts-title {
    color: #155724;
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 10px;
}

.fs-gifts-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.fs-gift-item {
    background: #fff;
    border: 1px solid #c3e6c3;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
}

.fs-gift-name {
    color: #155724;
    font-weight: 500;
}

/* Campaign Shortcode */
.fs-campaign-shortcode {
    margin: 30px 0;
}

.fs-campaign-title {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 15px;
    text-align: center;
}

.fs-campaign-description {
    text-align: center;
    color: #6c757d;
    margin: 0 0 30px;
}

.fs-products-grid {
    display: grid;
    gap: 20px;
    margin: 20px 0;
}

.fs-columns-2 {
    grid-template-columns: repeat(2, 1fr);
}

.fs-columns-3 {
    grid-template-columns: repeat(3, 1fr);
}

.fs-columns-4 {
    grid-template-columns: repeat(4, 1fr);
}

.fs-columns-5 {
    grid-template-columns: repeat(5, 1fr);
}

.fs-campaign-product {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.fs-campaign-product:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.fs-product-image {
    position: relative;
    overflow: hidden;
}

.fs-product-image img {
    width: 100%;
    height: auto;
    transition: transform 0.3s;
}

.fs-campaign-product:hover .fs-product-image img {
    transform: scale(1.05);
}

.fs-product-info {
    padding: 15px;
}

.fs-product-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 10px;
}

.fs-product-title a {
    color: #333;
    text-decoration: none;
}

.fs-product-title a:hover {
    color: #dc3545;
}

.fs-product-price {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 15px;
}

.fs-product-actions {
    text-align: center;
}

.fs-view-product {
    background: #dc3545;
    color: #fff;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 600;
    transition: background 0.2s;
}

.fs-view-product:hover {
    background: #c82333;
    color: #fff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .fs-columns-4,
    .fs-columns-5 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .fs-countdown-display {
        font-size: 16px;
    }
    
    .fs-countdown-display span {
        padding: 3px 6px;
    }
    
    .fs-stock-info {
        flex-direction: column;
        gap: 5px;
    }
    
    .fs-promotion-info {
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .fs-columns-2,
    .fs-columns-3,
    .fs-columns-4,
    .fs-columns-5 {
        grid-template-columns: 1fr;
    }
    
    .fs-gifts-list {
        flex-direction: column;
    }
    
    .fs-countdown-timer {
        padding: 10px;
    }
    
    .fs-countdown-display {
        font-size: 14px;
    }
}

/* Animation */
@keyframes flash {
    0%, 50%, 100% {
        opacity: 1;
    }
    25%, 75% {
        opacity: 0.5;
    }
}

/* Removed flash animation for simpler styling */

/* Loading States */
.fs-loading {
    opacity: 0.6;
    pointer-events: none;
}

.fs-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #ccc;
    border-top-color: #dc3545;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Variation Promotions */
.fs-variation-promotions {
    margin: 20px 0;
}

.fs-variation-notice {
    background: #e6f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    padding: 12px;
    color: #0066cc;
    font-style: italic;
    text-align: center;
    margin-bottom: 15px;
}

.fs-variation-promotion {
    border: 2px solid #0073aa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    background: #f8f9fa;
    animation: slideIn 0.3s ease;
}

.fs-variation-info {
    background: #0073aa;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    margin-bottom: 10px;
    font-size: 14px;
}

.fs-main-product-promotion {
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Progress Bar for Variations */
.fs-variation-promotion .fs-progress-bar {
    background: linear-gradient(90deg, #e9ecef, #f8f9fa);
    border: 1px solid #0073aa;
}

.fs-variation-promotion .fs-progress-fill {
    background: linear-gradient(90deg, #0073aa, #005a87);
}

/* Mobile Responsive for Variations */
@media (max-width: 768px) {
    .fs-variation-promotion {
        padding: 12px;
        margin-bottom: 12px;
    }
    
    .fs-variation-info {
        padding: 6px 10px;
        font-size: 13px;
    }
    
    .fs-variation-notice {
        padding: 10px;
        font-size: 13px;
    }
}
