/**
 * Frontend styles for Quantity Promotion
 */

/* Quantity Promotion Info on Product Page */
.fsqp-quantity-promotion-info {
    margin: 20px 0;
    padding: 20px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.fsqp-quantity-promotion-info h4 {
    margin: 0 0 15px 0;
    font-size: 18px;
    color: #2c3e50;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.fsqp-quantity-promotion-info h4::before {
    content: "🎯";
    font-size: 20px;
}

.fsqp-rules-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.fsqp-rules-table th,
.fsqp-rules-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.fsqp-rules-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.fsqp-rules-table tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

.fsqp-rules-table tbody tr:last-child td {
    border-bottom: none;
}

/* Quantity column styling */
.fsqp-rules-table td:first-child {
    font-weight: 600;
    color: #495057;
}

/* Discount column styling */
.fsqp-rules-table td:nth-child(2) {
    color: #28a745;
    font-weight: 600;
}

/* Price column styling */
.fsqp-rules-table td:last-child {
    color: #dc3545;
    font-weight: 600;
    font-size: 16px;
}

/* Cart Savings Display */
.fsqp-savings {
    color: #28a745;
    font-weight: 600;
    font-size: 12px;
    background: #d4edda;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 8px;
    display: inline-block;
}

/* Quantity Input Enhancement */
.quantity .qty {
    position: relative;
}

.fsqp-quantity-hint {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-top: none;
    padding: 8px 12px;
    font-size: 12px;
    color: #856404;
    border-radius: 0 0 4px 4px;
    z-index: 10;
    display: none;
}

.fsqp-quantity-hint.show {
    display: block;
}

.fsqp-quantity-hint strong {
    color: #28a745;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .fsqp-quantity-promotion-info {
        margin: 15px 0;
        padding: 15px;
    }
    
    .fsqp-quantity-promotion-info h4 {
        font-size: 16px;
    }
    
    .fsqp-rules-table {
        font-size: 14px;
    }
    
    .fsqp-rules-table th,
    .fsqp-rules-table td {
        padding: 8px 10px;
    }
    
    .fsqp-rules-table th {
        font-size: 12px;
    }
    
    /* Stack table on very small screens */
    @media (max-width: 480px) {
        .fsqp-rules-table,
        .fsqp-rules-table thead,
        .fsqp-rules-table tbody,
        .fsqp-rules-table th,
        .fsqp-rules-table td,
        .fsqp-rules-table tr {
            display: block;
        }
        
        .fsqp-rules-table thead tr {
            position: absolute;
            top: -9999px;
            left: -9999px;
        }
        
        .fsqp-rules-table tr {
            border: 1px solid #ccc;
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 6px;
            background: #fff;
        }
        
        .fsqp-rules-table td {
            border: none;
            position: relative;
            padding: 8px 8px 8px 50%;
            text-align: left;
        }
        
        .fsqp-rules-table td:before {
            content: attr(data-label) ": ";
            position: absolute;
            left: 6px;
            width: 45%;
            padding-right: 10px;
            white-space: nowrap;
            font-weight: 600;
            color: #495057;
        }
    }
}

/* Animation for savings highlight */
@keyframes fsqp-highlight {
    0% { background-color: #fff3cd; }
    50% { background-color: #ffeaa7; }
    100% { background-color: #fff3cd; }
}

.fsqp-savings.highlight {
    animation: fsqp-highlight 1s ease-in-out;
}

/* Tooltip styles */
.fsqp-tooltip {
    position: relative;
    display: inline-block;
    cursor: help;
}

.fsqp-tooltip .fsqp-tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #555;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
    line-height: 1.4;
}

.fsqp-tooltip .fsqp-tooltiptext::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #555 transparent transparent transparent;
}

.fsqp-tooltip:hover .fsqp-tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Loading states */
.fsqp-loading {
    opacity: 0.6;
    pointer-events: none;
}

.fsqp-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: fsqp-spin 1s linear infinite;
}

@keyframes fsqp-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success message in cart */
.fsqp-cart-message {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 10px 15px;
    border-radius: 4px;
    margin: 10px 0;
    font-size: 14px;
}

.fsqp-cart-message .fsqp-icon {
    margin-right: 8px;
    color: #28a745;
}

/* Quantity selector enhancement */
.fsqp-quantity-selector {
    position: relative;
    display: inline-block;
}

.fsqp-quantity-benefits {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #dee2e6;
    border-top: none;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 100;
    display: none;
    max-height: 200px;
    overflow-y: auto;
}

.fsqp-quantity-benefits.show {
    display: block;
}

.fsqp-benefit-item {
    padding: 8px 12px;
    border-bottom: 1px solid #f8f9fa;
    font-size: 12px;
    line-height: 1.4;
}

.fsqp-benefit-item:last-child {
    border-bottom: none;
}

.fsqp-benefit-item.active {
    background: #e7f3ff;
    border-left: 3px solid #007cba;
}

.fsqp-benefit-quantity {
    font-weight: 600;
    color: #495057;
}

.fsqp-benefit-discount {
    color: #28a745;
    font-weight: 600;
}

.fsqp-benefit-price {
    color: #dc3545;
    font-weight: 600;
}

/* Cart and Checkout Discount Styles */
.fsqp-discount-info {
    color: #27ae60 !important;
    font-weight: 500;
    font-size: 0.9em;
    display: block;
    margin-top: 5px;
}

.fsqp-discount-details th {
    background: #f8f9fa !important;
    color: #2c3e50 !important;
    font-weight: 600 !important;
    border-top: 2px solid #dee2e6 !important;
}

.fsqp-discount-item td {
    font-size: 0.9em;
    padding: 8px 12px !important;
}

.fsqp-discount-item td:first-child {
    color: #666 !important;
}

.fsqp-discount-item td:last-child {
    color: #27ae60 !important;
    font-weight: 500;
}

/* Fee line styling in cart totals */
.cart_totals .shop_table tr.fee td,
.woocommerce-checkout-review-order-table tr.fee td {
    color: #27ae60 !important;
    font-weight: 600;
}

.cart_totals .shop_table tr.fee th,
.woocommerce-checkout-review-order-table tr.fee th {
    color: #2c3e50 !important;
}

/* Quantity discount fee specific styling */
tr.fee:has(.amount:contains("Quantity Discount")) th,
tr.fee[data-fee-name*="quantity"] th {
    color: #2c3e50 !important;
    font-weight: 600;
}

tr.fee:has(.amount:contains("Quantity Discount")) .amount,
tr.fee[data-fee-name*="quantity"] .amount {
    color: #27ae60 !important;
    font-weight: 600;
}

/* Mobile responsive for discount details */
@media (max-width: 768px) {
    .fsqp-discount-details th,
    .fsqp-discount-item td {
        padding: 8px 5px !important;
        font-size: 0.85em !important;
    }

    .fsqp-discount-info {
        font-size: 0.8em;
    }
}
