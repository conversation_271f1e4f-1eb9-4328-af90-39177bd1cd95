/**
 * Public JavaScript for FlashSale Core
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

(function($) {
    'use strict';
    
    /**
     * FlashSale Public Class
     */
    var FlashSalePublic = {
        
        /**
         * Initialize
         */
        init: function() {
            this.initCountdownTimers();
            this.initStockProgress();
            this.initPromotionBadges();
            this.initVariationPromotions();
            this.bindEvents();
        },
        
        /**
         * Initialize countdown timers
         */
        initCountdownTimers: function() {
            $('.fs-countdown-timer').each(function() {
                var $timer = $(this);
                var endTime = parseInt($timer.data('end-time')) * 1000;
                
                FlashSalePublic.startCountdown($timer, endTime);
            });
        },
        
        /**
         * Start countdown timer
         */
        startCountdown: function($timer, endTime) {
            var interval = setInterval(function() {
                var now = new Date().getTime();
                var distance = endTime - now;
                
                if (distance < 0) {
                    clearInterval(interval);
                    FlashSalePublic.onCountdownExpired($timer);
                    return;
                }
                
                var days = Math.floor(distance / (1000 * 60 * 60 * 24));
                var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                var seconds = Math.floor((distance % (1000 * 60)) / 1000);
                
                $timer.find('.fs-countdown-days').text(FlashSalePublic.padZero(days));
                $timer.find('.fs-countdown-hours').text(FlashSalePublic.padZero(hours));
                $timer.find('.fs-countdown-minutes').text(FlashSalePublic.padZero(minutes));
                $timer.find('.fs-countdown-seconds').text(FlashSalePublic.padZero(seconds));
                
            }, 1000);
        },
        
        /**
         * Handle countdown expiration
         */
        onCountdownExpired: function($timer) {
            $timer.find('.fs-countdown-display').html('<span class="fs-expired">' + fs_public.strings.expired + '</span>');
            $timer.addClass('fs-expired');
            
            // Trigger custom event
            $(document).trigger('fs_countdown_expired', [$timer]);
        },
        
        /**
         * Pad number with zero
         */
        padZero: function(num) {
            return num < 10 ? '0' + num : num;
        },
        
        /**
         * Initialize stock progress animations
         */
        initStockProgress: function() {
            $('.fs-progress-fill').each(function() {
                var $fill = $(this);
                var width = $fill.css('width');
                
                $fill.css('width', '0');
                
                setTimeout(function() {
                    $fill.css('width', width);
                }, 500);
            });
        },
        
        /**
         * Initialize promotion badges
         */
        initPromotionBadges: function() {
            $('.fs-promotion-badge').each(function() {
                var $badge = $(this);
                
                // Add entrance animation
                $badge.addClass('fs-badge-animate');
                
                // Auto-hide after some time if configured
                var autoHide = $badge.data('auto-hide');
                if (autoHide) {
                    setTimeout(function() {
                        $badge.fadeOut();
                    }, autoHide * 1000);
                }
            });
        },
        
        /**
         * Initialize variation promotions
         */
        initVariationPromotions: function() {
            // Handle variation changes on variable products
            $('.variations_form').on('found_variation', function(event, variation) {
                FlashSalePublic.showVariationPromotion(variation.variation_id);
            });
            
            $('.variations_form').on('reset_data', function() {
                FlashSalePublic.hideAllVariationPromotions();
            });
        },
        
        /**
         * Show promotion for specific variation
         */
        showVariationPromotion: function(variationId) {
            // Hide all variation promotions first
            $('.fs-variation-promotion').hide();
            $('.fs-main-product-promotion').hide();
            
            // Show promotion for selected variation
            var $variationPromo = $('.fs-variation-promotion[data-variation-id="' + variationId + '"]');
            if ($variationPromo.length) {
                $variationPromo.fadeIn(300);
                
                // Reinitialize timers and progress bars for this variation
                this.initCountdownTimers();
                this.initStockProgress();
            } else {
                // Show main product promotion if no specific variation promotion
                $('.fs-main-product-promotion').fadeIn(300);
            }
        },
        
        /**
         * Hide all variation promotions
         */
        hideAllVariationPromotions: function() {
            $('.fs-variation-promotion').hide();
            $('.fs-main-product-promotion').hide();
            
            // Show the generic notice
            $('.fs-variation-notice').show();
        },
        
        /**
         * Bind events
         */
        bindEvents: function() {
            // Refresh promotion data
            $(document).on('click', '.fs-refresh-promotions', this.refreshPromotions);
            
            // Track promotion views
            $(document).on('click', '.fs-campaign-product a', this.trackPromotionView);
            
            // Handle gift selection
            $(document).on('click', '.fs-select-gift', this.handleGiftSelection);
            
            // Handle quantity changes for tiered pricing
            $(document).on('change', '.quantity input', this.handleQuantityChange);
        },
        
        /**
         * Refresh promotion data
         */
        refreshPromotions: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var productId = $button.data('product-id');
            
            $button.addClass('fs-loading');
            
            $.ajax({
                url: fs_public.ajax_url,
                type: 'POST',
                data: {
                    action: 'fs_refresh_promotions',
                    product_id: productId,
                    nonce: fs_public.nonce
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    }
                },
                complete: function() {
                    $button.removeClass('fs-loading');
                }
            });
        },
        
        /**
         * Track promotion view
         */
        trackPromotionView: function(e) {
            var $link = $(this);
            var campaignId = $link.closest('.fs-campaign-product').parent().data('campaign-id');
            var productId = $link.data('product-id');
            
            if (campaignId && productId) {
                $.ajax({
                    url: fs_public.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'fs_track_promotion_view',
                        campaign_id: campaignId,
                        product_id: productId,
                        nonce: fs_public.nonce
                    }
                });
            }
        },
        
        /**
         * Handle gift selection
         */
        handleGiftSelection: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var giftId = $button.data('gift-id');
            var campaignId = $button.data('campaign-id');
            
            // Open gift selection modal or process directly
            FlashSalePublic.openGiftModal(giftId, campaignId);
        },
        
        /**
         * Open gift selection modal
         */
        openGiftModal: function(giftId, campaignId) {
            // Create modal HTML
            var modalHtml = '<div class="fs-gift-modal">' +
                '<div class="fs-modal-content">' +
                '<div class="fs-modal-header">' +
                '<h3>' + fs_public.strings.select_gift + '</h3>' +
                '<button class="fs-modal-close">&times;</button>' +
                '</div>' +
                '<div class="fs-modal-body">' +
                '<div class="fs-loading">Loading gifts...</div>' +
                '</div>' +
                '</div>' +
                '</div>';
            
            $('body').append(modalHtml);
            
            // Load gift options
            $.ajax({
                url: fs_public.ajax_url,
                type: 'POST',
                data: {
                    action: 'fs_get_gift_options',
                    gift_id: giftId,
                    campaign_id: campaignId,
                    nonce: fs_public.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $('.fs-modal-body').html(response.data.html);
                    } else {
                        $('.fs-modal-body').html('<p>Error loading gifts.</p>');
                    }
                }
            });
            
            // Bind modal events
            $('.fs-modal-close, .fs-gift-modal').on('click', function(e) {
                if (e.target === this) {
                    $('.fs-gift-modal').remove();
                }
            });
        },
        
        /**
         * Handle quantity change for tiered pricing
         */
        handleQuantityChange: function() {
            var $input = $(this);
            var quantity = parseInt($input.val());
            var productId = $input.closest('form').find('[name="add-to-cart"]').val();
            
            if (!productId) {
                return;
            }
            
            // Check for quantity-based pricing
            FlashSalePublic.updateQuantityPricing(productId, quantity);
        },
        
        /**
         * Update quantity-based pricing
         */
        updateQuantityPricing: function(productId, quantity) {
            $.ajax({
                url: fs_public.ajax_url,
                type: 'POST',
                data: {
                    action: 'fs_get_quantity_pricing',
                    product_id: productId,
                    quantity: quantity,
                    nonce: fs_public.nonce
                },
                success: function(response) {
                    if (response.success && response.data.price_html) {
                        $('.price').html(response.data.price_html);
                        
                        // Update total if exists
                        if (response.data.total_html) {
                            $('.fs-quantity-total').html(response.data.total_html);
                        }
                    }
                }
            });
        },
        
        /**
         * Utility: Show notification
         */
        showNotification: function(message, type) {
            type = type || 'info';
            
            var $notification = $('<div class="fs-notification fs-notification-' + type + '">' + message + '</div>');
            
            $('body').append($notification);
            
            setTimeout(function() {
                $notification.addClass('fs-show');
            }, 100);
            
            setTimeout(function() {
                $notification.removeClass('fs-show');
                setTimeout(function() {
                    $notification.remove();
                }, 300);
            }, 3000);
        },
        
        /**
         * Utility: Format price
         */
        formatPrice: function(price, currency) {
            currency = currency || '$';
            return currency + parseFloat(price).toFixed(2);
        }
    };
    
    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        FlashSalePublic.init();
    });
    
    /**
     * Expose to global scope
     */
    window.FlashSalePublic = FlashSalePublic;
    
})(jQuery);
