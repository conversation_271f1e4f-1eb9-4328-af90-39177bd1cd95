/**
 * Frontend JavaScript for Quantity Promotion
 */

(function($) {
    'use strict';
    
    let productId = 0;
    let quantityDiscounts = [];
    
    $(document).ready(function() {
        initQuantityPromotion();
        initVariableProductSupport();
    });
    
    function initQuantityPromotion() {
        // Get product ID
        productId = getProductId();
        
        if (!productId) {
            return;
        }
        
        // Load quantity discounts
        loadQuantityDiscounts();
        
        // Bind events
        bindEvents();
    }
    
    function getProductId() {
        // Try to get product ID from various sources
        let id = 0;
        
        // From form data
        const $form = $('form.cart');
        if ($form.length) {
            const $productIdInput = $form.find('input[name="product_id"]');
            if ($productIdInput.length) {
                id = parseInt($productIdInput.val());
            }
        }
        
        // From body class
        if (!id) {
            const bodyClasses = $('body').attr('class') || '';
            const match = bodyClasses.match(/postid-(\d+)/);
            if (match) {
                id = parseInt(match[1]);
            }
        }
        
        // From data attribute
        if (!id) {
            const $product = $('.product');
            if ($product.length) {
                id = parseInt($product.data('product-id'));
            }
        }
        
        return id;
    }
    
    function loadQuantityDiscounts() {
        $.ajax({
            url: wc_add_to_cart_params.ajax_url,
            type: 'POST',
            data: {
                action: 'fsqp_get_quantity_discounts',
                product_id: productId
            },
            success: function(response) {
                if (response.success && response.data.discounts) {
                    quantityDiscounts = response.data.discounts;
                    enhanceQuantityInput();
                }
            },
            error: function() {
                console.log('Failed to load quantity discounts');
            }
        });
    }
    
    function bindEvents() {
        // Quantity input change
        $(document).on('change input', '.qty', updateQuantityBenefits);
        
        // Quantity input focus/blur
        $(document).on('focus', '.qty', showQuantityBenefits);
        $(document).on('blur', '.qty', function() {
            setTimeout(hideQuantityBenefits, 200);
        });
        
        // Cart updates
        $(document.body).on('updated_cart_totals', updateCartSavings);
        $(document.body).on('added_to_cart', showAddToCartMessage);
    }
    
    function enhanceQuantityInput() {
        if (quantityDiscounts.length === 0) {
            return;
        }
        
        const $qtyInput = $('.qty');
        if (!$qtyInput.length) {
            return;
        }
        
        // Wrap quantity input
        if (!$qtyInput.parent().hasClass('fsqp-quantity-selector')) {
            $qtyInput.wrap('<div class="fsqp-quantity-selector"></div>');
        }
        
        // Create benefits dropdown
        const $benefits = $('<div class="fsqp-quantity-benefits"></div>');
        
        quantityDiscounts.forEach(function(discount, index) {
            const quantityText = discount.max_quantity > 0 
                ? `${discount.min_quantity} - ${discount.max_quantity}` 
                : `${discount.min_quantity}+`;
            
            const discountText = discount.discount_type === 1 
                ? `${discount.discount_value}%` 
                : `$${discount.discount_value}`;
            
            const savingsText = discount.savings > 0 
                ? `Save $${discount.savings.toFixed(2)}` 
                : '';
            
            const $item = $(`
                <div class="fsqp-benefit-item" data-min-qty="${discount.min_quantity}" data-max-qty="${discount.max_quantity}">
                    <div class="fsqp-benefit-quantity">${quantityText} items</div>
                    <div class="fsqp-benefit-discount">${discountText} off - ${savingsText}</div>
                    <div class="fsqp-benefit-price">$${discount.discounted_price.toFixed(2)} each</div>
                </div>
            `);
            
            $benefits.append($item);
        });
        
        $qtyInput.parent().append($benefits);
        
        // Update initial state
        updateQuantityBenefits();
    }
    
    function showQuantityBenefits() {
        $('.fsqp-quantity-benefits').addClass('show');
    }
    
    function hideQuantityBenefits() {
        $('.fsqp-quantity-benefits').removeClass('show');
    }
    
    function updateQuantityBenefits() {
        const quantity = parseInt($('.qty').val()) || 1;
        
        // Update active benefit
        $('.fsqp-benefit-item').removeClass('active');
        
        quantityDiscounts.forEach(function(discount) {
            const minQty = discount.min_quantity;
            const maxQty = discount.max_quantity || Number.MAX_SAFE_INTEGER;
            
            if (quantity >= minQty && quantity <= maxQty) {
                $(`.fsqp-benefit-item[data-min-qty="${minQty}"]`).addClass('active');
            }
        });
        
        // Update price display
        updatePriceDisplay(quantity);
        
        // Show quantity hint
        showQuantityHint(quantity);
    }
    
    function updatePriceDisplay(quantity) {
        $.ajax({
            url: wc_add_to_cart_params.ajax_url,
            type: 'POST',
            data: {
                action: 'fsqp_calculate_quantity_price',
                product_id: productId,
                quantity: quantity
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    
                    // Update price display
                    const $priceElement = $('.price .amount, .price .woocommerce-Price-amount');
                    if ($priceElement.length && data.discounted_price < data.original_price) {
                        const originalHtml = $priceElement.html();
                        const newHtml = `
                            <del>${originalHtml}</del>
                            <ins>${data.price_html}</ins>
                            <span class="fsqp-savings">Save $${data.discount_amount.toFixed(2)}</span>
                        `;
                        $priceElement.html(newHtml);
                    }
                    
                    // Update total if exists
                    const $totalElement = $('.fsqp-total-price');
                    if ($totalElement.length) {
                        $totalElement.html(data.total_html);
                    }
                }
            }
        });
    }
    
    function showQuantityHint(quantity) {
        // Remove existing hint
        $('.fsqp-quantity-hint').remove();
        
        // Find next discount tier
        let nextDiscount = null;
        let currentDiscount = null;
        
        quantityDiscounts.forEach(function(discount) {
            const minQty = discount.min_quantity;
            const maxQty = discount.max_quantity || Number.MAX_SAFE_INTEGER;
            
            if (quantity >= minQty && quantity <= maxQty) {
                currentDiscount = discount;
            } else if (minQty > quantity && (!nextDiscount || minQty < nextDiscount.min_quantity)) {
                nextDiscount = discount;
            }
        });
        
        let hintText = '';
        
        if (currentDiscount) {
            const savingsText = currentDiscount.discount_type === 1 
                ? `${currentDiscount.discount_value}%` 
                : `$${currentDiscount.discount_value}`;
            hintText = `<strong>Active:</strong> ${savingsText} discount applied!`;
        }
        
        if (nextDiscount) {
            const moreNeeded = nextDiscount.min_quantity - quantity;
            const nextSavingsText = nextDiscount.discount_type === 1 
                ? `${nextDiscount.discount_value}%` 
                : `$${nextDiscount.discount_value}`;
            
            if (hintText) {
                hintText += ` Add ${moreNeeded} more for ${nextSavingsText} discount.`;
            } else {
                hintText = `Add ${moreNeeded} more items for ${nextSavingsText} discount!`;
            }
        }
        
        if (hintText) {
            const $hint = $(`<div class="fsqp-quantity-hint show">${hintText}</div>`);
            $('.qty').after($hint);
            
            setTimeout(function() {
                $hint.removeClass('show');
            }, 5000);
        }
    }
    
    function updateCartSavings() {
        // Update cart savings display
        $('.fsqp-savings').each(function() {
            $(this).addClass('highlight');
            setTimeout(() => {
                $(this).removeClass('highlight');
            }, 1000);
        });
    }
    
    function showAddToCartMessage(event, fragments, cart_hash, $button) {
        // Show success message when item with discount is added to cart
        const quantity = parseInt($('.qty').val()) || 1;
        
        quantityDiscounts.forEach(function(discount) {
            const minQty = discount.min_quantity;
            const maxQty = discount.max_quantity || Number.MAX_SAFE_INTEGER;
            
            if (quantity >= minQty && quantity <= maxQty) {
                const savingsText = discount.discount_type === 1 
                    ? `${discount.discount_value}%` 
                    : `$${discount.discount_value}`;
                
                const totalSavings = discount.savings * quantity;
                
                const message = `
                    <div class="fsqp-cart-message">
                        <span class="fsqp-icon">🎉</span>
                        Great! You saved $${totalSavings.toFixed(2)} with quantity discount (${savingsText} off)
                    </div>
                `;
                
                $('.woocommerce-notices-wrapper').append(message);
                
                setTimeout(function() {
                    $('.fsqp-cart-message').fadeOut();
                }, 5000);
            }
        });
    }
    
    // Utility functions
    function formatPrice(price) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(price);
    }
    
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Debounced version of updateQuantityBenefits
    const debouncedUpdateQuantityBenefits = debounce(updateQuantityBenefits, 300);
    
    // Replace the direct binding with debounced version
    $(document).off('input', '.qty').on('input', '.qty', debouncedUpdateQuantityBenefits);

    /**
     * Initialize variable product support
     * This handles progress bars and promotions for variable products
     */
    function initVariableProductSupport() {
        // Handle variation changes
        $('.variations_form').on('found_variation', function(event, variation) {
            handleVariationChange(variation.variation_id);
        });

        $('.variations_form').on('reset_data', function() {
            handleVariationReset();
        });

        // Initialize progress bars for variable products
        initVariableProgressBars();
    }

    /**
     * Handle variation change
     */
    function handleVariationChange(variationId) {
        // Update product ID for quantity promotion
        productId = variationId;

        // Reload quantity discounts for this variation
        loadQuantityDiscounts();

        // Show/hide progress bars for this variation
        showVariationProgressBar(variationId);

        // Update promotion displays
        updateVariationPromotions(variationId);
    }

    /**
     * Handle variation reset
     */
    function handleVariationReset() {
        // Reset to main product ID
        productId = getMainProductId();

        // Hide all variation-specific progress bars
        hideAllVariationProgressBars();

        // Show main product progress bar if exists
        showMainProductProgressBar();

        // Reset quantity discounts
        quantityDiscounts = [];
        $('.fsqp-quantity-benefits').remove();
    }

    /**
     * Get main product ID (not variation)
     */
    function getMainProductId() {
        // Try to get main product ID from various sources
        let id = 0;

        // From body class
        const bodyClasses = $('body').attr('class') || '';
        const match = bodyClasses.match(/postid-(\d+)/);
        if (match) {
            id = parseInt(match[1]);
        }

        // From data attribute on main product
        if (!id) {
            const $product = $('.product');
            if ($product.length) {
                id = parseInt($product.data('product-id')) || parseInt($product.attr('data-product-id'));
            }
        }

        return id;
    }

    /**
     * Initialize progress bars for variable products
     */
    function initVariableProgressBars() {
        // Find all progress bars and ensure they have proper data attributes
        $('.fs-progress-bar, .fs-stock-progress').each(function() {
            const $progressBar = $(this);

            // Add data key if missing
            if (!$progressBar.attr('data-key')) {
                const productId = $progressBar.closest('[data-product-id]').attr('data-product-id') ||
                                 $progressBar.attr('data-product-id') ||
                                 getMainProductId();

                if (productId) {
                    $progressBar.attr('data-key', productId);
                }
            }

            // Remove any inline display:none styles that might hide variable product progress bars
            if ($progressBar.css('display') === 'none') {
                $progressBar.css('display', '');
            }
        });
    }

    /**
     * Show progress bar for specific variation
     */
    function showVariationProgressBar(variationId) {
        // Hide all progress bars first
        $('.fs-progress-bar, .fs-stock-progress').hide();

        // Show progress bar for this variation
        const $variationProgressBar = $(`.fs-progress-bar[data-key="${variationId}"], .fs-stock-progress[data-key="${variationId}"]`);
        if ($variationProgressBar.length) {
            $variationProgressBar.show();

            // Reinitialize progress bar animation
            reinitializeProgressBar($variationProgressBar);
        } else {
            // Show main product progress bar if no variation-specific one exists
            showMainProductProgressBar();
        }
    }

    /**
     * Hide all variation progress bars
     */
    function hideAllVariationProgressBars() {
        $('.fs-progress-bar[data-key], .fs-stock-progress[data-key]').hide();
    }

    /**
     * Show main product progress bar
     */
    function showMainProductProgressBar() {
        const mainProductId = getMainProductId();
        const $mainProgressBar = $(`.fs-progress-bar[data-key="${mainProductId}"], .fs-stock-progress[data-key="${mainProductId}"]`);

        if ($mainProgressBar.length) {
            $mainProgressBar.show();
            reinitializeProgressBar($mainProgressBar);
        }
    }

    /**
     * Reinitialize progress bar animation
     */
    function reinitializeProgressBar($progressBar) {
        const $fill = $progressBar.find('.fs-progress-fill');
        if ($fill.length) {
            const width = $fill.attr('style') ? $fill.attr('style').match(/width:\s*([^;]+)/) : null;
            const targetWidth = width ? width[1] : $fill.css('width');

            // Reset and animate
            $fill.css('width', '0');
            setTimeout(function() {
                $fill.css('width', targetWidth);
            }, 100);
        }
    }

    /**
     * Update promotion displays for variation
     */
    function updateVariationPromotions(variationId) {
        // Hide all promotion displays
        $('.fs-promotion-display, .fs-flash-sale-bar').hide();

        // Show promotion for this variation
        const $variationPromotion = $(`.fs-promotion-display[data-variation-id="${variationId}"], .fs-flash-sale-bar[data-variation-id="${variationId}"]`);
        if ($variationPromotion.length) {
            $variationPromotion.show();
        } else {
            // Show main product promotion if no variation-specific one exists
            const mainProductId = getMainProductId();
            $(`.fs-promotion-display[data-product-id="${mainProductId}"], .fs-flash-sale-bar[data-product-id="${mainProductId}"]`).show();
        }
    }

})(jQuery);
