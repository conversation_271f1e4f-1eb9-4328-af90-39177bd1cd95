# 📋 Bulk Actions Implementation Guide

## 🎯 Overview

Đã triển khai hệ thống bulk actions tái sử dụng cho cả core plugin và addons, thay thế code duplicate và cung cấp functionality thực sự.

## 🔧 Architecture

### 1. **Shared Library (Core Plugin)**
- **File**: `/admin/js/bulk-actions.js`
- **Namespace**: `window.FSBulkActions`
- **Purpose**: Cung cấp functionality chung cho tất cả plugins

### 2. **CSS Styles (Core Plugin)**
- **File**: `/admin/css/admin.css`
- **Classes**: `.fs-bulk-menu`, `.fs-bulk-menu-item`, etc.
- **Purpose**: Styling cho dropdown menu và interactions

### 3. **Plugin-Specific Implementation**
- **Core Plugin**: Flash sale specific actions
- **Quantity Promotion**: Quantity promotion specific actions + "Apply Rules"

## 🚀 Features Implemented

### ✅ **Core Functionality**
1. **Select All/Individual Selection**
2. **Dynamic Bulk Button State**
3. **Dropdown Menu with Actions**
4. **Confirmation Dialogs**
5. **Success/Error Notifications**

### ✅ **Available Actions**

#### **Flash Sale Plugin**:
- 🗑️ **Delete Selected**: Remove products from campaign
- 📋 **Duplicate Selected**: Clone product settings
- 📥 **Export Selected**: Export to JSON

#### **Quantity Promotion Plugin**:
- 🗑️ **Delete Selected**: Remove products from campaign
- 📋 **Duplicate Selected**: Clone product settings with rules
- 📥 **Export Selected**: Export with quantity rules
- ⚙️ **Apply Rules to Selected**: Bulk apply quantity rules

## 📝 Usage Guide

### **For Core Plugin (Flash Sale)**

```javascript
// Initialize bulk actions
FSBulkActions.init({
    container: '.fs-products-section',
    selectAllId: '#fs-select-all-products',
    checkboxClass: '.fs-product-checkbox',
    bulkButtonId: '#fs-bulk-actions-btn',
    bulkMenuId: '#fs-bulk-actions-menu',
    actions: {
        'delete': {
            label: 'Delete Selected',
            icon: 'dashicons-trash',
            class: 'fs-bulk-delete',
            confirm: 'Are you sure you want to delete the selected products?'
        },
        'duplicate': {
            label: 'Duplicate Selected',
            icon: 'dashicons-admin-page',
            class: 'fs-bulk-duplicate'
        },
        'export': {
            label: 'Export Selected',
            icon: 'dashicons-download',
            class: 'fs-bulk-export'
        }
    },
    callbacks: {
        onBulkAction: function(action, selectedItems) {
            handleFlashSaleBulkAction(action, selectedItems);
        }
    }
});
```

### **For Addon Plugins (Quantity Promotion)**

```javascript
// Initialize with addon-specific actions
FSBulkActions.init({
    // ... same base config ...
    actions: {
        // ... standard actions ...
        'apply-rules': {
            label: 'Apply Rules to Selected',
            icon: 'dashicons-admin-settings',
            class: 'fs-bulk-apply-rules'
        }
    },
    callbacks: {
        onBulkAction: function(action, selectedItems) {
            handleQuantityPromotionBulkAction(action, selectedItems);
        }
    }
});
```

## 🎨 UI Components

### **Bulk Actions Button**
```html
<button type="button" id="fs-bulk-actions-btn" class="fs-btn fs-btn-outline" disabled>
    Bulk Actions
</button>
```

### **Dropdown Menu** (Auto-generated)
```html
<div id="fs-bulk-actions-menu" class="fs-bulk-menu">
    <div class="fs-bulk-menu-item fs-bulk-delete" data-action="delete">
        <span class="dashicons dashicons-trash"></span>
        Delete Selected
    </div>
    <div class="fs-bulk-menu-item fs-bulk-duplicate" data-action="duplicate">
        <span class="dashicons dashicons-admin-page"></span>
        Duplicate Selected
    </div>
    <!-- ... more actions ... -->
</div>
```

### **Checkboxes**
```html
<!-- Select All -->
<input type="checkbox" id="fs-select-all-products">

<!-- Individual Items -->
<input type="checkbox" class="fs-product-checkbox" value="product_id">
```

## 🔄 Action Flow

1. **User selects products** → Checkboxes checked
2. **Bulk button activates** → Shows count `Bulk Actions (3)`
3. **User clicks bulk button** → Dropdown menu appears
4. **User selects action** → Confirmation dialog (if needed)
5. **Action executes** → Custom handler function runs
6. **Feedback shown** → Success/error notification
7. **UI updates** → Selection cleared, counts updated

## 📊 Data Structure

### **Selected Items Object**
```javascript
{
    checkbox: HTMLElement,    // The checkbox element
    row: jQuery,             // The table row
    id: string,              // Product ID or row ID
    data: Object             // Row data attributes
}
```

### **Export Data Format**

**Flash Sale**:
```json
{
    "product_id": "123",
    "product_name": "Sample Product",
    "discount_type": "1",
    "discount_value": "20",
    "qty_max": "100"
}
```

**Quantity Promotion**:
```json
{
    "product_id": "123",
    "product_name": "Sample Product",
    "rules": [
        {
            "min_quantity": "1",
            "max_quantity": "5",
            "discount_type": "1",
            "discount_value": "10"
        }
    ]
}
```

## 🎯 Specific Features

### **Quantity Promotion: Apply Rules Modal**

Unique feature cho quantity promotion plugin:

```javascript
function bulkApplyRules(selectedItems) {
    // Shows modal with:
    // - Min Quantity input
    // - Max Quantity input  
    // - Discount Type select
    // - Discount Value input
    
    // Applies rules to all selected products
}
```

### **Smart Duplication**

- **Flash Sale**: Simple row cloning
- **Quantity Promotion**: Updates array indices in form names

### **Export Functionality**

- **Auto-download JSON files**
- **Timestamped filenames**
- **Plugin-specific data structure**

## 🔧 Customization

### **Adding New Actions**

```javascript
// In plugin-specific code
actions: {
    'custom-action': {
        label: 'Custom Action',
        icon: 'dashicons-admin-tools',
        class: 'fs-bulk-custom',
        confirm: 'Confirm this action?'
    }
}

// Handle in callback
function handleBulkAction(action, selectedItems) {
    switch (action) {
        case 'custom-action':
            // Your custom logic here
            break;
    }
}
```

### **Custom Styling**

```css
.fs-bulk-menu-item.fs-bulk-custom:hover {
    background-color: #e3f2fd;
    color: #1976d2;
}
```

## 📱 Responsive Design

- **Mobile-friendly dropdown**
- **Touch-friendly buttons**
- **Responsive modal dialogs**
- **Flexible grid layouts**

## 🧪 Testing

### **Manual Testing Checklist**

- [ ] Select all functionality works
- [ ] Individual selection updates select all state
- [ ] Bulk button enables/disables correctly
- [ ] Dropdown menu appears on click
- [ ] All actions execute properly
- [ ] Confirmations show when needed
- [ ] Success messages display
- [ ] Export downloads work
- [ ] Mobile responsive

### **Browser Compatibility**

- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

## 🚀 Benefits Achieved

### ✅ **Code Reusability**
- Shared library eliminates duplicate code
- Consistent behavior across plugins
- Easy to maintain and update

### ✅ **Better UX**
- Professional dropdown interface
- Clear visual feedback
- Intuitive interactions

### ✅ **Extensibility**
- Easy to add new actions
- Plugin-specific customizations
- Flexible configuration options

### ✅ **Maintainability**
- Centralized bulk actions logic
- Consistent coding patterns
- Clear separation of concerns

## 📋 Migration Notes

### **Before (Problems)**
- Duplicate code in both plugins
- No actual functionality
- Inconsistent behavior
- Hard to maintain

### **After (Solutions)**
- Shared library in core plugin
- Full functionality implemented
- Consistent across all plugins
- Easy to extend and maintain

## 🎯 Future Enhancements

1. **AJAX Actions**: Server-side bulk operations
2. **Progress Indicators**: For long-running operations
3. **Undo Functionality**: Reverse bulk actions
4. **Keyboard Shortcuts**: Power user features
5. **Batch Processing**: Handle large datasets

Hệ thống bulk actions mới cung cấp foundation mạnh mẽ cho tất cả plugins trong ecosystem! 🚀
