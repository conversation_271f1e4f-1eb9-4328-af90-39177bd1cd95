<?php
/**
 * Updated test file to verify the fixes for:
 * 1. Remove product count update issue
 * 2. Variable product progress bar and countdown display
 * 
 * @package FlashSaleCore
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test Fix 1: Product Count Update After Removal
 */
function test_product_count_update() {
    echo "<h2>Test 1: Product Count Update After Removal</h2>";
    
    $flash_sale_file = 'admin/views/campaign-types/flash-sale.php';
    
    if (file_exists($flash_sale_file)) {
        $content = file_get_contents($flash_sale_file);
        
        // Check for improved updateProductCount function
        $has_empty_state_logic = strpos($content, 'if (count === 0) {') !== false;
        $has_fadeout_removal = strpos($content, '$row.fadeOut(300, function()') !== false;
        $has_bulk_count_fix = strpos($content, 'deletedCount++') !== false;
        
        echo "<p><strong>Empty State Logic:</strong> " . ($has_empty_state_logic ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Fadeout Removal:</strong> " . ($has_fadeout_removal ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Bulk Delete Count Fix:</strong> " . ($has_bulk_count_fix ? '✅ Yes' : '❌ No') . "</p>";
        
        if ($has_empty_state_logic && $has_fadeout_removal && $has_bulk_count_fix) {
            echo "<p style='color: green;'>✅ Product count update functionality is properly fixed!</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Product count update functionality needs attention.</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Flash sale file not found.</p>";
    }
}

/**
 * Test Fix 2: Variable Product Progress Bar & Countdown Display
 */
function test_variable_product_display() {
    echo "<h2>Test 2: Variable Product Progress Bar & Countdown Display</h2>";
    
    // Check PHP file - countdown timers should no longer be hidden
    $flash_sale_bar_file = 'public/partials/single-product/flash-sale-bar.php';
    
    if (file_exists($flash_sale_bar_file)) {
        $content = file_get_contents($flash_sale_bar_file);
        
        // Check if hidden styles are removed from countdown
        $has_hidden_countdown = strpos($content, 'countdown-fs') !== false && strpos($content, 'style="display: none;"') === false;
        $has_data_key_countdown = strpos($content, 'countdown-fs') !== false && strpos($content, 'data-key="<?php echo $p[\'product_id\']; ?>"') !== false;
        $has_progress_data_key = strpos($content, 'sold-feal') !== false && strpos($content, 'data-key="<?php echo $p[\'product_id\']; ?>"') !== false;
        
        echo "<p><strong>Countdown Not Hidden:</strong> " . ($has_hidden_countdown ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Countdown Data Key:</strong> " . ($has_data_key_countdown ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Progress Data Key:</strong> " . ($has_progress_data_key ? '✅ Yes' : '❌ No') . "</p>";
        
        if ($has_hidden_countdown && $has_data_key_countdown && $has_progress_data_key) {
            echo "<p style='color: green;'>✅ Variable product PHP template is properly fixed!</p>";
        } else {
            echo "<p style='color: red;'>❌ Variable product PHP template still has issues.</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Flash sale bar file not found.</p>";
    }
    
    // Check JavaScript functionality
    $public_js_file = 'public/js/public.js';
    
    if (file_exists($public_js_file)) {
        $js_content = file_get_contents($public_js_file);
        
        $has_woocommerce_events = strpos($js_content, 'found_variation') !== false;
        $has_show_variation_elements = strpos($js_content, 'showVariationElements') !== false;
        $has_debug_logging = strpos($js_content, 'console.log') !== false;
        $has_countdown_hiding = strpos($js_content, '_1iFmQA[data-key]') !== false;
        
        echo "<p><strong>WooCommerce Events:</strong> " . ($has_woocommerce_events ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Show Variation Elements:</strong> " . ($has_show_variation_elements ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Debug Logging:</strong> " . ($has_debug_logging ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Countdown Hiding Logic:</strong> " . ($has_countdown_hiding ? '✅ Yes' : '❌ No') . "</p>";
        
        if ($has_woocommerce_events && $has_show_variation_elements && $has_countdown_hiding) {
            echo "<p style='color: green;'>✅ JavaScript for variable products is properly enhanced!</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ JavaScript for variable products needs attention.</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Public JS file not found.</p>";
    }
    
    // Check CSS styles
    $public_css_file = 'public/css/public.css';
    
    if (file_exists($public_css_file)) {
        $css_content = file_get_contents($public_css_file);
        
        $has_countdown_data_key_styles = strpos($css_content, '._1iFmQA[data-key]') !== false;
        $has_countdown_active_styles = strpos($css_content, '._1iFmQA[data-key].active') !== false;
        $has_general_countdown_styles = strpos($css_content, '._1iFmQA {') !== false;
        $has_debug_styles = strpos($css_content, '.debug-variation') !== false;
        
        echo "<p><strong>Countdown Data Key Styles:</strong> " . ($has_countdown_data_key_styles ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Countdown Active Styles:</strong> " . ($has_countdown_active_styles ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>General Countdown Styles:</strong> " . ($has_general_countdown_styles ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Debug Styles:</strong> " . ($has_debug_styles ? '✅ Yes' : '❌ No') . "</p>";
        
        if ($has_countdown_data_key_styles && $has_countdown_active_styles && $has_general_countdown_styles) {
            echo "<p style='color: green;'>✅ CSS for variable products is properly enhanced!</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ CSS for variable products needs attention.</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Public CSS file not found.</p>";
    }
}

/**
 * Test debugging features
 */
function test_debugging_features() {
    echo "<h2>Test 3: Debugging Features</h2>";
    
    echo "<p><strong>Console Logging:</strong> Check browser console for debug messages when selecting variations</p>";
    echo "<p><strong>CSS Debug Classes:</strong> Add 'debug-variation' class to elements for visual debugging</p>";
    
    echo "<div style='background: #f0f8ff; padding: 15px; border-left: 4px solid #007cba; margin: 10px 0;'>";
    echo "<h4>How to test Variable Products:</h4>";
    echo "<ol>";
    echo "<li>Go to a WooCommerce variable product page</li>";
    echo "<li>Open browser Developer Tools (F12) and check Console tab</li>";
    echo "<li>Select different variations and watch console logs</li>";
    echo "<li>Verify progress bars and countdown timers show/hide correctly</li>";
    echo "<li>Check that only the selected variation's elements are visible</li>";
    echo "</ol>";
    echo "</div>";
}

/**
 * Run all tests
 */
function run_all_tests() {
    echo "<h1>FlashSale Core - Updated Fixes Verification</h1>";
    echo "<p>Testing the updated fixes for product count and variable product display issues.</p>";
    echo "<hr>";
    
    test_product_count_update();
    echo "<hr>";
    
    test_variable_product_display();
    echo "<hr>";
    
    test_debugging_features();
    echo "<hr>";
    
    echo "<h2>Summary</h2>";
    echo "<p><strong>Issues Fixed:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Product count now updates correctly after removal (single and bulk)</li>";
    echo "<li>✅ Countdown timers are no longer hidden by default for variable products</li>";
    echo "<li>✅ Progress bars and countdown timers show/hide based on selected variation</li>";
    echo "<li>✅ Added WooCommerce event handling for better variation detection</li>";
    echo "<li>✅ Added console logging for debugging</li>";
    echo "<li>✅ Enhanced CSS styling for countdown timers</li>";
    echo "</ul>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0;'>";
    echo "<h4>⚠️ Important Notes:</h4>";
    echo "<ul>";
    echo "<li>Clear browser cache after implementing these changes</li>";
    echo "<li>Test on actual WooCommerce variable product pages</li>";
    echo "<li>Check browser console for any JavaScript errors</li>";
    echo "<li>Verify that the correct variation IDs are being passed to elements</li>";
    echo "</ul>";
    echo "</div>";
}

// Run tests if this file is accessed directly
if (isset($_GET['run_tests']) && $_GET['run_tests'] == '1') {
    run_all_tests();
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>FlashSale Core - Updated Fixes Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h1 { color: #333; }
        h2 { color: #666; border-bottom: 1px solid #eee; padding-bottom: 5px; }
        h4 { color: #555; margin-top: 20px; }
        p { margin: 8px 0; }
        ul, ol { margin: 10px 0; padding-left: 20px; }
        li { margin: 4px 0; }
        hr { margin: 20px 0; border: none; border-top: 1px solid #eee; }
        .test-button { 
            background: #007cba; 
            color: white; 
            padding: 12px 24px; 
            text-decoration: none; 
            border-radius: 6px; 
            display: inline-block; 
            margin: 15px 0;
            font-weight: 500;
        }
        .test-button:hover { background: #005a87; color: white; }
    </style>
</head>
<body>
    <?php if (!isset($_GET['run_tests'])): ?>
        <h1>FlashSale Core - Updated Fixes Test</h1>
        <p>This test will verify the latest fixes for product count updates and variable product display issues.</p>
        <a href="?run_tests=1" class="test-button">Run Updated Tests</a>
        
        <h2>What's New in This Update:</h2>
        <ul>
            <li><strong>Fixed Product Count Update:</strong> Count now updates correctly after removing products</li>
            <li><strong>Fixed Variable Product Display:</strong> Progress bars and countdown timers now show properly for selected variations</li>
            <li><strong>Enhanced JavaScript:</strong> Better WooCommerce integration and debugging features</li>
            <li><strong>Improved CSS:</strong> Better styling and show/hide logic for variable product elements</li>
        </ul>
    <?php else: ?>
        <?php run_all_tests(); ?>
        <p><a href="?" class="test-button">Run Tests Again</a></p>
    <?php endif; ?>
</body>
</html> 