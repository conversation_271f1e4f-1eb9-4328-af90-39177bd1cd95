# Migration Guide - FlashSale Core v2

## Plugin đã được làm sạch và tối ưu

### C<PERSON><PERSON> thay đổi đã thực hiện:

#### ✅ Đã xóa các file cũ từ ait-dev:
- `admin/class-ait-dev-admin.php`
- `includes/class-ait-dev-*.php` (tất cả các class cũ)
- `public/class-ait-dev-public.php`
- `admin/css/ait-dev-admin.css`
- `public/css/ait-dev-public.css`
- `admin/js/ait-dev-admin.js`
- `public/js/ait-dev-public.js`
- Tất cả các file `admin/partials/ait-dev-*.php`

#### ✅ Đã xóa thư mục addons:
- Addons giờ là các plugin riêng biệt, không nằm trong core plugin
- Cần cài đặt addons như các plugin WordPress thông thường

#### ✅ Đã cập nhật styling:
- <PERSON><PERSON><PERSON> bỏ gradient phức tạp, thay bằng WordPress color scheme
- Áp dụng màu sắc chuẩn WordPress (#0073aa, #00a32a, #d63638, etc.)
- Thêm prefix `.fs-` cho tất cả CSS classes để tránh conflict
- Đơn giản hóa shadow effects và transitions

#### ✅ Đã xóa các file legacy khác:
- `admin/function-admin-ajax.php` (AJAX handler cũ)
- `admin/cites/` (dữ liệu địa chỉ Việt Nam không dùng)
- Tất cả các file `admin/partials/ait-dev-*.php`
- Tất cả các file `public/js/ait-dev-*.js`

#### ✅ Đã sửa logic addons:
- **Loại bỏ dữ liệu demo** từ `class-fs-activator.php`
- **Thêm cleanup method** để xóa addons demo đã tồn tại
- **Chỉ hiển thị addons thực sự** được cài đặt từ database
- **Empty state** với hướng dẫn cài đặt addon rõ ràng

#### ✅ Đã thay đổi container class:
- **Từ**: `.fs-admin-wrap` (có thể conflict)
- **Thành**: `.flashsale-admin-container` (riêng biệt)
- **Không ẩn notices** từ WordPress/plugin khác
- **Styling riêng** với border-radius và shadow

## Cấu trúc plugin hiện tại (đã làm sạch)

```
flashsale-core-v2/
├── flashsale-core.php
├── includes/
│   ├── class-fs-core.php
│   ├── class-fs-database.php
│   ├── class-fs-campaign-manager.php
│   ├── class-fs-addon-manager.php
│   ├── class-fs-rule-engine.php
│   ├── class-fs-gift-manager.php
│   ├── class-fs-api.php
│   ├── class-fs-legacy.php
│   ├── class-fs-activator.php
│   └── class-fs-deactivator.php
├── admin/
│   ├── class-fs-admin.php
│   ├── css/
│   │   └── admin.css (đã cập nhật WordPress style)
│   ├── js/
│   │   └── admin.js
│   ├── views/
│   │   ├── main-page.php
│   │   ├── campaigns-page.php
│   │   ├── addons-page.php
│   │   └── settings-page.php
│   └── partials/
│       └── views/
├── public/
│   ├── class-fs-public.php
│   ├── css/
│   │   └── public.css
│   ├── js/
│   │   └── public.js
│   └── partials/
│       ├── content-product-combo.php
│       ├── content-product-upsell.php
│       ├── email-template.php
│       └── single-product/
└── README.md
```

## Lưu ý quan trọng

1. **Backup dữ liệu** trước khi migration
2. **Test trên staging** trước khi deploy production
3. **Migration tự động** sẽ chuyển đổi dữ liệu từ schema cũ
4. **Legacy support** đảm bảo tương thích ngược
5. **Có thể chạy song song** với plugin cũ trong thời gian chuyển đổi
