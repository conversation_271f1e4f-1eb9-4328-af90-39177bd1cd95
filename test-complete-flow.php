<?php
/**
 * Complete flow test for quantity promotion save
 * Add this to wp-config.php or run in WordPress admin context
 */

// Enable debug logging
if (!defined('WP_DEBUG_LOG')) {
    define('WP_DEBUG_LOG', true);
}

// Test complete save flow
function test_complete_quantity_save_flow() {
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }
    
    echo "<h1>Complete Quantity Promotion Save Flow Test</h1>";
    
    // Step 1: Check if all classes exist
    echo "<h2>Step 1: Class Availability</h2>";
    $required_classes = [
        'FS_Core',
        'FS_Campaign_Manager', 
        'FSQP_Database',
        'FSQP_Campaign_Handler'
    ];
    
    foreach ($required_classes as $class) {
        if (class_exists($class)) {
            echo "<p style='color: green;'>✅ $class exists</p>";
        } else {
            echo "<p style='color: red;'>❌ $class missing</p>";
            return;
        }
    }
    
    // Step 2: Check database tables
    echo "<h2>Step 2: Database Tables</h2>";
    global $wpdb;
    $tables = [
        'fs_campaigns' => $wpdb->prefix . 'fs_campaigns',
        'fs_campaign_products' => $wpdb->prefix . 'fs_campaign_products'
    ];
    
    foreach ($tables as $name => $table) {
        $exists = $wpdb->get_var("SHOW TABLES LIKE '$table'") === $table;
        if ($exists) {
            echo "<p style='color: green;'>✅ $name table exists</p>";
        } else {
            echo "<p style='color: red;'>❌ $name table missing</p>";
            return;
        }
    }
    
    // Step 3: Check hooks registration
    echo "<h2>Step 3: Hooks Registration</h2>";
    global $wp_filter;
    
    $hooks = ['fs_save_campaign', 'ccp_save_campaign'];
    foreach ($hooks as $hook) {
        if (isset($wp_filter[$hook])) {
            echo "<p style='color: green;'>✅ $hook is registered</p>";
            
            // Check for FSQP handler
            $found_fsqp = false;
            foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback) {
                    if (is_array($callback['function']) && 
                        $callback['function'][0] instanceof FSQP_Campaign_Handler) {
                        $found_fsqp = true;
                        echo "<p style='color: green;'>&nbsp;&nbsp;✅ FSQP handler found at priority $priority</p>";
                        break 2;
                    }
                }
            }
            
            if (!$found_fsqp) {
                echo "<p style='color: orange;'>&nbsp;&nbsp;⚠️ FSQP handler not found</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ $hook not registered</p>";
        }
    }
    
    // Step 4: Test campaign creation
    echo "<h2>Step 4: Test Campaign Creation</h2>";
    
    $campaign_data = [
        'name' => 'Test QP Campaign ' . date('H:i:s'),
        'description' => 'Test campaign for debugging',
        'type' => 'quantity-promotion',
        'priority' => 10,
        'status' => 1,
        'start_date' => date('Y-m-d H:i:s'),
        'end_date' => date('Y-m-d H:i:s', strtotime('+30 days')),
        'global_settings' => []
    ];
    
    try {
        $campaign_manager = new FS_Campaign_Manager();
        $campaign_id = $campaign_manager->create_campaign($campaign_data);
        
        if ($campaign_id) {
            echo "<p style='color: green;'>✅ Campaign created with ID: $campaign_id</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create campaign</p>";
            return;
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Campaign creation failed: " . $e->getMessage() . "</p>";
        return;
    }
    
    // Step 5: Test hook trigger
    echo "<h2>Step 5: Test Hook Trigger</h2>";
    
    $products_data = [
        '38' => [
            'product_id' => 38,
            'ranges' => [
                [
                    'min_quantity' => 1,
                    'max_quantity' => 5,
                    'discount_type' => 1,
                    'discount_value' => 5,
                    'max_discount_amount' => 0
                ]
            ]
        ]
    ];
    
    // Simulate form data
    $_POST['products'] = $products_data;
    
    echo "<p>Triggering fs_save_campaign hook...</p>";
    do_action('fs_save_campaign', $campaign_id, $campaign_data, $_POST);
    
    // Step 6: Verify data was saved
    echo "<h2>Step 6: Verify Data Saved</h2>";
    
    $count = $wpdb->get_var($wpdb->prepare("
        SELECT COUNT(*) 
        FROM {$wpdb->prefix}fs_campaign_products 
        WHERE promotion_id = %d
    ", $campaign_id));
    
    if ($count > 0) {
        echo "<p style='color: green;'>✅ Found $count records in database</p>";
        
        // Show the records
        $records = $wpdb->get_results($wpdb->prepare("
            SELECT * 
            FROM {$wpdb->prefix}fs_campaign_products 
            WHERE promotion_id = %d
        ", $campaign_id));
        
        foreach ($records as $record) {
            echo "<p>Record: Product {$record->product_id}, Qty {$record->qty_max}-{$record->percent_sold_max}, Discount {$record->discount_value}%</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ No records found in database</p>";
    }
    
    // Step 7: Test loading
    echo "<h2>Step 7: Test Data Loading</h2>";
    
    $database = new FSQP_Database();
    $loaded_rules = $database->get_campaign_quantity_rules($campaign_id, false);
    
    if (!empty($loaded_rules)) {
        echo "<p style='color: green;'>✅ Loaded " . count($loaded_rules) . " rules</p>";
        foreach ($loaded_rules as $rule) {
            echo "<p>Loaded: Product {$rule->product_id}, Qty {$rule->min_quantity}-{$rule->max_quantity}, Discount {$rule->discount_value}%</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ No rules loaded</p>";
    }
    
    // Cleanup
    echo "<h2>Cleanup</h2>";
    if (isset($_GET['cleanup']) && $_GET['cleanup'] === '1') {
        $wpdb->delete($wpdb->prefix . 'fs_campaign_products', ['promotion_id' => $campaign_id]);
        $wpdb->delete($wpdb->prefix . 'fs_campaigns', ['id' => $campaign_id]);
        echo "<p style='color: blue;'>🧹 Test data cleaned up</p>";
    } else {
        echo "<p><a href='?page=test-complete-flow&cleanup=1' class='button'>Clean Up Test Data</a></p>";
    }
    
    echo "<h2>Debug Information</h2>";
    echo "<p>Check your error log for detailed debug information from the save process.</p>";
    echo "<p>Error log location: " . ini_get('error_log') . "</p>";
}

// Add admin page
add_action('admin_menu', function() {
    if (current_user_can('manage_options')) {
        add_submenu_page(
            null,
            'Test Complete Flow',
            'Test Complete Flow', 
            'manage_options',
            'test-complete-flow',
            'test_complete_quantity_save_flow'
        );
    }
});

// Add quick access
add_action('admin_notices', function() {
    if (current_user_can('manage_options') && 
        (strpos($_SERVER['REQUEST_URI'], 'flashsale') !== false || 
         strpos($_SERVER['REQUEST_URI'], 'campaign') !== false)) {
        echo '<div class="notice notice-info"><p>';
        echo '<strong>Debug Tools:</strong> ';
        echo '<a href="?page=test-complete-flow" target="_blank" class="button button-small">Test Complete Flow</a>';
        echo '</p></div>';
    }
});
