<?php
/**
 * Fired during plugin deactivation
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class FS_Deactivator {
    
    /**
     * Plugin deactivation
     */
    public static function deactivate() {
        // Clear scheduled events
        self::clear_scheduled_events();
        
        // Clear cache
        self::clear_cache();
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Set deactivation flag
        update_option('fs_deactivation_time', current_time('mysql'));
        
        do_action('flashsale_core_deactivated');
    }
    
    /**
     * Clear scheduled events
     */
    private static function clear_scheduled_events() {
        // Clear any scheduled cron events
        wp_clear_scheduled_hook('fs_update_campaign_stats');
        wp_clear_scheduled_hook('fs_cleanup_expired_campaigns');
        wp_clear_scheduled_hook('fs_cache_cleanup');
    }
    
    /**
     * Clear cache
     */
    private static function clear_cache() {
        // Clear any cached data
        wp_cache_delete('fs_active_campaigns');
        wp_cache_delete('fs_campaign_rules');
        
        // Clear transients
        delete_transient('fs_campaigns_cache');
        delete_transient('fs_addons_cache');
    }
}
