<?php
/**
 * Test script to verify price application
 * Run this from WordPress admin or via WP-CLI
 */

// Ensure this is run in WordPress context
if (!defined('ABSPATH')) {
    // If running via WP-CLI or direct access, load WordPress
    require_once dirname(__FILE__) . '/../../../wp-config.php';
}

echo "Testing FlashSale Price Application...\n";

try {
    // Check if FlashSale is loaded
    if (!function_exists('FS')) {
        echo "FlashSale Core not loaded. Loading manually...\n";
        require_once dirname(__FILE__) . '/includes/class-fs-core.php';
        FS_Core::get_instance();
    }
    
    // Get active campaigns
    $campaigns = FS()->campaigns()->get_active_campaigns();
    echo "Found " . count($campaigns) . " active campaigns\n";
    
    if (empty($campaigns)) {
        echo "No active campaigns found. Please create and activate a campaign first.\n";
        return;
    }
    
    foreach ($campaigns as $campaign) {
        echo "\nCampaign: {$campaign->name} (ID: {$campaign->id})\n";
        echo "Status: " . ($campaign->status ? 'Active' : 'Inactive') . "\n";
        echo "Start: {$campaign->start_date}\n";
        echo "End: {$campaign->end_date}\n";
        
        // Get campaign products
        $products = FS()->campaigns()->get_campaign_products($campaign->id);
        echo "Products in campaign: " . count($products) . "\n";
        
        foreach ($products as $product_data) {
            echo "\n  Product ID: {$product_data->product_id}\n";
            echo "  Discount Type: " . ($product_data->discount_type == 1 ? 'Percentage' : 'Fixed Amount') . "\n";
            echo "  Discount Value: {$product_data->discount_value}\n";
            echo "  Max Discount: {$product_data->max_discount_amount}\n";
            
            // Get WooCommerce product
            $wc_product = wc_get_product($product_data->product_id);
            if ($wc_product) {
                echo "  Product Name: {$wc_product->get_name()}\n";
                echo "  Regular Price: {$wc_product->get_regular_price()}\n";
                echo "  Current Sale Price: {$wc_product->get_sale_price()}\n";
                echo "  Price HTML: " . strip_tags($wc_product->get_price_html()) . "\n";
                
                // Test our price calculation
                $fs_public = new FS_Public();
                $original_price = $wc_product->get_regular_price();
                
                // Calculate what the sale price should be
                if ($product_data->discount_type == 1) { // percentage
                    $calculated_price = $original_price * (1 - $product_data->discount_value / 100);
                } else { // fixed amount
                    $calculated_price = $original_price - $product_data->discount_value;
                }
                
                echo "  Calculated FlashSale Price: " . number_format($calculated_price, 2) . "\n";
            } else {
                echo "  Product not found in WooCommerce\n";
            }
        }
    }
    
    echo "\nTest completed.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
