<?php
/**
 * Test script to check plugin conflicts between core and quantity promotion addon
 * Add this to wp-config.php or run in WordPress admin context
 */

function test_plugin_conflict_resolution() {
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }
    
    echo "<h1>Plugin Conflict Resolution Test</h1>";
    
    // Step 1: Check hooks registration and priorities
    echo "<h2>Step 1: Hooks Registration & Priorities</h2>";
    
    global $wp_filter;
    
    $hooks_to_check = [
        'woocommerce_before_calculate_totals',
        'woocommerce_single_product_summary',
        'woocommerce_product_get_price',
        'woocommerce_get_price_html'
    ];
    
    foreach ($hooks_to_check as $hook) {
        echo "<h3>Hook: $hook</h3>";
        
        if (isset($wp_filter[$hook])) {
            echo "<p style='color: green;'>✅ Hook is registered</p>";
            
            // Sort by priority to see execution order
            $callbacks_by_priority = $wp_filter[$hook]->callbacks;
            ksort($callbacks_by_priority);
            
            foreach ($callbacks_by_priority as $priority => $callbacks) {
                echo "<p><strong>Priority $priority:</strong></p>";
                
                foreach ($callbacks as $callback) {
                    if (is_array($callback['function'])) {
                        $class = get_class($callback['function'][0]);
                        $method = $callback['function'][1];
                        
                        $plugin_type = 'Unknown';
                        if (strpos($class, 'FS_Public') !== false) {
                            $plugin_type = 'Core Plugin (Flash Sale)';
                        } elseif (strpos($class, 'FSQP_Public') !== false) {
                            $plugin_type = 'Quantity Promotion Addon';
                        }
                        
                        echo "<p>&nbsp;&nbsp;- $class::$method ($plugin_type)</p>";
                        
                        // Check for conflicts
                        if ($plugin_type === 'Quantity Promotion Addon' && $priority >= 5) {
                            echo "<p style='color: orange;'>&nbsp;&nbsp;&nbsp;&nbsp;⚠️ Warning: Addon priority should be < 5 to run before core</p>";
                        }
                        
                        if ($plugin_type === 'Core Plugin (Flash Sale)' && $priority <= 3) {
                            echo "<p style='color: orange;'>&nbsp;&nbsp;&nbsp;&nbsp;⚠️ Warning: Core priority should be > 3 to run after addon</p>";
                        }
                    }
                }
            }
        } else {
            echo "<p style='color: red;'>❌ Hook is not registered</p>";
        }
        
        echo "<hr>";
    }
    
    // Step 2: Test campaign type filtering
    echo "<h2>Step 2: Campaign Type Filtering</h2>";
    
    // Create test campaigns
    global $wpdb;
    $campaigns_table = $wpdb->prefix . 'fs_campaigns';
    
    // Test flash sale campaign
    $flash_sale_data = [
        'name' => 'Test Flash Sale',
        'type' => 'flash-sale',
        'status' => 1,
        'priority' => 10,
        'start_date' => date('Y-m-d H:i:s'),
        'end_date' => date('Y-m-d H:i:s', strtotime('+1 day')),
        'created_at' => current_time('mysql')
    ];
    
    $wpdb->insert($campaigns_table, $flash_sale_data);
    $flash_sale_id = $wpdb->insert_id;
    
    // Test quantity promotion campaign
    $quantity_promo_data = [
        'name' => 'Test Quantity Promotion',
        'type' => 'quantity-promotion',
        'status' => 1,
        'priority' => 10,
        'start_date' => date('Y-m-d H:i:s'),
        'end_date' => date('Y-m-d H:i:s', strtotime('+1 day')),
        'created_at' => current_time('mysql')
    ];
    
    $wpdb->insert($campaigns_table, $quantity_promo_data);
    $quantity_promo_id = $wpdb->insert_id;
    
    echo "<p>✅ Created test campaigns:</p>";
    echo "<p>&nbsp;&nbsp;- Flash Sale ID: $flash_sale_id</p>";
    echo "<p>&nbsp;&nbsp;- Quantity Promotion ID: $quantity_promo_id</p>";
    
    // Step 3: Test core plugin filtering
    echo "<h2>Step 3: Core Plugin Campaign Filtering</h2>";
    
    if (class_exists('FS_Public')) {
        $fs_public = new FS_Public();
        
        // Test with a product that has both campaign types
        $test_product_id = 38; // Beanie with Logo
        
        // Simulate promotions data
        $mock_promotions = [
            [
                'campaign' => (object) [
                    'id' => $flash_sale_id,
                    'name' => 'Test Flash Sale',
                    'type' => 'flash-sale',
                    'priority' => 10
                ],
                'product_data' => (object) [
                    'discount_type' => 1,
                    'discount_value' => 20
                ]
            ],
            [
                'campaign' => (object) [
                    'id' => $quantity_promo_id,
                    'name' => 'Test Quantity Promotion',
                    'type' => 'quantity-promotion',
                    'priority' => 10
                ],
                'product_data' => (object) [
                    'discount_type' => 1,
                    'discount_value' => 15
                ]
            ]
        ];
        
        // Test filtering in display_promotion_info
        $flash_sale_promotions = array_filter($mock_promotions, function($promotion) {
            return $promotion['campaign']->type !== 'quantity-promotion';
        });
        
        echo "<p>Original promotions count: " . count($mock_promotions) . "</p>";
        echo "<p>Flash sale promotions count: " . count($flash_sale_promotions) . "</p>";
        
        if (count($flash_sale_promotions) === 1) {
            echo "<p style='color: green;'>✅ Core plugin correctly filters out quantity promotions</p>";
        } else {
            echo "<p style='color: red;'>❌ Core plugin filtering not working correctly</p>";
        }
        
        // Test progress bar filtering
        $flash_sale_campaign = $flash_sale_promotions[0]['campaign'];
        $quantity_promo_campaign = $mock_promotions[1]['campaign'];
        
        $should_show_progress_flash = $flash_sale_campaign->type !== 'quantity-promotion';
        $should_show_progress_quantity = $quantity_promo_campaign->type !== 'quantity-promotion';
        
        echo "<p>Flash sale should show progress bar: " . ($should_show_progress_flash ? 'Yes' : 'No') . "</p>";
        echo "<p>Quantity promotion should show progress bar: " . ($should_show_progress_quantity ? 'Yes' : 'No') . "</p>";
        
        if ($should_show_progress_flash && !$should_show_progress_quantity) {
            echo "<p style='color: green;'>✅ Progress bar filtering works correctly</p>";
        } else {
            echo "<p style='color: red;'>❌ Progress bar filtering not working correctly</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ FS_Public class not found</p>";
    }
    
    // Step 4: Test cart item modification flags
    echo "<h2>Step 4: Cart Item Modification Flags</h2>";
    
    // Simulate cart item with quantity promotion applied
    $mock_cart_item = [
        'product_id' => 38,
        'quantity' => 5,
        'fsqp_discount_applied' => true,
        'fs_addon_modified' => true
    ];
    
    $should_skip_core = isset($mock_cart_item['fs_addon_modified']) && $mock_cart_item['fs_addon_modified'];
    
    echo "<p>Cart item has addon modification flag: " . ($should_skip_core ? 'Yes' : 'No') . "</p>";
    
    if ($should_skip_core) {
        echo "<p style='color: green;'>✅ Core plugin will skip this cart item</p>";
    } else {
        echo "<p style='color: red;'>❌ Core plugin will not skip this cart item</p>";
    }
    
    // Step 5: Test priority execution order
    echo "<h2>Step 5: Priority Execution Order</h2>";
    
    $test_priorities = [
        'Quantity Promotion Addon' => 3,
        'Core Plugin Flash Sale' => 5,
        'WooCommerce Core' => 10
    ];
    
    echo "<p>Expected execution order (lower priority runs first):</p>";
    asort($test_priorities);
    $order = 1;
    foreach ($test_priorities as $plugin => $priority) {
        echo "<p>$order. $plugin (Priority: $priority)</p>";
        $order++;
    }
    
    if ($test_priorities['Quantity Promotion Addon'] < $test_priorities['Core Plugin Flash Sale']) {
        echo "<p style='color: green;'>✅ Quantity promotion will run before core plugin</p>";
    } else {
        echo "<p style='color: red;'>❌ Priority order is incorrect</p>";
    }
    
    // Cleanup
    echo "<h2>Cleanup</h2>";
    if (isset($_GET['cleanup']) && $_GET['cleanup'] === '1') {
        $wpdb->delete($campaigns_table, ['id' => $flash_sale_id]);
        $wpdb->delete($campaigns_table, ['id' => $quantity_promo_id]);
        echo "<p style='color: blue;'>🧹 Test campaigns cleaned up</p>";
    } else {
        echo "<p><a href='?page=test-plugin-conflict&cleanup=1' class='button'>Clean Up Test Data</a></p>";
    }
    
    echo "<h2>Summary</h2>";
    echo "<p><strong>Key Changes Made:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Quantity promotion addon hooks run at priority 3 (before core plugin priority 5)</li>";
    echo "<li>✅ Core plugin filters out quantity-promotion campaigns from display and pricing</li>";
    echo "<li>✅ Progress bars only show for flash sale campaigns, not quantity promotions</li>";
    echo "<li>✅ Cart items modified by quantity promotion are marked to prevent core override</li>";
    echo "<li>✅ Quantity promotion displays at priority 26 (after core plugin priority 25)</li>";
    echo "</ul>";
}

// Add admin page
add_action('admin_menu', function() {
    if (current_user_can('manage_options')) {
        add_submenu_page(
            null,
            'Test Plugin Conflict',
            'Test Plugin Conflict',
            'manage_options',
            'test-plugin-conflict',
            'test_plugin_conflict_resolution'
        );
    }
});

// Add quick access
add_action('admin_notices', function() {
    if (current_user_can('manage_options') && 
        (strpos($_SERVER['REQUEST_URI'], 'flashsale') !== false || 
         strpos($_SERVER['REQUEST_URI'], 'campaign') !== false)) {
        echo '<div class="notice notice-warning"><p>';
        echo '<strong>Plugin Conflict Test:</strong> ';
        echo '<a href="?page=test-plugin-conflict" target="_blank" class="button button-small">Test Conflict Resolution</a>';
        echo '</p></div>';
    }
});
