<?php
/**
 * Test file for the two core plugin fixes:
 * 1. Campaign update issue - products not being removed properly
 * 2. Variable product support in flash sale search and display
 *
 * @package FlashSale_Core
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test Fix 1: Campaign Update Logic
 */
function test_campaign_update_logic() {
    echo "<h2>Test 1: Campaign Update Logic</h2>";
    
    // Check if the flash sale manager class exists
    $manager_exists = class_exists('FS_Flash_Sale_Manager');
    echo "<p><strong>FS_Flash_Sale_Manager class exists:</strong> " . ($manager_exists ? '✅ Yes' : '❌ No') . "</p>";
    
    if ($manager_exists) {
        // Check if the update_campaign method exists
        $update_method_exists = method_exists('FS_Flash_Sale_Manager', 'update_campaign');
        echo "<p><strong>update_campaign method exists:</strong> " . ($update_method_exists ? '✅ Yes' : '❌ No') . "</p>";
        
        // Check if the method uses transactions
        $reflection = new ReflectionMethod('FS_Flash_Sale_Manager', 'update_campaign');
        $method_content = file_get_contents($reflection->getFileName());
        $has_transaction = strpos($method_content, 'START TRANSACTION') !== false;
        $has_clear_products = strpos($method_content, 'DELETE FROM') !== false;
        
        echo "<p><strong>Uses database transactions:</strong> " . ($has_transaction ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Clears existing products before insert:</strong> " . ($has_clear_products ? '✅ Yes' : '❌ No') . "</p>";
        
        if ($update_method_exists && $has_transaction && $has_clear_products) {
            echo "<p style='color: green;'>✅ Campaign update logic should be working correctly!</p>";
            echo "<p><em>The new logic clears all existing products and re-inserts them fresh, preventing orphaned products.</em></p>";
        } else {
            echo "<p style='color: red;'>❌ Campaign update logic has issues.</p>";
        }
    }
}

/**
 * Test Fix 2: Variable Product Support
 */
function test_variable_product_support() {
    echo "<h2>Test 2: Variable Product Support</h2>";
    
    // Check if the flash sale manager class exists
    $manager_exists = class_exists('FS_Flash_Sale_Manager');
    echo "<p><strong>FS_Flash_Sale_Manager class exists:</strong> " . ($manager_exists ? '✅ Yes' : '❌ No') . "</p>";
    
    if ($manager_exists) {
        // Check if the ajax_search_products method exists
        $search_method_exists = method_exists('FS_Flash_Sale_Manager', 'ajax_search_products');
        echo "<p><strong>ajax_search_products method exists:</strong> " . ($search_method_exists ? '✅ Yes' : '❌ No') . "</p>";
        
        // Check if the format_variation_attributes method exists
        $format_method_exists = method_exists('FS_Flash_Sale_Manager', 'format_variation_attributes');
        echo "<p><strong>format_variation_attributes method exists:</strong> " . ($format_method_exists ? '✅ Yes' : '❌ No') . "</p>";
        
        if ($search_method_exists) {
            // Check if the search method includes product_variation
            $reflection = new ReflectionMethod('FS_Flash_Sale_Manager', 'ajax_search_products');
            $method_content = file_get_contents($reflection->getFileName());
            $includes_variations = strpos($method_content, 'product_variation') !== false;
            $has_variation_logic = strpos($method_content, 'is_variation') !== false;
            $has_total_sold = strpos($method_content, 'total_sold') !== false;
            
            echo "<p><strong>Searches product variations:</strong> " . ($includes_variations ? '✅ Yes' : '❌ No') . "</p>";
            echo "<p><strong>Has variation detection logic:</strong> " . ($has_variation_logic ? '✅ Yes' : '❌ No') . "</p>";
            echo "<p><strong>Calculates total sold for variable products:</strong> " . ($has_total_sold ? '✅ Yes' : '❌ No') . "</p>";
            
            if ($includes_variations && $has_variation_logic && $has_total_sold && $format_method_exists) {
                echo "<p style='color: green;'>✅ Variable product support should be working!</p>";
                echo "<p><em>Search now includes variations with enhanced display and total sold calculation.</em></p>";
            } else {
                echo "<p style='color: red;'>❌ Variable product support has issues.</p>";
            }
        }
    }
    
    // Check JavaScript enhancements
    $flash_sale_file = plugin_dir_path(__FILE__) . 'admin/views/campaign-types/flash-sale.php';
    if (file_exists($flash_sale_file)) {
        $content = file_get_contents($flash_sale_file);
        
        $has_type_badges = strpos($content, 'fs-product-type') !== false;
        $has_variation_display = strpos($content, 'is_variation') !== false;
        $has_sold_info = strpos($content, 'total_sold') !== false;
        $has_clear_button = strpos($content, 'fs-datetime-clear') !== false;
        
        echo "<p><strong>JavaScript has product type badges:</strong> " . ($has_type_badges ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>JavaScript has variation display logic:</strong> " . ($has_variation_display ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>JavaScript shows sold information:</strong> " . ($has_sold_info ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>JavaScript has clear button for time range:</strong> " . ($has_clear_button ? '✅ Yes' : '❌ No') . "</p>";
        
        if ($has_type_badges && $has_variation_display && $has_sold_info && $has_clear_button) {
            echo "<p style='color: green;'>✅ JavaScript enhancements are in place!</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Some JavaScript enhancements may be missing.</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Flash sale form file not found.</p>";
    }
}

/**
 * Test CSS Styling
 */
function test_css_styling() {
    echo "<h2>Test 3: CSS Styling</h2>";
    
    $css_file = plugin_dir_path(__FILE__) . 'admin/css/admin.css';
    if (file_exists($css_file)) {
        $content = file_get_contents($css_file);
        
        $has_product_type_styles = strpos($content, '.fs-product-type') !== false;
        $has_variation_styles = strpos($content, '.fs-variation') !== false;
        $has_variable_styles = strpos($content, '.fs-variable') !== false;
        $has_clear_button_styles = strpos($content, '.fs-btn-warning') !== false;
        
        echo "<p><strong>CSS has product type styles:</strong> " . ($has_product_type_styles ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>CSS has variation badge styles:</strong> " . ($has_variation_styles ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>CSS has variable product styles:</strong> " . ($has_variable_styles ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>CSS has clear button styles:</strong> " . ($has_clear_button_styles ? '✅ Yes' : '❌ No') . "</p>";
        
        if ($has_product_type_styles && $has_variation_styles && $has_variable_styles && $has_clear_button_styles) {
            echo "<p style='color: green;'>✅ CSS styling is complete!</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Some CSS styles may be missing.</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Admin CSS file not found.</p>";
    }
}

/**
 * Test Summary
 */
function test_summary() {
    echo "<h2>Test Summary</h2>";
    
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Fixes Implemented:</h3>";
    echo "<ol>";
    echo "<li><strong>Campaign Update Logic:</strong>";
    echo "<ul>";
    echo "<li>Fixed product removal issue by using clear-and-insert strategy</li>";
    echo "<li>Added database transactions for data integrity</li>";
    echo "<li>Improved error handling and logging</li>";
    echo "</ul></li>";
    
    echo "<li><strong>Variable Product Support:</strong>";
    echo "<ul>";
    echo "<li>Enhanced search to include product variations</li>";
    echo "<li>Added variation attribute formatting for better display</li>";
    echo "<li>Implemented total sold calculation for variable products</li>";
    echo "<li>Added product type badges (Variable/Variation)</li>";
    echo "</ul></li>";
    
    echo "<li><strong>Time Range Clear Functionality:</strong>";
    echo "<ul>";
    echo "<li>Added clear button to datetime modal</li>";
    echo "<li>Implemented clear functionality to reset time range</li>";
    echo "<li>Added appropriate styling for the clear button</li>";
    echo "</ul></li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Testing Instructions:</h3>";
    echo "<ol>";
    echo "<li><strong>Campaign Update:</strong> Create a flash sale campaign, add products, save, then remove some products and save again. Verify removed products don't appear.</li>";
    echo "<li><strong>Variable Products:</strong> Search for variable products in flash sale campaign creation. Verify variations appear with proper labels and sold quantities.</li>";
    echo "<li><strong>Time Range Clear:</strong> Set a time range for a product, then click the clear button to verify it resets.</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Key Improvements:</h3>";
    echo "<ul>";
    echo "<li>🔧 <strong>Database Integrity:</strong> Transactions prevent partial updates</li>";
    echo "<li>🎯 <strong>Variable Product Support:</strong> Full support for WooCommerce variable products</li>";
    echo "<li>📊 <strong>Enhanced Display:</strong> Better product information with type badges</li>";
    echo "<li>🧹 <strong>User Experience:</strong> Clear button for easy time range reset</li>";
    echo "<li>🎨 <strong>Visual Improvements:</strong> Color-coded product types and better styling</li>";
    echo "</ul>";
    echo "</div>";
}

// Run all tests
echo "<h1>FlashSale Core Plugin - Two Fixes Test</h1>";
test_campaign_update_logic();
test_variable_product_support();
test_css_styling();
test_summary();
