<?php
/**
 * Test file for the three fixes:
 * 1. Delete products works but save doesn't work
 * 2. Select Time Range can't be cleared  
 * 3. Progress bar needs to work for variable products
 *
 * @package CanhcamPromotion_Quantity_Promotion
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test Fix 1: Delete and Save Functionality
 */
function test_delete_and_save_functionality() {
    echo "<h2>Test 1: Delete and Save Functionality</h2>";
    
    // Check if AJAX handler is properly registered
    $ajax_class_exists = class_exists('FSQP_Ajax');
    echo "<p><strong>FSQP_Ajax class exists:</strong> " . ($ajax_class_exists ? '✅ Yes' : '❌ No') . "</p>";
    
    if ($ajax_class_exists) {
        // Check if the remove_campaign_product method exists
        $method_exists = method_exists('FSQP_Ajax', 'remove_campaign_product');
        echo "<p><strong>remove_campaign_product method exists:</strong> " . ($method_exists ? '✅ Yes' : '❌ No') . "</p>";
        
        // Check if the handle_fs_admin_action method exists
        $admin_handler_exists = method_exists('FSQP_Ajax', 'handle_fs_admin_action');
        echo "<p><strong>handle_fs_admin_action method exists:</strong> " . ($admin_handler_exists ? '✅ Yes' : '❌ No') . "</p>";
    }
    
    // Check if campaign handler save functionality is working
    $campaign_handler_exists = class_exists('FSQP_Campaign_Handler');
    echo "<p><strong>FSQP_Campaign_Handler class exists:</strong> " . ($campaign_handler_exists ? '✅ Yes' : '❌ No') . "</p>";
    
    if ($campaign_handler_exists) {
        $save_method_exists = method_exists('FSQP_Campaign_Handler', 'save_quantity_promotion_data');
        echo "<p><strong>save_quantity_promotion_data method exists:</strong> " . ($save_method_exists ? '✅ Yes' : '❌ No') . "</p>";
    }
    
    // Check if database save method exists
    $database_class_exists = class_exists('FSQP_Database');
    echo "<p><strong>FSQP_Database class exists:</strong> " . ($database_class_exists ? '✅ Yes' : '❌ No') . "</p>";
    
    if ($database_class_exists) {
        $save_rule_method_exists = method_exists('FSQP_Database', 'save_quantity_rule');
        echo "<p><strong>save_quantity_rule method exists:</strong> " . ($save_rule_method_exists ? '✅ Yes' : '❌ No') . "</p>";
    }
    
    // Check if the JavaScript has been updated with AJAX functionality
    $campaign_form_file = plugin_dir_path(__FILE__) . 'admin/views/campaign-form.php';
    if (file_exists($campaign_form_file)) {
        $content = file_get_contents($campaign_form_file);
        $has_ajax_remove = strpos($content, 'fs_admin_action') !== false;
        $has_confirmation = strpos($content, 'confirm(') !== false;
        
        echo "<p><strong>JavaScript AJAX remove functionality:</strong> " . ($has_ajax_remove ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>JavaScript confirmation dialog:</strong> " . ($has_confirmation ? '✅ Yes' : '❌ No') . "</p>";
    } else {
        echo "<p><strong>Campaign form file:</strong> ❌ Not found</p>";
    }
    
    if ($ajax_class_exists && $campaign_handler_exists && $database_class_exists) {
        echo "<p style='color: green;'>✅ Delete and Save functionality should be working!</p>";
    } else {
        echo "<p style='color: red;'>❌ Delete and Save functionality has issues.</p>";
    }
}

/**
 * Test Fix 2: Time Range Clear Functionality
 */
function test_time_range_clear_functionality() {
    echo "<h2>Test 2: Time Range Clear Functionality</h2>";
    
    echo "<p><strong>Note:</strong> Time range functionality is in the core plugin (FlashSale Core), not the quantity promotion plugin.</p>";
    echo "<p>The quantity promotion plugin doesn't have time range functionality - it only has quantity ranges.</p>";
    echo "<p>If you're seeing time range issues, they would be in the core plugin's flash-sale.php form.</p>";
    
    // Check if this is about the core plugin
    $core_plugin_active = function_exists('FS');
    echo "<p><strong>FlashSale Core plugin active:</strong> " . ($core_plugin_active ? '✅ Yes' : '❌ No') . "</p>";
    
    if ($core_plugin_active) {
        echo "<p style='color: blue;'>ℹ️ Time range clear functionality needs to be implemented in the core plugin.</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ FlashSale Core plugin is not active.</p>";
    }
}

/**
 * Test Fix 3: Variable Product Progress Bar
 */
function test_variable_product_progress_bar() {
    echo "<h2>Test 3: Variable Product Progress Bar</h2>";
    
    // Check if frontend JavaScript has been updated
    $frontend_js_file = plugin_dir_path(__FILE__) . 'public/js/frontend.js';
    
    if (file_exists($frontend_js_file)) {
        $content = file_get_contents($frontend_js_file);
        
        // Check for variable product support functions
        $has_variable_support = strpos($content, 'initVariableProductSupport') !== false;
        $has_variation_change = strpos($content, 'handleVariationChange') !== false;
        $has_progress_bar_init = strpos($content, 'initVariableProgressBars') !== false;
        $has_show_variation_progress = strpos($content, 'showVariationProgressBar') !== false;
        $has_reinitialize_progress = strpos($content, 'reinitializeProgressBar') !== false;
        
        echo "<p><strong>Variable product support initialization:</strong> " . ($has_variable_support ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Variation change handler:</strong> " . ($has_variation_change ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Progress bar initialization:</strong> " . ($has_progress_bar_init ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Show variation progress bar:</strong> " . ($has_show_variation_progress ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Reinitialize progress bar:</strong> " . ($has_reinitialize_progress ? '✅ Yes' : '❌ No') . "</p>";
        
        // Check for WooCommerce variation form event handlers
        $has_found_variation = strpos($content, 'found_variation') !== false;
        $has_reset_data = strpos($content, 'reset_data') !== false;
        
        echo "<p><strong>WooCommerce found_variation event:</strong> " . ($has_found_variation ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>WooCommerce reset_data event:</strong> " . ($has_reset_data ? '✅ Yes' : '❌ No') . "</p>";
        
        if ($has_variable_support && $has_variation_change && $has_progress_bar_init && $has_found_variation) {
            echo "<p style='color: green;'>✅ Variable product progress bar functionality should be working!</p>";
        } else {
            echo "<p style='color: red;'>❌ Variable product progress bar functionality has issues.</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Frontend JavaScript file not found.</p>";
    }
}

/**
 * Test Summary
 */
function test_summary() {
    echo "<h2>Test Summary</h2>";
    
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Fixes Implemented:</h3>";
    echo "<ol>";
    echo "<li><strong>Delete and Save Functionality:</strong>";
    echo "<ul>";
    echo "<li>Added AJAX handler for removing products from campaigns</li>";
    echo "<li>Updated JavaScript to use AJAX calls with confirmation dialogs</li>";
    echo "<li>Enhanced error handling and user feedback</li>";
    echo "</ul></li>";
    
    echo "<li><strong>Time Range Clear Functionality:</strong>";
    echo "<ul>";
    echo "<li>Note: This is a core plugin issue, not quantity promotion plugin</li>";
    echo "<li>Time range functionality is in admin/views/campaign-types/flash-sale.php</li>";
    echo "<li>Would need to add clear button to the datetime modal</li>";
    echo "</ul></li>";
    
    echo "<li><strong>Variable Product Progress Bar:</strong>";
    echo "<ul>";
    echo "<li>Added comprehensive variable product support to frontend.js</li>";
    echo "<li>Handles variation changes and progress bar visibility</li>";
    echo "<li>Reinitializes progress bar animations for variations</li>";
    echo "<li>Manages promotion displays for variable products</li>";
    echo "</ul></li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Testing Instructions:</h3>";
    echo "<ol>";
    echo "<li>Test product deletion: Try removing products from a quantity promotion campaign</li>";
    echo "<li>Test campaign saving: Add products with quantity ranges and save the campaign</li>";
    echo "<li>Test variable products: Visit a variable product page and change variations to see if progress bars update</li>";
    echo "</ol>";
    echo "</div>";
}

// Run all tests
echo "<h1>Quantity Promotion Plugin - Three Fixes Test</h1>";
test_delete_and_save_functionality();
test_time_range_clear_functionality();
test_variable_product_progress_bar();
test_summary();
