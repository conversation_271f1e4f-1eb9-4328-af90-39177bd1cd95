<?php
/**
 * Debug script to check hooks and save functionality
 * Add this to wp-config.php or run in WordPress admin context
 */

// Add debug logging for hooks
add_action('fs_save_campaign', function($campaign_id, $campaign_data, $form_data) {
    error_log("DEBUG: fs_save_campaign hook triggered");
    error_log("Campaign ID: " . $campaign_id);
    error_log("Campaign Type: " . ($campaign_data['type'] ?? 'not set'));
    error_log("Form Data Products: " . (isset($form_data['products']) ? count($form_data['products']) : 'not set'));
    
    if (isset($form_data['products'])) {
        foreach ($form_data['products'] as $product_id => $product_data) {
            error_log("Product {$product_id} has " . (isset($product_data['ranges']) ? count($product_data['ranges']) : 0) . " ranges");
        }
    }
}, 5, 3);

// Add debug logging for quantity promotion handler
add_action('init', function() {
    if (class_exists('FSQP_Campaign_Handler')) {
        error_log("DEBUG: FSQP_Campaign_Handler class exists");
        
        // Check if hooks are registered
        global $wp_filter;
        if (isset($wp_filter['fs_save_campaign'])) {
            error_log("DEBUG: fs_save_campaign hook has " . count($wp_filter['fs_save_campaign']->callbacks) . " priority levels");
            
            foreach ($wp_filter['fs_save_campaign']->callbacks as $priority => $callbacks) {
                error_log("Priority {$priority}: " . count($callbacks) . " callbacks");
                foreach ($callbacks as $callback) {
                    if (is_array($callback['function']) && $callback['function'][0] instanceof FSQP_Campaign_Handler) {
                        error_log("Found FSQP_Campaign_Handler callback at priority {$priority}");
                    }
                }
            }
        } else {
            error_log("DEBUG: fs_save_campaign hook not registered");
        }
    } else {
        error_log("DEBUG: FSQP_Campaign_Handler class does not exist");
    }
}, 999);

// Test function to manually trigger save
function test_quantity_promotion_save() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    error_log("=== MANUAL TEST: Quantity Promotion Save ===");
    
    // Test data
    $campaign_data = [
        'type' => 'quantity-promotion',
        'name' => 'Test Campaign'
    ];
    
    $form_data = [
        'products' => [
            '38' => [
                'product_id' => 38,
                'ranges' => [
                    [
                        'min_quantity' => 1,
                        'max_quantity' => 5,
                        'discount_type' => 1,
                        'discount_value' => 5,
                        'max_discount_amount' => 0
                    ]
                ]
            ]
        ]
    ];
    
    // Trigger the hook manually
    do_action('fs_save_campaign', 999, $campaign_data, $form_data);
    
    error_log("=== END MANUAL TEST ===");
}

// Add admin action to test
add_action('wp_ajax_test_quantity_save', 'test_quantity_promotion_save');

// Add debug info to admin footer
add_action('admin_footer', function() {
    if (current_user_can('manage_options')) {
        ?>
        <script>
        console.log('=== Quantity Promotion Debug Info ===');
        console.log('FSQP_Campaign_Handler exists:', typeof FSQP_Campaign_Handler !== 'undefined');
        
        // Add test button
        if (window.location.href.includes('flashsale') || window.location.href.includes('campaign')) {
            const testBtn = document.createElement('button');
            testBtn.textContent = 'Test QP Save';
            testBtn.style.position = 'fixed';
            testBtn.style.top = '50px';
            testBtn.style.right = '20px';
            testBtn.style.zIndex = '9999';
            testBtn.style.background = '#0073aa';
            testBtn.style.color = 'white';
            testBtn.style.border = 'none';
            testBtn.style.padding = '10px';
            testBtn.style.borderRadius = '3px';
            testBtn.onclick = function() {
                fetch(ajaxurl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=test_quantity_save'
                }).then(response => {
                    console.log('Test completed, check error log');
                    alert('Test completed, check error log');
                });
            };
            document.body.appendChild(testBtn);
        }
        </script>
        <?php
    }
});

// Check database structure
add_action('admin_init', function() {
    if (isset($_GET['debug_fsqp_db']) && current_user_can('manage_options')) {
        global $wpdb;
        
        echo "<h2>FlashSale Quantity Promotion Database Debug</h2>";
        
        // Check tables
        $tables = [
            'fs_campaigns' => $wpdb->prefix . 'fs_campaigns',
            'fs_campaign_products' => $wpdb->prefix . 'fs_campaign_products'
        ];
        
        foreach ($tables as $name => $table) {
            $exists = $wpdb->get_var("SHOW TABLES LIKE '$table'") === $table;
            echo "<p><strong>{$name}:</strong> " . ($exists ? "✓ EXISTS" : "✗ MISSING") . "</p>";
            
            if ($exists) {
                $count = $wpdb->get_var("SELECT COUNT(*) FROM $table");
                echo "<p>Records: {$count}</p>";
                
                if ($name === 'fs_campaign_products') {
                    $qp_count = $wpdb->get_var("
                        SELECT COUNT(*) 
                        FROM $table p 
                        INNER JOIN {$wpdb->prefix}fs_campaigns c ON p.promotion_id = c.id 
                        WHERE c.type = 'quantity-promotion'
                    ");
                    echo "<p>Quantity Promotion Records: {$qp_count}</p>";
                }
            }
        }
        
        // Check recent campaigns
        $recent_campaigns = $wpdb->get_results("
            SELECT id, name, type, status, created_at 
            FROM {$wpdb->prefix}fs_campaigns 
            ORDER BY created_at DESC 
            LIMIT 5
        ");
        
        echo "<h3>Recent Campaigns:</h3>";
        foreach ($recent_campaigns as $campaign) {
            echo "<p>ID: {$campaign->id}, Name: {$campaign->name}, Type: {$campaign->type}, Status: {$campaign->status}</p>";
        }
        
        exit;
    }
});

// Add debug link to admin menu
add_action('admin_menu', function() {
    if (current_user_can('manage_options')) {
        add_submenu_page(
            null, // No parent menu
            'FSQP Debug',
            'FSQP Debug',
            'manage_options',
            'fsqp-debug',
            function() {
                echo '<div class="wrap">';
                echo '<h1>FlashSale Quantity Promotion Debug</h1>';
                echo '<p><a href="?debug_fsqp_db=1" class="button">Check Database</a></p>';
                echo '<p><a href="#" onclick="testQuantitySave()" class="button">Test Save Function</a></p>';
                echo '<script>
                function testQuantitySave() {
                    fetch(ajaxurl, {
                        method: "POST",
                        headers: {"Content-Type": "application/x-www-form-urlencoded"},
                        body: "action=test_quantity_save"
                    }).then(() => {
                        alert("Test completed, check error log");
                    });
                }
                </script>';
                echo '</div>';
            }
        );
    }
});

// Log when quantity promotion addon is loaded
add_action('plugins_loaded', function() {
    if (class_exists('FlashSale_Quantity_Promotion')) {
        error_log("DEBUG: FlashSale_Quantity_Promotion class loaded");
    }
}, 999);

add_action('flashsale_core_loaded', function() {
    error_log("DEBUG: flashsale_core_loaded action triggered");
});

add_action('canhcampromotion_core_loaded', function() {
    error_log("DEBUG: canhcampromotion_core_loaded action triggered");
});
