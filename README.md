# FlashSale Core - Flash Sale Management System

Hệ thống quản lý Flash Sale chuyên nghiệp với kiến trúc addon mở rộng cho WordPress/WooCommerce.

## Tính năng chính

### 🎯 Core Plugin - Flash Sale Focus
- **Flash Sale Manager**: <PERSON>u<PERSON>n lý các chương trình flash sale với thời gian giới hạn
- **Product Configuration**: Cấu hình giá khuyến mãi, số lượng giới hạn cho từng sản phẩm
- **Time-based Campaigns**: Hệ thống chiến dịch theo thời gian thực
- **Stock Management**: Quản lý số lượng bán và giới hạn mua
- **Addon Manager**: Quản lý các addon mở rộng
- **API Framework**: REST API đầy đủ cho tích hợp
- **Legacy Support**: <PERSON>ư<PERSON><PERSON> thích ngược với plugin cũ

### 🔌 Addon System
**Lưu ý**: Plugin tập trung vào Flash Sale. Các loại khuyến mãi khác đượ<PERSON> hỗ trợ qua addon riêng biệt.

Các addon khuyến mãi mở rộng:
- **Product Promotion Addon**: Khuyến mãi theo sản phẩm
- **Category Promotion Addon**: Khuyến mãi theo danh mục
- **Quantity Promotion Addon**: Khuyến mãi theo số lượng
- **Order Promotion Addon**: Khuyến mãi theo đơn hàng
- **Combo Promotion Addon**: Khuyến mãi combo sản phẩm
- **Shipping Promotion Addon**: Khuyến mãi phí ship
- **Upsell Promotion Addon**: Khuyến mãi upsell
- **Gift Promotion Addon**: Khuyến mãi quà tặng

## Cài đặt

### Yêu cầu hệ thống
- WordPress 5.0+
- WooCommerce 3.0+
- PHP 7.4+
- MySQL 5.6+

### Cài đặt Plugin
1. Upload thư mục `flashsale-core` vào `/wp-content/plugins/`
2. Kích hoạt plugin trong WordPress Admin
3. Truy cập **FlashSale** trong menu admin

### Migration từ plugin cũ
Plugin sẽ tự động migrate dữ liệu từ schema cũ sang schema mới khi kích hoạt.

## Cấu trúc Database

### Bảng chính
- `fs_campaigns`: Các chiến dịch khuyến mãi
- `fs_campaign_rules`: Rules cho campaigns
- `fs_campaign_targets`: Targets (products/categories)
- `fs_campaign_gifts`: Quà tặng
- `fs_campaign_combos`: Combo sản phẩm
- `fs_campaign_geography`: Targeting địa lý
- `fs_campaign_stats`: Thống kê
- `fs_addons`: Quản lý addons
- `fs_addon_hooks`: Hooks của addons

## Sử dụng

### Tạo Flash Sale Campaign mới
```php
$campaign_data = [
    'name' => 'Flash Sale Tết 2024',
    'description' => 'Flash sale đặc biệt dịp Tết',
    'type' => 'flash-sale',
    'priority' => 10,
    'status' => 1,
    'start_date' => '2024-01-01 00:00:00',
    'end_date' => '2024-01-31 23:59:59',
    'global_settings' => [],
    'targets' => [
        [
            'target_type' => 'product',
            'target_id' => 123,
            'target_name' => 'Sản phẩm A',
            'pricing_rules' => [
                'discount_type' => 'percentage',
                'discount_value' => 20,
                'max_discount_amount' => 100000,
                'max_quantity' => 100,
                'sold_quantity' => 0
            ]
        ]
    ]
];

$campaign_id = FS()->campaigns()->create_campaign($campaign_data);
```

### Shortcode hiển thị campaign
```php
[flashsale_campaign id="123" limit="10" columns="4"]
```

### API Endpoints
```
GET /wp-json/flashsale/v1/campaigns
POST /wp-json/flashsale/v1/campaigns
GET /wp-json/flashsale/v1/campaigns/{id}
PUT /wp-json/flashsale/v1/campaigns/{id}
DELETE /wp-json/flashsale/v1/campaigns/{id}
```

## Phát triển Addon

### Cấu trúc Addon
```
addon-name/
├── addon-name.php
├── includes/
│   ├── class-addon-main.php
│   ├── class-addon-admin.php
│   └── class-addon-public.php
├── admin/
│   └── views/
└── assets/
```

### Tạo Addon mới
```php
class My_Custom_Addon {
    
    const ADDON_SLUG = 'my-custom-addon';
    
    public function __construct() {
        add_action('flashsale_core_loaded', [$this, 'init']);
    }
    
    public function init() {
        // Register addon
        FS()->addons()->register_addon(self::ADDON_SLUG, [
            'name' => 'My Custom Addon',
            'version' => '1.0.0',
            'type' => 'promotion',
            'settings' => [],
            'dependencies' => []
        ]);
        
        // Register hooks
        FS()->addons()->register_addon_hook(self::ADDON_SLUG, [
            'hook_name' => 'woocommerce_get_price',
            'hook_type' => 'filter',
            'callback_class' => 'My_Custom_Addon_Public',
            'callback_method' => 'modify_price',
            'priority' => 10
        ]);
    }
}

new My_Custom_Addon();
```

## Hooks & Filters

### Actions
- `flashsale_core_init`: Khi core được khởi tạo
- `flashsale_core_loaded`: Khi core đã load xong
- `fs_campaign_created`: Khi campaign được tạo
- `fs_campaign_updated`: Khi campaign được cập nhật
- `fs_campaign_deleted`: Khi campaign bị xóa
- `fs_addon_activated`: Khi addon được kích hoạt
- `fs_addon_deactivated`: Khi addon bị vô hiệu hóa

### Filters
- `fs_rule_types`: Đăng ký rule types mới
- `fs_gift_types`: Đăng ký gift types mới
- `fs_evaluate_condition`: Custom condition evaluation
- `fs_execute_action`: Custom action execution

## Tùy chỉnh

### CSS Classes
- `.fs-promotion-info`: Container thông tin khuyến mãi
- `.fs-promotion-badge`: Badge khuyến mãi
- `.fs-countdown-timer`: Đếm ngược thời gian
- `.fs-stock-progress`: Thanh tiến độ bán hàng
- `.fs-promotion-gifts`: Danh sách quà tặng

### JavaScript Events
- `fs_countdown_expired`: Khi đếm ngược hết hạn
- `fs_promotion_applied`: Khi khuyến mãi được áp dụng
- `fs_gift_added`: Khi quà tặng được thêm

## Troubleshooting

### Lỗi thường gặp

1. **Plugin không kích hoạt được**
   - Kiểm tra WooCommerce đã cài đặt chưa
   - Kiểm tra phiên bản PHP >= 7.4

2. **Migration lỗi**
   - Backup database trước khi migration
   - Kiểm tra quyền database

3. **Addon không hoạt động**
   - Kiểm tra dependencies
   - Kiểm tra hook registration

### Debug Mode
```php
// Trong wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);

// Trong settings
update_option('fs_enable_debug', 1);
```

## Changelog

### Version 2.0.0
- Kiến trúc mới với addon system
- Database schema tối ưu
- API framework
- Legacy support
- Migration tool

## Support

- **Documentation**: [Link docs]
- **Issues**: [Link GitHub issues]
- **Email**: <EMAIL>

## License

GPL-2.0+ License
