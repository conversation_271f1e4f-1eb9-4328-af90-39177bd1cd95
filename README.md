# FlashSale Quantity Promotion

Addon plugin cho FlashSale Core - Tính năng khuyến mãi theo số lượng sản phẩm.

## M<PERSON> tả

Plugin này mở rộng FlashSale Core với tính năng khuyến mãi theo số lượng sản phẩm, cho phép thiết lập giảm giá theo từng khoảng số lượng mua.

### Tính năng chính

- **Khuyến mãi theo số lượng**: Thiết lập giảm % hoặc số tiền theo từng khoảng số lượng mua
- **Ví dụ**: S<PERSON>n phẩm A mua từ 2-5 sản phẩm được gi<PERSON> 15%, mua từ 6+ sản phẩm đượ<PERSON> giảm 25%
- **Hiển thị bảng giá**: <PERSON>h<PERSON>ch hàng thấy rõ mức giảm giá theo từng khoảng số lượng
- **Tính toán tự động**: <PERSON><PERSON><PERSON> tự động cập nhật khi thay đổi số lượng
- **Tích hợp giỏ hàng**: Hiển thị số tiền tiết kiệm trong giỏ hàng

## Yêu cầu hệ thống

- WordPress 5.0+
- WooCommerce 5.0+
- FlashSale Core v2.0+
- PHP 7.4+

## Cài đặt

1. Đảm bảo FlashSale Core đã được cài đặt và kích hoạt
2. Upload thư mục `flashsale-quantity-promotion` vào `/wp-content/plugins/`
3. Kích hoạt plugin trong WordPress Admin
4. Plugin sẽ tự động đăng ký với FlashSale Core

## Sử dụng

### Tạo campaign khuyến mãi theo số lượng

1. Vào **FlashSale Core > Campaigns**
2. Click **Add New Campaign**
3. Chọn **Campaign Type**: "Quantity Promotion"
4. Điền thông tin campaign cơ bản
5. Thiết lập **Quantity Rules**:
   - Chọn sản phẩm
   - Đặt số lượng tối thiểu và tối đa
   - Chọn loại giảm giá (% hoặc số tiền)
   - Nhập giá trị giảm giá
   - Đặt giới hạn giảm giá tối đa (tùy chọn)

### Ví dụ thiết lập

**Sản phẩm: Áo thun**
- Giá gốc: $20
- Rule 1: Mua 2-4 cái → Giảm 10% → $18/cái
- Rule 2: Mua 5-9 cái → Giảm 15% → $17/cái  
- Rule 3: Mua 10+ cái → Giảm 20% → $16/cái

### Global Settings

- **Apply to variations**: Áp dụng cho từng variation riêng biệt
- **Combine with other discounts**: Cho phép kết hợp với khuyến mãi khác
- **Show savings message**: Hiển thị thông báo tiết kiệm trong giỏ hàng

## Cấu trúc Database

Plugin này **sử dụng lại database của FlashSale Core** thay vì tạo tables mới:

### Sử dụng FlashSale Core Tables

#### `wp_fs_campaigns`
- Lưu campaign với `type = 'quantity-promotion'`
- `global_settings` chứa cấu hình quantity-specific

#### `wp_fs_campaign_products`
- Lưu quantity rules như products với mapping đặc biệt:
  - `qty_max` → `min_quantity` (số lượng tối thiểu)
  - `percent_sold_max` → `max_quantity` (số lượng tối đa)
  - `discount_type` → loại giảm giá (1=%, 2=fixed)
  - `discount_value` → giá trị giảm giá
  - `max_discount_amount` → giới hạn giảm giá tối đa

### Table mới: `wp_fsqp_promotion_logs`

```sql
CREATE TABLE wp_fsqp_promotion_logs (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    order_id bigint(20) unsigned NOT NULL,
    product_id bigint(20) unsigned NOT NULL,
    campaign_id bigint(20) unsigned NOT NULL,
    quantity int(11) NOT NULL,
    original_price decimal(10,2) NOT NULL,
    discounted_price decimal(10,2) NOT NULL,
    discount_amount decimal(10,2) NOT NULL,
    discount_type tinyint(1) NOT NULL,
    min_quantity int(11) NOT NULL,
    max_quantity int(11) NOT NULL DEFAULT 0,
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);
```

### Lợi ích của việc sử dụng lại database

- ✅ **Không tạo tables mới**: Tận dụng cấu trúc có sẵn
- ✅ **Tích hợp tốt**: Sử dụng campaign manager của FlashSale Core
- ✅ **Priority system**: Tự động kế thừa priority logic
- ✅ **Conflict detection**: Sử dụng validation có sẵn
- ✅ **Dễ maintain**: Ít database schema phải quản lý

## API Reference

### AJAX Endpoints

#### Admin
- `fsqp_search_products` - Tìm kiếm sản phẩm
- `fsqp_save_campaign` - Lưu campaign
- `fsqp_delete_rule` - Xóa rule
- `fsqp_get_product_info` - Lấy thông tin sản phẩm

#### Frontend  
- `fsqp_calculate_quantity_price` - Tính giá theo số lượng
- `fsqp_get_quantity_discounts` - Lấy danh sách giảm giá

### PHP Classes

#### `FSQP_Database`
Quản lý database operations (sử dụng FlashSale Core structure)

```php
// Lấy rules cho sản phẩm (từ fs_campaign_products)
$rules = $database->get_product_quantity_rules($product_id);

// Lấy rule áp dụng cho số lượng
$rule = $database->get_applicable_rule($product_id, $quantity);

// Lưu rule (vào fs_campaign_products)
$rule_id = $database->save_quantity_rule($data);
```

#### `FS_Campaign_Manager` (FlashSale Core)
Sử dụng campaign manager của FlashSale Core

```php
// Tạo campaign quantity-promotion
$campaign_manager = new FS_Campaign_Manager();
$campaign_id = $campaign_manager->create_campaign([
    'type' => 'quantity-promotion',
    'name' => 'Quantity Discount Campaign',
    // ... other data
]);
```

#### `FSQP_Public`
Xử lý logic frontend

```php
// Tính giá giảm theo số lượng
$discounted_price = $public->calculate_quantity_discount($product_id, $quantity, $original_price);
```

## Hooks & Filters

### Actions
- `fsqp_before_calculate_discount` - Trước khi tính giảm giá
- `fsqp_after_calculate_discount` - Sau khi tính giảm giá
- `fsqp_promotion_applied` - Khi áp dụng khuyến mãi

### Filters
- `fsqp_discount_amount` - Lọc số tiền giảm giá
- `fsqp_applicable_rules` - Lọc rules áp dụng
- `fsqp_display_savings_message` - Lọc thông báo tiết kiệm

## Customization

### Custom CSS

```css
/* Tùy chỉnh bảng giảm giá */
.fsqp-quantity-promotion-info {
    background: #your-color;
    border: 1px solid #your-border-color;
}

/* Tùy chỉnh thông báo tiết kiệm */
.fsqp-savings {
    background: #your-savings-color;
    color: #your-text-color;
}
```

### Custom JavaScript

```javascript
// Tùy chỉnh logic cập nhật giá
jQuery(document).on('fsqp_price_updated', function(event, data) {
    console.log('Price updated:', data);
    // Your custom logic here
});
```

## Troubleshooting

### Vấn đề thường gặp

1. **Giảm giá không áp dụng**
   - Kiểm tra campaign đang active
   - Kiểm tra thời gian campaign
   - Kiểm tra rules có đúng không

2. **Bảng giảm giá không hiển thị**
   - Kiểm tra sản phẩm có rules không
   - Kiểm tra CSS conflicts
   - Kiểm tra JavaScript errors

3. **Giá không cập nhật trong giỏ hàng**
   - Clear cache
   - Kiểm tra WooCommerce version
   - Kiểm tra conflicts với plugins khác

### Debug Mode

Thêm vào `wp-config.php`:

```php
define('FSQP_DEBUG', true);
```

## Changelog

### Version 1.0.0
- Initial release
- Quantity-based discount rules
- Frontend price calculation
- Admin interface
- Cart integration
- Logging system

## Support

Để được hỗ trợ, vui lòng:
1. Kiểm tra documentation
2. Tìm trong phần troubleshooting
3. Liên hệ support team

## License

GPL v2 or later
