<?php
/**
 * Test script để verify sold quantity update functionality
 * 
 * @package FlashSale_Core
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test sold quantity update functionality
 */
function test_sold_quantity_update() {
    echo "<h1>Test: Sold Quantity Update on Order Complete</h1>";
    
    global $wpdb;
    $products_table = $wpdb->prefix . 'fs_campaign_products';
    $campaigns_table = $wpdb->prefix . 'fs_campaigns';
    
    echo "<h2>1. Check Current Setup</h2>";
    
    // Check if tables exist
    $tables_exist = $wpdb->get_var("SHOW TABLES LIKE '$products_table'") === $products_table;
    echo "<p><strong>Campaign Products Table:</strong> " . ($tables_exist ? "✅ EXISTS" : "❌ MISSING") . "</p>";
    
    if (!$tables_exist) {
        echo "<p style='color: red;'>❌ Cannot test without campaign products table</p>";
        return;
    }
    
    // Check active campaigns
    $active_campaigns = $wpdb->get_results("
        SELECT c.id, c.name, c.type, c.status, COUNT(p.product_id) as product_count
        FROM $campaigns_table c
        LEFT JOIN $products_table p ON c.id = p.promotion_id
        WHERE c.status = 'active' AND c.type = 'flash-sale'
        GROUP BY c.id
        ORDER BY c.created_at DESC
        LIMIT 5
    ");
    
    echo "<h3>Active Flash Sale Campaigns:</h3>";
    if (empty($active_campaigns)) {
        echo "<p style='color: orange;'>⚠️ No active flash sale campaigns found</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Name</th><th>Type</th><th>Status</th><th>Products</th></tr>";
        foreach ($active_campaigns as $campaign) {
            echo "<tr>";
            echo "<td>{$campaign->id}</td>";
            echo "<td>{$campaign->name}</td>";
            echo "<td>{$campaign->type}</td>";
            echo "<td>{$campaign->status}</td>";
            echo "<td>{$campaign->product_count}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>2. Check Campaign Products with Sold Quantities</h2>";
    
    $campaign_products = $wpdb->get_results("
        SELECT p.promotion_id, p.product_id, p.sold, c.name as campaign_name, 
               wp.post_title as product_name
        FROM $products_table p
        INNER JOIN $campaigns_table c ON p.promotion_id = c.id
        LEFT JOIN {$wpdb->posts} wp ON p.product_id = wp.ID
        WHERE c.type = 'flash-sale' AND c.status = 'active'
        ORDER BY p.promotion_id, p.product_id
        LIMIT 10
    ");
    
    if (empty($campaign_products)) {
        echo "<p style='color: orange;'>⚠️ No products in active flash sale campaigns</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Campaign</th><th>Product</th><th>Current Sold</th><th>Actions</th></tr>";
        foreach ($campaign_products as $cp) {
            echo "<tr>";
            echo "<td>{$cp->campaign_name} (ID: {$cp->promotion_id})</td>";
            echo "<td>{$cp->product_name} (ID: {$cp->product_id})</td>";
            echo "<td><strong>{$cp->sold}</strong></td>";
            echo "<td>";
            echo "<button onclick='simulateOrder({$cp->product_id}, {$cp->promotion_id})' class='button button-small'>Simulate Order</button>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>3. Test Hooks Registration</h2>";
    
    global $wp_filter;
    $hooks_to_check = [
        'woocommerce_order_status_completed' => 'update_sold_quantity_on_order_complete',
        'woocommerce_payment_complete' => 'update_sold_quantity_on_payment_complete'
    ];
    
    foreach ($hooks_to_check as $hook => $method) {
        if (isset($wp_filter[$hook])) {
            $found = false;
            foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback) {
                    if (is_array($callback['function']) && 
                        $callback['function'][0] instanceof FS_Public &&
                        $callback['function'][1] === $method) {
                        $found = true;
                        echo "<p>✅ <strong>$hook</strong> → $method (priority: $priority)</p>";
                        break 2;
                    }
                }
            }
            if (!$found) {
                echo "<p>❌ <strong>$hook</strong> → $method not found</p>";
            }
        } else {
            echo "<p>❌ <strong>$hook</strong> not registered</p>";
        }
    }
    
    echo "<h2>4. Manual Test Functions</h2>";
    
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>Test Scenarios:</h3>";
    echo "<ol>";
    echo "<li><strong>Create Test Order:</strong> Create a WooCommerce order with flash sale products</li>";
    echo "<li><strong>Complete Order:</strong> Change order status to 'completed'</li>";
    echo "<li><strong>Check Sold Quantity:</strong> Verify sold quantity increased in database</li>";
    echo "<li><strong>Prevent Duplicate:</strong> Complete same order again, sold quantity should not increase</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>5. JavaScript Test Functions</h2>";
    ?>
    
    <script>
    function simulateOrder(productId, campaignId) {
        if (!confirm('Simulate an order for Product ID: ' + productId + ' in Campaign ID: ' + campaignId + '?')) {
            return;
        }
        
        // This would normally create a real order, but for testing we'll just show the process
        console.log('Simulating order for product:', productId, 'campaign:', campaignId);
        
        // In a real scenario, you would:
        // 1. Create WooCommerce order with this product
        // 2. Set order status to completed
        // 3. Check if sold quantity increased
        
        alert('In a real scenario, this would:\n' +
              '1. Create WooCommerce order with Product ID: ' + productId + '\n' +
              '2. Set order status to completed\n' +
              '3. Trigger update_sold_quantity_for_order()\n' +
              '4. Increase sold quantity in campaign_products table\n\n' +
              'Check the browser console for more details.');
    }
    
    function testSoldQuantityUpdate() {
        console.log('=== Testing Sold Quantity Update ===');
        
        // Check if FS_Public class exists
        if (typeof fs_public !== 'undefined') {
            console.log('✅ FS_Public scripts loaded');
        } else {
            console.log('❌ FS_Public scripts not loaded');
        }
        
        // Test AJAX call to simulate order completion
        fetch(ajaxurl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=test_sold_quantity_update&nonce=' + (fs_public?.nonce || '')
        })
        .then(response => response.json())
        .then(data => {
            console.log('Test result:', data);
            if (data.success) {
                alert('✅ Test completed successfully!\nCheck console for details.');
            } else {
                alert('❌ Test failed: ' + (data.data?.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Test error:', error);
            alert('❌ Test error: ' + error.message);
        });
    }
    </script>
    
    <div style='margin: 20px 0;'>
        <button onclick='testSoldQuantityUpdate()' class='button button-primary'>Run AJAX Test</button>
        <button onclick='location.reload()' class='button'>Refresh Data</button>
    </div>
    
    <?php
    
    echo "<h2>6. Implementation Details</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>How it works:</h3>";
    echo "<ol>";
    echo "<li><strong>Order Hooks:</strong> Listen to <code>woocommerce_order_status_completed</code> and <code>woocommerce_payment_complete</code></li>";
    echo "<li><strong>Product Check:</strong> For each order item, check if product is in active flash sale campaigns</li>";
    echo "<li><strong>Quantity Update:</strong> Increment <code>sold</code> field in <code>fs_campaign_products</code> table</li>";
    echo "<li><strong>Duplicate Prevention:</strong> Use order meta <code>_fs_sold_quantity_updated</code> to prevent double counting</li>";
    echo "<li><strong>Cache Clear:</strong> Clear promotion caches after updating sold quantities</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>7. Database Query Examples</h2>";
    
    echo "<div style='background: #f0f0f0; padding: 10px; border-radius: 3px; margin: 10px 0;'>";
    echo "<h4>Check sold quantities:</h4>";
    echo "<pre>";
    echo "SELECT p.promotion_id, p.product_id, p.sold, c.name\n";
    echo "FROM {$products_table} p\n";
    echo "INNER JOIN {$campaigns_table} c ON p.promotion_id = c.id\n";
    echo "WHERE c.type = 'flash-sale' AND c.status = 'active'\n";
    echo "ORDER BY p.sold DESC;";
    echo "</pre>";
    echo "</div>";
    
    echo "<div style='background: #f0f0f0; padding: 10px; border-radius: 3px; margin: 10px 0;'>";
    echo "<h4>Check processed orders:</h4>";
    echo "<pre>";
    echo "SELECT post_id as order_id, meta_value as processed\n";
    echo "FROM {$wpdb->postmeta}\n";
    echo "WHERE meta_key = '_fs_sold_quantity_updated'\n";
    echo "ORDER BY post_id DESC\n";
    echo "LIMIT 10;";
    echo "</pre>";
    echo "</div>";
}

// Add AJAX handler for testing
add_action('wp_ajax_test_sold_quantity_update', function() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Unauthorized']);
    }
    
    // This is a mock test - in real scenario you'd create actual orders
    wp_send_json_success([
        'message' => 'Sold quantity update functionality is implemented and ready',
        'hooks_registered' => [
            'woocommerce_order_status_completed',
            'woocommerce_payment_complete'
        ],
        'method' => 'update_sold_quantity_for_order',
        'prevention' => '_fs_sold_quantity_updated order meta'
    ]);
});

// Run test if accessed directly
if (isset($_GET['test_sold_quantity'])) {
    test_sold_quantity_update();
}
?>
