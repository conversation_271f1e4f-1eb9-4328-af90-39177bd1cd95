<?php
/**
 * Direct test of quantity promotion save functionality
 * Run this in WordPress admin context to test saving
 */

if (!defined('ABSPATH')) {
    echo "This script must be run in WordPress context\n";
    exit;
}

function test_direct_quantity_save() {
    echo "<h2>Direct Quantity Promotion Save Test</h2>";
    
    // Check if classes exist
    if (!class_exists('FSQP_Database')) {
        echo "<p style='color: red;'>❌ FSQP_Database class not found</p>";
        return;
    }
    
    if (!class_exists('FSQP_Campaign_Handler')) {
        echo "<p style='color: red;'>❌ FSQP_Campaign_Handler class not found</p>";
        return;
    }
    
    echo "<p style='color: green;'>✅ Required classes found</p>";
    
    // Test database connection
    global $wpdb;
    $campaigns_table = $wpdb->prefix . 'fs_campaigns';
    $products_table = $wpdb->prefix . 'fs_campaign_products';
    
    if ($wpdb->get_var("SHOW TABLES LIKE '$campaigns_table'") !== $campaigns_table) {
        echo "<p style='color: red;'>❌ Campaigns table not found: $campaigns_table</p>";
        return;
    }
    
    if ($wpdb->get_var("SHOW TABLES LIKE '$products_table'") !== $products_table) {
        echo "<p style='color: red;'>❌ Products table not found: $products_table</p>";
        return;
    }
    
    echo "<p style='color: green;'>✅ Database tables found</p>";
    
    // Create test campaign first
    $campaign_data = [
        'name' => 'Test Quantity Promotion ' . date('Y-m-d H:i:s'),
        'description' => 'Test campaign for debugging',
        'type' => 'quantity-promotion',
        'priority' => 10,
        'status' => 1,
        'start_date' => date('Y-m-d H:i:s'),
        'end_date' => date('Y-m-d H:i:s', strtotime('+30 days')),
        'created_at' => current_time('mysql'),
        'updated_at' => current_time('mysql')
    ];
    
    $result = $wpdb->insert($campaigns_table, $campaign_data);
    
    if (!$result) {
        echo "<p style='color: red;'>❌ Failed to create test campaign: " . $wpdb->last_error . "</p>";
        return;
    }
    
    $campaign_id = $wpdb->insert_id;
    echo "<p style='color: green;'>✅ Test campaign created with ID: $campaign_id</p>";
    
    // Test products data
    $products_data = [
        '38' => [
            'product_id' => 38,
            'ranges' => [
                [
                    'min_quantity' => 1,
                    'max_quantity' => 5,
                    'discount_type' => 1,
                    'discount_value' => 5,
                    'max_discount_amount' => 0
                ],
                [
                    'min_quantity' => 6,
                    'max_quantity' => 0,
                    'discount_type' => 1,
                    'discount_value' => 10,
                    'max_discount_amount' => 0
                ]
            ]
        ]
    ];
    
    // Test direct database save
    echo "<h3>Testing Direct Database Save</h3>";
    
    $database = new FSQP_Database();
    
    foreach ($products_data as $product_id => $product_data) {
        $product = wc_get_product($product_id);
        if (!$product) {
            echo "<p style='color: orange;'>⚠️ Product $product_id not found, skipping</p>";
            continue;
        }
        
        echo "<p>Processing product: {$product->get_name()} (ID: $product_id)</p>";
        
        foreach ($product_data['ranges'] as $range_index => $range_data) {
            $rule_data = [
                'campaign_id' => $campaign_id,
                'product_id' => $product_id,
                'regular_price' => $product->get_regular_price(),
                'sku' => $product->get_sku(),
                'min_quantity' => intval($range_data['min_quantity']),
                'max_quantity' => intval($range_data['max_quantity']),
                'discount_type' => intval($range_data['discount_type']),
                'discount_value' => floatval($range_data['discount_value']),
                'max_discount_amount' => floatval($range_data['max_discount_amount'])
            ];
            
            $save_result = $database->save_quantity_rule($rule_data);
            
            if ($save_result) {
                echo "<p style='color: green;'>✅ Range $range_index saved with ID: $save_result</p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to save range $range_index: " . $wpdb->last_error . "</p>";
            }
        }
    }
    
    // Test loading the saved data
    echo "<h3>Testing Data Loading</h3>";
    
    $loaded_rules = $database->get_campaign_quantity_rules($campaign_id, false);
    
    if (empty($loaded_rules)) {
        echo "<p style='color: red;'>❌ No rules loaded from database</p>";
    } else {
        echo "<p style='color: green;'>✅ Loaded " . count($loaded_rules) . " rules</p>";
        
        foreach ($loaded_rules as $rule) {
            echo "<p>Rule: Product {$rule->product_id}, Qty {$rule->min_quantity}-{$rule->max_quantity}, Discount {$rule->discount_value}%</p>";
        }
    }
    
    // Test campaign handler
    echo "<h3>Testing Campaign Handler</h3>";
    
    $handler = new FSQP_Campaign_Handler();
    
    // Simulate form data
    $_POST['products'] = $products_data;
    
    try {
        $handler->save_quantity_promotion_data($campaign_id, $campaign_data, $_POST);
        echo "<p style='color: green;'>✅ Campaign handler executed without errors</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Campaign handler failed: " . $e->getMessage() . "</p>";
    }
    
    // Final verification
    echo "<h3>Final Verification</h3>";
    
    $final_count = $wpdb->get_var($wpdb->prepare("
        SELECT COUNT(*) 
        FROM $products_table 
        WHERE promotion_id = %d
    ", $campaign_id));
    
    echo "<p>Total records in database for campaign $campaign_id: $final_count</p>";
    
    // Clean up test campaign
    if (isset($_GET['cleanup']) && $_GET['cleanup'] === '1') {
        $wpdb->delete($products_table, ['promotion_id' => $campaign_id]);
        $wpdb->delete($campaigns_table, ['id' => $campaign_id]);
        echo "<p style='color: blue;'>🧹 Test data cleaned up</p>";
    } else {
        echo "<p><a href='?page=test-direct-save&cleanup=1' class='button'>Clean Up Test Data</a></p>";
    }
}

// Add admin page for testing
add_action('admin_menu', function() {
    if (current_user_can('manage_options')) {
        add_submenu_page(
            null,
            'Test Direct Save',
            'Test Direct Save',
            'manage_options',
            'test-direct-save',
            'test_direct_quantity_save'
        );
    }
});

// Add quick access link
add_action('admin_notices', function() {
    if (current_user_can('manage_options') && isset($_GET['page']) && $_GET['page'] === 'flashsale-campaigns') {
        echo '<div class="notice notice-info"><p>';
        echo '<strong>Debug:</strong> ';
        echo '<a href="?page=test-direct-save" target="_blank">Test Direct Save</a> | ';
        echo '<a href="?debug_fsqp_db=1" target="_blank">Check Database</a>';
        echo '</p></div>';
    }
});

// Test hooks registration
add_action('admin_init', function() {
    if (isset($_GET['test_hooks']) && current_user_can('manage_options')) {
        echo "<h2>Hooks Registration Test</h2>";
        
        global $wp_filter;
        
        $hooks_to_check = ['fs_save_campaign', 'ccp_save_campaign'];
        
        foreach ($hooks_to_check as $hook) {
            echo "<h3>Hook: $hook</h3>";
            
            if (isset($wp_filter[$hook])) {
                echo "<p style='color: green;'>✅ Hook is registered</p>";
                
                foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
                    echo "<p>Priority $priority: " . count($callbacks) . " callbacks</p>";
                    
                    foreach ($callbacks as $callback) {
                        if (is_array($callback['function'])) {
                            $class = get_class($callback['function'][0]);
                            $method = $callback['function'][1];
                            echo "<p>&nbsp;&nbsp;- $class::$method</p>";
                            
                            if ($class === 'FSQP_Campaign_Handler') {
                                echo "<p style='color: green;'>&nbsp;&nbsp;&nbsp;&nbsp;✅ Found FSQP handler!</p>";
                            }
                        }
                    }
                }
            } else {
                echo "<p style='color: red;'>❌ Hook is not registered</p>";
            }
        }
        
        exit;
    }
});
