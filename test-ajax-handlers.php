<?php
/**
 * Test script to verify AJAX handlers are properly registered
 * 
 * This script should be run in WordPress admin context to test the AJAX functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // For testing purposes, we'll simulate WordPress environment
    echo "Testing AJAX handlers registration...\n\n";
}

/**
 * Test if AJAX actions are properly registered
 */
function test_ajax_handlers() {
    global $wp_filter;
    
    $ajax_actions = [
        'wp_ajax_fsqp_search_products',
        'wp_ajax_fsqp_get_product_info', 
        'wp_ajax_fsqp_validate_rules',
        'wp_ajax_fsqp_check_product_conflicts',
        'wp_ajax_fsqp_calculate_quantity_price',
        'wp_ajax_nopriv_fsqp_calculate_quantity_price',
        'wp_ajax_fsqp_get_quantity_discounts',
        'wp_ajax_nopriv_fsqp_get_quantity_discounts'
    ];
    
    echo "Checking AJAX action registrations:\n";
    echo "=====================================\n";
    
    foreach ($ajax_actions as $action) {
        if (isset($wp_filter[$action])) {
            echo "✓ {$action} - REGISTERED\n";
        } else {
            echo "✗ {$action} - NOT REGISTERED\n";
        }
    }
    
    echo "\n";
}

/**
 * Test nonce generation for the form
 */
function test_nonce_generation() {
    if (!function_exists('wp_create_nonce')) {
        echo "WordPress nonce functions not available in test environment\n";
        return;
    }
    
    echo "Testing nonce generation:\n";
    echo "========================\n";
    
    $nonces = [
        'fsqp_search_products' => wp_create_nonce('fsqp_search_products'),
        'fsqp_get_product_info' => wp_create_nonce('fsqp_get_product_info'),
        'fsqp_check_product_conflicts' => wp_create_nonce('fsqp_check_product_conflicts'),
        'fsqp_validate_rules' => wp_create_nonce('fsqp_validate_rules')
    ];
    
    foreach ($nonces as $action => $nonce) {
        echo "✓ {$action}: {$nonce}\n";
    }
    
    echo "\n";
}

/**
 * Test FSQP_Ajax class instantiation
 */
function test_ajax_class() {
    echo "Testing FSQP_Ajax class:\n";
    echo "=======================\n";
    
    if (class_exists('FSQP_Ajax')) {
        echo "✓ FSQP_Ajax class exists\n";
        
        // Check if methods exist
        $methods = [
            'search_products',
            'get_product_info',
            'validate_rules', 
            'check_product_conflicts',
            'calculate_quantity_price',
            'get_quantity_discounts'
        ];
        
        foreach ($methods as $method) {
            if (method_exists('FSQP_Ajax', $method)) {
                echo "✓ Method {$method} exists\n";
            } else {
                echo "✗ Method {$method} missing\n";
            }
        }
    } else {
        echo "✗ FSQP_Ajax class not found\n";
    }
    
    echo "\n";
}

// Run tests if WordPress is loaded
if (defined('ABSPATH')) {
    test_ajax_handlers();
    test_nonce_generation();
    test_ajax_class();
} else {
    echo "This script should be run in WordPress context\n";
    echo "You can include it in a WordPress admin page or run via WP-CLI\n";
}
