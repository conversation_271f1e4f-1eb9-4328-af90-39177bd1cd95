<?php 
$camp = AITFS_Campaign::getCampaign(['promotion_id' => $promotion_id]);

$data = json_decode($camp['data_json'], true);
$should_check = false;
foreach ($result as $key => $rp) {
    if($rp['allow_gift_selection'] == '1' || $rp['allow_gift_selection'] == 1) {
        $should_check = true;
        break;
    }
}

?>

<input type="hidden" name="current_gift_id" value="">
<input type="hidden" name="current_gift_type" value="">

<a href="#0" class="btn-close-gift"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
        class="bi bi-x-lg" viewBox="0 0 16 16">
        <path
            d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z" />
    </svg></a>
<div class="inner-gift-content">
    <table>
        <tbody>
            <tr>
                <td>Cấu hình quà tặng</td>
                <td>
                    <div class="gift-selection-option">
                        <label for="allow_gift_selection">
                            <input type="checkbox" id="allow_gift_selection" name="allow_gift_selection" <?php echo $should_check ? 'checked' : ''; ?>> Cho phép khách hàng chọn quà tặng
                        </label>
                    </div>
                </td>
            </tr>
            <tr>
                <td>Chọn sản phẩm</td>
                <td>
                    <div class="panel-product-gift relative">
                        <div class="search-bar">
                            <div class="field mb-0">
                                <label class="label">Tìm kiếm sản phẩm</label>
                                <div class="columns">
                                    <div class="column control is-12">
                                        <input class="input" type="text" name="txt-search-gift"
                                            placeholder="Nhập tên sản phẩm để tìm kiếm">
                                    </div>
                                </div>
                            </div>
                            <div id="searchResults" class="">
                                <ul class="search-results-list"></ul>
                            </div>
                        </div>
                    </div>

                    <div class="panel-gift-choose">
                        <?php 
                        if($result) {
                            foreach ($result as $key => $rp) {
                                $image = get_the_post_thumbnail($rp['product_id'], 'full');

                                ?>
                                <div class="gift-item" data-gift-id="<?php echo $rp['product_id']; ?>" data-gift-name="<?php echo $rp['name']; ?>">
                                <div class="gift-name"><?php echo $image; ?><?php echo $rp['name']; ?></div><div class="gift-remove"><a href="javascript:;" class="remove-gift"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x" viewBox="0 0 16 16">
                                <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708"></path>
                                </svg></a></div>
                                </div>
                                <?php
                            }
                        }else{
                            echo '<div class="gift-none">Chưa có sản phẩm quà tặng.</div>';
                        }
                        
                        ?>
                    </div>


                </td>
            </tr>

            <tr style="display: none;">
                <td>Ưu đãi thêm</td>
                <td>
                    <?php 
                    // if($result_offline)  {
                        ?>
                        <div class="panel-product-gift">
                            <div class="gift-image">
                                <div id="image-preview">
                                    <?php 
                                    if($result_offline && $result_offline[0]['image']) {
                                        echo '<a href="javascript:;" class="remove-image-gift">&#10539;</a>';
                                        $attachment = wp_get_attachment_image( $result_offline[0]['image'], 'full' );
                                        echo $attachment;
                                        echo '<input type="hidden" name="gift-image" value="'. $result_offline[0]['image'] .'" />';
                                    }
                                    ?>
                                </div>
                                <button id="open-media-button" class="button button-primary">Ảnh quà tặng</button>
                            </div>
                            <?php 
                                $content_gift = (!empty($result_offline[0]['description'])) ? $result_offline[0]['description'] : '';
                                wp_editor( $content_gift , 'content_gift', array(
                                    'wpautop'       => true,
                                    'media_buttons' => false,
                                    'textarea_name' => 'content_gift',
                                    'editor_class'  => 'content_gift_class',
                                    'textarea_rows' => 10
                                ) );    
                            ?>
                        </div>
                        <?php
                    // }
                    
                    ?>
                </td>
            </tr>
        </tbody>
    </table>
    <a href="#0" class="button button-primary btn-gift-save">Lưu quà tặng</a>

</div>