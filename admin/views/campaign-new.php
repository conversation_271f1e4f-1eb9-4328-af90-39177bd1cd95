<?php
/**
 * New campaign page template
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap fs-admin-wrap">
    <div class="fs-top-page">
        <h1 class="fs-title">
            <span class="fs-icon">✨</span>
            <?php _e('Create New Campaign', 'flashsale-core'); ?>
        </h1>
        <div class="fs-top-page-actions">
            <a href="<?php echo admin_url('admin.php?page=flashsale-campaigns'); ?>" class="fs-btn fs-btn-secondary">
                <span class="dashicons dashicons-arrow-left-alt"></span>
                <?php _e('Back to Campaigns', 'flashsale-core'); ?>
            </a>
        </div>
    </div>

    <div class="fs-dashboard">
        <form id="fs-campaign-form" method="post">
            <?php wp_nonce_field('fs_campaign_nonce', '_wpnonce'); ?>
            
            <div class="fs-form-grid">
                <!-- Basic Information -->
                <div class="fs-card">
                    <div class="fs-card-top-page">
                        <h2><?php _e('Basic Information', 'flashsale-core'); ?></h2>
                    </div>
                    <div class="fs-card-content">
                        <div class="fs-form-group">
                            <label for="campaign_name" class="fs-label">
                                <?php _e('Campaign Name', 'flashsale-core'); ?>
                                <span class="fs-required">*</span>
                            </label>
                            <input type="text" 
                                   id="campaign_name" 
                                   name="campaign_name" 
                                   class="fs-input" 
                                   placeholder="<?php _e('Enter campaign name', 'flashsale-core'); ?>"
                                   required>
                        </div>
                        
                        <div class="fs-form-group">
                            <label for="campaign_description" class="fs-label">
                                <?php _e('Description', 'flashsale-core'); ?>
                            </label>
                            <textarea id="campaign_description" 
                                      name="campaign_description" 
                                      class="fs-textarea" 
                                      rows="3"
                                      placeholder="<?php _e('Enter campaign description', 'flashsale-core'); ?>"></textarea>
                        </div>
                        
                        <div class="fs-form-row">
                            <div class="fs-form-group">
                                <label for="campaign_type" class="fs-label">
                                    <?php _e('Campaign Type', 'flashsale-core'); ?>
                                    <span class="fs-required">*</span>
                                </label>
                                <div class="fs-select-wrapper">
                                    <select id="campaign_type" name="campaign_type" class="fs-select" required>
                                        <option value="flash-sale" selected><?php _e('Flash Sale', 'flashsale-core'); ?></option>
                                    </select>
                                </div>
                                <small class="fs-help-text"><?php _e('Additional promotion types available as addons', 'flashsale-core'); ?></small>
                            </div>

                            <div class="fs-form-group">
                                <label for="campaign_priority" class="fs-label">
                                    <?php _e('Priority', 'flashsale-core'); ?>
                                </label>
                                <div class="fs-select-wrapper">
                                    <select id="campaign_priority" name="campaign_priority" class="fs-select">
                                        <?php for ($i = 0; $i <= 10; $i++): ?>
                                            <option value="<?php echo $i; ?>" <?php selected($i, 5); ?>><?php echo $i; ?></option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                                <small class="fs-help-text"><?php _e('Higher numbers = higher priority', 'flashsale-core'); ?></small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Schedule -->
                <div class="fs-card">
                    <div class="fs-card-top-page">
                        <h2><?php _e('Schedule', 'flashsale-core'); ?></h2>
                    </div>
                    <div class="fs-card-content">
                        <div class="fs-form-row">
                            <div class="fs-form-group">
                                <label for="start_date" class="fs-label">
                                    <?php _e('Start Date & Time', 'flashsale-core'); ?>
                                </label>
                                <input type="datetime-local" 
                                       id="start_date" 
                                       name="start_date" 
                                       class="fs-input">
                            </div>
                            
                            <div class="fs-form-group">
                                <label for="end_date" class="fs-label">
                                    <?php _e('End Date & Time', 'flashsale-core'); ?>
                                </label>
                                <input type="datetime-local" 
                                       id="end_date" 
                                       name="end_date" 
                                       class="fs-input">
                            </div>
                        </div>
                        
                        <div class="fs-form-group">
                            <label class="fs-checkbox-label">
                                <input type="checkbox" id="no_end_date" name="no_end_date" class="fs-checkbox">
                                <span class="fs-checkmark"></span>
                                <?php _e('No end date (run indefinitely)', 'flashsale-core'); ?>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Flash Sale Settings -->
                <div class="fs-card fs-full-width" id="campaign-settings">
                    <div class="fs-card-top-page">
                        <h2><?php _e('Flash Sale Configuration', 'flashsale-core'); ?></h2>
                    </div>
                    <div class="fs-card-content">
                        <?php
                        // Include flash sale form
                        $is_edit = false;
                        $campaign = null;
                        include plugin_dir_path(__FILE__) . 'campaign-types/flash-sale.php';
                        ?>
                    </div>
                </div>

                <!-- Status -->
                <div class="fs-card">
                    <div class="fs-card-top-page">
                        <h2><?php _e('Status', 'flashsale-core'); ?></h2>
                    </div>
                    <div class="fs-card-content">
                        <div class="fs-form-group">
                            <label class="fs-checkbox-label">
                                <input type="checkbox" id="campaign_status" name="campaign_status" class="fs-checkbox" value="1" checked>
                                <span class="fs-checkmark"></span>
                                <?php _e('Activate campaign immediately', 'flashsale-core'); ?>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="fs-form-actions">
                <button type="submit" class="fs-btn fs-btn-primary fs-btn-large">
                    <span class="dashicons dashicons-yes"></span>
                    <?php _e('Create Campaign', 'flashsale-core'); ?>
                </button>
                
                <button type="button" class="fs-btn fs-btn-secondary fs-btn-large" id="save-draft">
                    <span class="dashicons dashicons-edit"></span>
                    <?php _e('Save as Draft', 'flashsale-core'); ?>
                </button>
                
                <a href="<?php echo admin_url('admin.php?page=flashsale-campaigns'); ?>" class="fs-btn fs-btn-outline fs-btn-large">
                    <?php _e('Cancel', 'flashsale-core'); ?>
                </a>
            </div>
        </form>
    </div>
</div>



<script>
jQuery(document).ready(function($) {
    // No end date checkbox
    $('#no_end_date').on('change', function() {
        if ($(this).is(':checked')) {
            $('#end_date').prop('disabled', true).val('');
        } else {
            $('#end_date').prop('disabled', false);
        }
    });

    // Save as draft
    $('#save-draft').on('click', function() {
        $('#campaign_status').prop('checked', false);
        $('#fs-campaign-form').submit();
    });

    // Form validation
    $('#fs-campaign-form').on('submit', function(e) {
        var isValid = true;
        var errors = [];

        // Check required fields
        if (!$('#campaign_name').val().trim()) {
            errors.push('<?php _e('Campaign name is required', 'flashsale-core'); ?>');
            isValid = false;
        }

        // Check if at least one product is added
        if ($('#fs-products-tbody tr').length === 0) {
            errors.push('<?php _e('Please add at least one product to the flash sale', 'flashsale-core'); ?>');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            alert(errors.join('\n'));
        }
    });
});
</script>
