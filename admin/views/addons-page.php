<?php
/**
 * Addons page template
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
<div class="flashsale-admin-container">
    <div class="fs-top-page">
        <h1 class="fs-title">
            <span class="fs-icon">🧩</span>
            <?php _e('FlashSale Addons', 'flashsale-core'); ?>
        </h1>
        <div class="fs-top-page-actions">
            <button class="fs-btn fs-btn-primary" id="refresh-addons">
                <span class="dashicons dashicons-update"></span>
                <?php _e('Refresh', 'flashsale-core'); ?>
            </button>
        </div>
    </div>

    <div class="fs-dashboard">
        <!-- Addon Categories -->
        <div class="fs-addon-categories">
            <button class="fs-category-btn active" data-category="all">
                <?php _e('All Addons', 'flashsale-core'); ?>
            </button>
            <button class="fs-category-btn" data-category="promotion">
                <?php _e('Promotion', 'flashsale-core'); ?>
            </button>
            <button class="fs-category-btn" data-category="analytics">
                <?php _e('Analytics', 'flashsale-core'); ?>
            </button>
            <button class="fs-category-btn" data-category="integration">
                <?php _e('Integration', 'flashsale-core'); ?>
            </button>
            <button class="fs-category-btn" data-category="ui">
                <?php _e('UI/UX', 'flashsale-core'); ?>
            </button>
        </div>

        <!-- Addons Grid -->
        <div class="fs-addons-container">
            <?php if (!empty($addons) && is_array($addons) && count($addons) > 0): ?>
                <div class="fs-addons-grid">
                    <?php foreach ($addons as $addon): ?>
                        <div class="fs-addon-item" data-category="<?php echo esc_attr($addon->addon_type ?? 'promotion'); ?>">
                            <div class="fs-addon-card">
                                <div class="fs-addon-header">
                                    <div class="fs-addon-icon">
                                        <span class="dashicons dashicons-admin-plugins"></span>
                                    </div>
                                    <div class="fs-addon-status-badge">
                                        <?php if ($addon->status): ?>
                                            <span class="fs-status fs-status-active"><?php _e('Active', 'flashsale-core'); ?></span>
                                        <?php else: ?>
                                            <span class="fs-status fs-status-inactive"><?php _e('Inactive', 'flashsale-core'); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="fs-addon-content">
                                    <h3 class="fs-addon-title"><?php echo esc_html($addon->addon_name); ?></h3>
                                    <p class="fs-addon-description">
                                        <?php _e('Custom addon for extending FlashSale functionality.', 'flashsale-core'); ?>
                                    </p>

                                    <div class="fs-addon-meta">
                                        <span class="fs-addon-version">
                                            <?php printf(__('Version %s', 'flashsale-core'), $addon->addon_version); ?>
                                        </span>
                                        <span class="fs-addon-type">
                                            <?php echo esc_html(ucfirst($addon->addon_type)); ?>
                                        </span>
                                    </div>
                                </div>

                                <div class="fs-addon-actions">
                                    <?php if ($addon->status): ?>
                                        <button class="fs-btn fs-btn-danger fs-toggle-addon"
                                                data-addon="<?php echo esc_attr($addon->addon_slug); ?>"
                                                data-action="deactivate">
                                            <span class="dashicons dashicons-controls-pause"></span>
                                            <?php _e('Deactivate', 'flashsale-core'); ?>
                                        </button>
                                    <?php else: ?>
                                        <button class="fs-btn fs-btn-primary fs-toggle-addon"
                                                data-addon="<?php echo esc_attr($addon->addon_slug); ?>"
                                                data-action="activate">
                                            <span class="dashicons dashicons-controls-play"></span>
                                            <?php _e('Activate', 'flashsale-core'); ?>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <!-- Sample/Demo Addons -->
                <div class="fs-addons-grid">
                    <!-- Product Promotion Addon -->
                    <div class="fs-addon-item" data-category="promotion">
                        <div class="fs-addon-card fs-addon-demo">
                            <div class="fs-addon-header">
                                <div class="fs-addon-icon">
                                    <span class="dashicons dashicons-products"></span>
                                </div>
                                <div class="fs-addon-status-badge">
                                    <span class="fs-status fs-status-active"><?php _e('Built-in', 'flashsale-core'); ?></span>
                                </div>
                            </div>
                            
                            <div class="fs-addon-content">
                                <h3 class="fs-addon-title"><?php _e('Product Promotion', 'flashsale-core'); ?></h3>
                                <p class="fs-addon-description">
                                    <?php _e('Create flash sales for specific products with percentage or fixed discounts.', 'flashsale-core'); ?>
                                </p>
                                
                                <div class="fs-addon-meta">
                                    <span class="fs-addon-version"><?php _e('Version 2.0.0', 'flashsale-core'); ?></span>
                                    <span class="fs-addon-type"><?php _e('Promotion', 'flashsale-core'); ?></span>
                                </div>
                            </div>
                            
                            <div class="fs-addon-actions">
                                <span class="fs-btn fs-btn-success fs-btn-disabled">
                                    <span class="dashicons dashicons-yes"></span>
                                    <?php _e('Active', 'flashsale-core'); ?>
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Analytics Addon (Coming Soon) -->
                    <div class="fs-addon-item" data-category="analytics">
                        <div class="fs-addon-card fs-addon-coming-soon">
                            <div class="fs-addon-header">
                                <div class="fs-addon-icon">
                                    <span class="dashicons dashicons-chart-bar"></span>
                                </div>
                                <div class="fs-addon-status-badge">
                                    <span class="fs-status fs-status-coming-soon"><?php _e('Coming Soon', 'flashsale-core'); ?></span>
                                </div>
                            </div>
                            
                            <div class="fs-addon-content">
                                <h3 class="fs-addon-title"><?php _e('Advanced Analytics', 'flashsale-core'); ?></h3>
                                <p class="fs-addon-description">
                                    <?php _e('Detailed analytics and reporting for your flash sale campaigns.', 'flashsale-core'); ?>
                                </p>
                                
                                <div class="fs-addon-meta">
                                    <span class="fs-addon-version"><?php _e('Version 1.0.0', 'flashsale-core'); ?></span>
                                    <span class="fs-addon-type"><?php _e('Analytics', 'flashsale-core'); ?></span>
                                </div>
                            </div>
                            
                            <div class="fs-addon-actions">
                                <button class="fs-btn fs-btn-outline fs-btn-disabled">
                                    <span class="dashicons dashicons-clock"></span>
                                    <?php _e('Coming Soon', 'flashsale-core'); ?>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Email Marketing Integration -->
                    <div class="fs-addon-item" data-category="integration">
                        <div class="fs-addon-card fs-addon-coming-soon">
                            <div class="fs-addon-header">
                                <div class="fs-addon-icon">
                                    <span class="dashicons dashicons-email-alt"></span>
                                </div>
                                <div class="fs-addon-status-badge">
                                    <span class="fs-status fs-status-coming-soon"><?php _e('Coming Soon', 'flashsale-core'); ?></span>
                                </div>
                            </div>
                            
                            <div class="fs-addon-content">
                                <h3 class="fs-addon-title"><?php _e('Email Marketing', 'flashsale-core'); ?></h3>
                                <p class="fs-addon-description">
                                    <?php _e('Integrate with popular email marketing services to promote your flash sales.', 'flashsale-core'); ?>
                                </p>
                                
                                <div class="fs-addon-meta">
                                    <span class="fs-addon-version"><?php _e('Version 1.0.0', 'flashsale-core'); ?></span>
                                    <span class="fs-addon-type"><?php _e('Integration', 'flashsale-core'); ?></span>
                                </div>
                            </div>
                            
                            <div class="fs-addon-actions">
                                <button class="fs-btn fs-btn-outline fs-btn-disabled">
                                    <span class="dashicons dashicons-clock"></span>
                                    <?php _e('Coming Soon', 'flashsale-core'); ?>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Countdown Timer -->
                    <div class="fs-addon-item" data-category="ui">
                        <div class="fs-addon-card fs-addon-coming-soon">
                            <div class="fs-addon-header">
                                <div class="fs-addon-icon">
                                    <span class="dashicons dashicons-clock"></span>
                                </div>
                                <div class="fs-addon-status-badge">
                                    <span class="fs-status fs-status-coming-soon"><?php _e('Coming Soon', 'flashsale-core'); ?></span>
                                </div>
                            </div>
                            
                            <div class="fs-addon-content">
                                <h3 class="fs-addon-title"><?php _e('Countdown Timer', 'flashsale-core'); ?></h3>
                                <p class="fs-addon-description">
                                    <?php _e('Add beautiful countdown timers to create urgency for your flash sales.', 'flashsale-core'); ?>
                                </p>
                                
                                <div class="fs-addon-meta">
                                    <span class="fs-addon-version"><?php _e('Version 1.0.0', 'flashsale-core'); ?></span>
                                    <span class="fs-addon-type"><?php _e('UI/UX', 'flashsale-core'); ?></span>
                                </div>
                            </div>
                            
                            <div class="fs-addon-actions">
                                <button class="fs-btn fs-btn-outline fs-btn-disabled">
                                    <span class="dashicons dashicons-clock"></span>
                                    <?php _e('Coming Soon', 'flashsale-core'); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Empty State Message -->
                <div class="fs-empty-state fs-addons-empty">
                    <span class="dashicons dashicons-admin-plugins"></span>
                    <h3><?php _e('No addons installed', 'flashsale-core'); ?></h3>
                    <p><?php _e('Install addon plugins to extend FlashSale functionality. Addons are separate WordPress plugins that integrate with FlashSale Core.', 'flashsale-core'); ?></p>
                    <div style="margin-top: 20px;">
                        <p><strong><?php _e('How to install addons:', 'flashsale-core'); ?></strong></p>
                        <ol style="text-align: left; display: inline-block;">
                            <li><?php _e('Download addon plugin files', 'flashsale-core'); ?></li>
                            <li><?php _e('Install via WordPress Plugins > Add New > Upload', 'flashsale-core'); ?></li>
                            <li><?php _e('Activate the addon plugin', 'flashsale-core'); ?></li>
                            <li><?php _e('Return here to manage addon settings', 'flashsale-core'); ?></li>
                        </ol>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
</div>



<script>
jQuery(document).ready(function($) {
    // Category filtering
    $('.fs-category-btn').on('click', function() {
        var category = $(this).data('category');
        
        // Update active button
        $('.fs-category-btn').removeClass('active');
        $(this).addClass('active');
        
        // Filter addons
        if (category === 'all') {
            $('.fs-addon-item').show();
        } else {
            $('.fs-addon-item').hide();
            $('.fs-addon-item[data-category="' + category + '"]').show();
        }
    });
    
    // Toggle addon
    $('.fs-toggle-addon').on('click', function() {
        var $btn = $(this);
        var addon = $btn.data('addon');
        var action = $btn.data('action');

        $btn.prop('disabled', true);

        // AJAX request
        $.post(fs_admin.ajax_url, {
            action: 'fs_admin_action',
            fs_action: 'toggle_addon',
            addon_slug: addon,
            status: action,
            nonce: fs_admin.nonce
        }, function(response) {
            if (response.success) {
                if (action === 'activate') {
                    $btn.removeClass('fs-btn-primary').addClass('fs-btn-danger');
                    $btn.data('action', 'deactivate');
                    $btn.html('<span class="dashicons dashicons-controls-pause"></span> <?php _e('Deactivate', 'flashsale-core'); ?>');
                    $btn.closest('.fs-addon-card').find('.fs-status').removeClass('fs-status-inactive').addClass('fs-status-active').text('<?php _e('Active', 'flashsale-core'); ?>');
                } else {
                    $btn.removeClass('fs-btn-danger').addClass('fs-btn-primary');
                    $btn.data('action', 'activate');
                    $btn.html('<span class="dashicons dashicons-controls-play"></span> <?php _e('Activate', 'flashsale-core'); ?>');
                    $btn.closest('.fs-addon-card').find('.fs-status').removeClass('fs-status-active').addClass('fs-status-inactive').text('<?php _e('Inactive', 'flashsale-core'); ?>');
                }
            } else {
                alert(response.data.message || '<?php _e('Error occurred!', 'flashsale-core'); ?>');
            }
            $btn.prop('disabled', false);
        }).fail(function() {
            alert('<?php _e('Error occurred!', 'flashsale-core'); ?>');
            $btn.prop('disabled', false);
        });
    });
    
    // Refresh addons
    $('#refresh-addons').on('click', function() {
        location.reload();
    });
});
</script>
