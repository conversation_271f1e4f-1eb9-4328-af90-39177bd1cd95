<?php
/**
 * Flash Sale Campaign Type Form
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get existing products if editing
$campaign_products = [];
if ($is_edit && $campaign) {
    // Get products from campaign - use safe method or fallback to empty array
    if (method_exists(FS()->campaigns(), 'get_campaign_products')) {
        $campaign_products = FS()->campaigns()->get_campaign_products($campaign->id);
    } else {
        // Fallback: get products directly from database if method doesn't exist
        global $wpdb;
        $products_table = $wpdb->prefix . 'fs_campaign_products';
        if ($wpdb->get_var("SHOW TABLES LIKE '$products_table'") == $products_table) {
            $campaign_products = $wpdb->get_results($wpdb->prepare(
                "SELECT * FROM $products_table WHERE campaign_id = %d",
                $campaign->id
            ));
        }
    }
}
?>

<div class="fs-campaign-type-content">
    <div class="fs-section-header">
        <h2><?php _e('Flash Sale Products', 'flashsale-core'); ?></h2>
        <p><?php _e('Add products to your flash sale campaign and configure pricing, quantities, and timing.', 'flashsale-core'); ?></p>
    </div>

    <!-- Product Search & Selection -->
    <div class="fs-product-search-section">
        <div class="fs-search-bar">
            <div class="fs-search-input-group">
                <input type="text" id="fs-product-search" placeholder="<?php _e('Search products by name, SKU, or ID...', 'flashsale-core'); ?>">
                <div class="fs-search-loading">
                    <div class="fs-loading-spinner"></div>
                </div>
                <button type="button" id="fs-search-btn" class="fs-btn fs-btn-secondary">
                    <span class="dashicons dashicons-search"></span>
                    <?php _e('Search', 'flashsale-core'); ?>
                </button>
            </div>
            
            <div class="fs-search-filters">
                <div class="fs-select-wrapper">
                    <select id="fs-category-filter" class="fs-select">
                        <option value=""><?php _e('All Categories', 'flashsale-core'); ?></option>
                        <?php
                        $categories = get_terms([
                            'taxonomy' => 'product_cat',
                            'hide_empty' => false,
                            'orderby' => 'name'
                        ]);
                        foreach ($categories as $category) {
                            echo '<option value="' . $category->term_id . '">' . esc_html($category->name) . '</option>';
                        }
                        ?>
                    </select>
                </div>

                <button type="button" id="fs-load-category-products" class="fs-btn fs-btn-outline">
                    <span class="fs-btn-text"><?php _e('Load Category Products', 'flashsale-core'); ?></span>
                    <span class="fs-loading-spinner" style="display: none;"></span>
                </button>
            </div>
        </div>

        <!-- Search Results -->
        <div id="fs-search-results" class="fs-search-results" style="display: none;">
            <div class="fs-search-results-header">
                <h4><?php _e('Search Results', 'flashsale-core'); ?></h4>
                <button type="button" id="fs-close-search" class="fs-btn fs-btn-small">×</button>
            </div>
            <div class="fs-search-results-list"></div>
        </div>
    </div>

    <!-- Campaign Products Section -->
    <div class="fs-products-section">
        <div class="fs-section-header">
            <h3><?php _e('Campaign Products', 'flashsale-core'); ?>
                <span class="fs-product-count">(<?php echo !empty($campaign_products) ? count($campaign_products) : 0; ?> <?php _e('products', 'flashsale-core'); ?>)</span>
            </h3>
            <div class="fs-section-actions">
                <button type="button" id="fs-bulk-actions-btn" class="fs-btn fs-btn-outline" disabled>
                    <?php _e('Bulk Actions', 'flashsale-core'); ?>
                </button>
            </div>
        </div>

        <!-- Products Table Container -->
        <div class="fs-products-container">
            <div class="fs-products-table-wrapper">
                <table id="fs-products-table" class="fs-table fs-products-table">
                    <thead>
                        <tr>
                            <th width="40px" class="fs-checkbox-column">
                                <div class="fs-checkbox-wrapper">
                                    <input type="checkbox" id="fs-select-all-products" class="fs-checkbox">
                                    <label for="fs-select-all-products" class="fs-checkbox-label"></label>
                                </div>
                            </th>
                            <th width="280px" class="fs-product-info-column"><?php _e('Product Information', 'flashsale-core'); ?></th>
                            <th width="100px" class="fs-price-column"><?php _e('Regular Price', 'flashsale-core'); ?></th>
                            <th width="160px" class="fs-discount-column"><?php _e('Discount Settings', 'flashsale-core'); ?></th>
                            <th width="100px" class="fs-max-discount-column"><?php _e('Max Discount', 'flashsale-core'); ?></th>
                            <th width="80px" class="fs-sold-column"><?php _e('Sold', 'flashsale-core'); ?></th>
                            <th width="80px" class="fs-max-qty-column"><?php _e('Max Qty', 'flashsale-core'); ?></th>
                            <th width="180px" class="fs-time-range-column"><?php _e('Time Range', 'flashsale-core'); ?></th>
                            <th width="80px" class="fs-actions-column"><?php _e('Actions', 'flashsale-core'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="fs-products-tbody">
                    <?php if (!empty($campaign_products)): ?>
                        <?php foreach ($campaign_products as $product_data): ?>
                            <?php
                            $product = wc_get_product($product_data->product_id);
                            if (!$product) continue;
                            ?>
                            <tr data-product-id="<?php echo $product_data->product_id; ?>" class="fs-product-row">
                                <td class="fs-checkbox-column">
                                    <div class="fs-checkbox-wrapper">
                                        <input type="checkbox" class="fs-product-checkbox fs-checkbox"
                                               id="product-<?php echo $product_data->product_id; ?>"
                                               value="<?php echo $product_data->product_id; ?>">
                                        <label for="product-<?php echo $product_data->product_id; ?>" class="fs-checkbox-label"></label>
                                    </div>
                                </td>
                                <td class="fs-product-info-cell">
                                    <div class="fs-product-info-enhanced">
                                        <div class="fs-product-image">
                                            <?php
                                            $image = wp_get_attachment_image_src(get_post_thumbnail_id($product->get_id()), 'thumbnail');
                                            if ($image): ?>
                                                <img src="<?php echo $image[0]; ?>" alt="<?php echo esc_attr($product->get_name()); ?>" class="fs-product-thumb">
                                            <?php else: ?>
                                                <div class="fs-product-thumb-placeholder">
                                                    <span class="dashicons dashicons-products"></span>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="fs-product-details">
                                            <strong class="fs-product-name"><?php echo esc_html($product->get_name()); ?></strong>
                                            <div class="fs-product-meta">
                                                <span class="fs-product-sku">SKU: <?php echo $product->get_sku() ?: '-'; ?></span>
                                                <span class="fs-product-id">ID: <?php echo $product->get_id(); ?></span>
                                                <?php if ($product->get_stock_status() === 'instock'): ?>
                                                    <span class="fs-stock-status fs-in-stock"><?php _e('In Stock', 'flashsale-core'); ?></span>
                                                <?php else: ?>
                                                    <span class="fs-stock-status fs-out-of-stock"><?php _e('Out of Stock', 'flashsale-core'); ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="fs-price-cell">
                                    <span class="fs-regular-price"><?php echo $product->get_regular_price() ? number_format($product->get_regular_price(), 2) : '-'; ?></span>
                                </td>
                                <td class="fs-discount-cell">
                                    <div class="fs-discount-controls">
                                        <div class="fs-discount-type-toggle">
                                            <button type="button" class="fs-discount-type-btn <?php echo ($product_data->discount_type ?? 1) == 1 ? 'active' : ''; ?>" data-type="percentage">%</button>
                                            <button type="button" class="fs-discount-type-btn <?php echo ($product_data->discount_type ?? 1) == 2 ? 'active' : ''; ?>" data-type="fixed">$</button>
                                        </div>
                                        <input type="number" name="products[<?php echo $product_data->product_id; ?>][discount_value]"
                                               value="<?php echo $product_data->discount_value ?? 0; ?>"
                                               min="0" step="0.01" class="fs-input-small fs-discount-input"
                                               placeholder="0">
                                        <input type="hidden" name="products[<?php echo $product_data->product_id; ?>][discount_type]" value="<?php echo ($product_data->discount_type ?? 1) == 1 ? 'percentage' : 'fixed'; ?>">
                                    </div>
                                </td>
                                <td class="fs-input-cell">
                                    <input type="number" name="products[<?php echo $product_data->product_id; ?>][max_discount]"
                                           value="<?php echo $product_data->max_discount_amount ?? 0; ?>"
                                           min="0" step="0.01" class="fs-input-small" placeholder="0">
                                </td>
                                <td class="fs-input-cell">
                                    <input type="number" name="products[<?php echo $product_data->product_id; ?>][sold_quantity]"
                                           value="<?php echo $product_data->sold ?? 0; ?>"
                                           min="0" class="fs-input-small" placeholder="0">
                                </td>
                                <td class="fs-input-cell">
                                    <input type="number" name="products[<?php echo $product_data->product_id; ?>][max_quantity]"
                                           value="<?php echo $product_data->qty_max ?? 0; ?>"
                                           min="0" class="fs-input-small" placeholder="∞">
                                </td>
                                <td class="fs-input-cell">
                                    <input type="text" name="products[<?php echo $product_data->product_id; ?>][time_range]"
                                           value="<?php echo $product_data->start_date && $product_data->end_date ?
                                               date('Y-m-d H:i', strtotime($product_data->start_date)) . ' - ' .
                                               date('Y-m-d H:i', strtotime($product_data->end_date)) : ''; ?>"
                                           class="fs-daterange-picker fs-input-small"
                                           placeholder="<?php _e('Select time range', 'flashsale-core'); ?>"
                                           readonly>
                                </td>
                                <td class="fs-actions-cell">
                                    <button type="button" class="fs-btn fs-btn-danger fs-btn-small fs-remove-product"
                                            title="<?php _e('Remove product', 'flashsale-core'); ?>">
                                        <span class="dashicons dashicons-trash"></span>
                                    </button>
                                    <input type="hidden" name="products[<?php echo $product_data->product_id; ?>][product_id]" value="<?php echo $product_data->product_id; ?>">
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                    </tbody>
                </table>

                <?php if (empty($campaign_products)): ?>
                    <div id="fs-empty-products" class="fs-empty-state">
                        <span class="dashicons dashicons-products"></span>
                        <h3><?php _e('No products added yet', 'flashsale-core'); ?></h3>
                        <p><?php _e('Search and add products to your flash sale campaign.', 'flashsale-core'); ?></p>
                    </div>
                <?php endif; ?>
            </div> <!-- End products-table-wrapper -->
        </div> <!-- End products-container -->
    </div> <!-- End products-section -->
</div>

<script>
jQuery(document).ready(function($) {
    // Product search functionality
    let searchTimeout;

    $('#fs-product-search').on('input', function() {
        clearTimeout(searchTimeout);
        const query = $(this).val().trim();

        if (query.length >= 2) {
            searchTimeout = setTimeout(() => {
                searchProducts(query);
            }, 500);
        } else {
            $('#fs-search-results').hide();
            $('.fs-search-input-group').removeClass('loading');
        }
    });
    
    $('#fs-search-btn').on('click', function() {
        const query = $('#fs-product-search').val().trim();
        if (query) {
            searchProducts(query);
        }
    });
    
    function searchProducts(query) {
        const categoryId = $('#fs-category-filter').val();

        // Show loading state
        $('.fs-search-input-group').addClass('loading');
        $('#fs-search-results').hide();

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'fs_search_products',
                query: query,
                category_id: categoryId,
                nonce: '<?php echo wp_create_nonce('fs_search_products'); ?>'
            },
            success: function(response) {
                $('.fs-search-input-group').removeClass('loading');
                if (response.success) {
                    displaySearchResults(response.data);
                } else {
                    displaySearchResults([]);
                }
            },
            error: function() {
                $('.fs-search-input-group').removeClass('loading');
                displaySearchResults([]);
            }
        });
    }
    
    function displaySearchResults(products) {
        const $results = $('#fs-search-results .fs-search-results-list');
        $results.empty();

        if (products.length === 0) {
            $results.html('<p><?php _e('No products found.', 'flashsale-core'); ?></p>');
        } else {
            products.forEach(product => {
                // Enhanced display for variable products
                let typeInfo = '';
                let parentInfo = '';
                let soldInfo = '';

                if (product.is_variation) {
                    typeInfo = ' <span class="fs-product-type fs-variation"><?php _e('Variation', 'flashsale-core'); ?></span>';
                    parentInfo = ` (Parent: ${product.parent_id})`;
                } else if (product.type === 'variable') {
                    typeInfo = ' <span class="fs-product-type fs-variable"><?php _e('Variable', 'flashsale-core'); ?></span>';
                }

                if (product.total_sold !== undefined) {
                    soldInfo = `<span>Sold: ${product.total_sold}</span>`;
                }

                const $item = $(`
                    <div class="fs-search-result-item" data-product-id="${product.id}" data-product-type="${product.type || 'simple'}">
                        <div class="fs-product-info">
                            <strong>${product.name}${typeInfo}</strong>
                            <div class="fs-product-meta">
                                <span>SKU: ${product.sku || '-'}</span>
                                <span>ID: ${product.id}${parentInfo}</span>
                                <span>Price: ${product.price}</span>
                                ${soldInfo}
                            </div>
                        </div>
                        <button type="button" class="fs-btn fs-btn-primary fs-btn-small fs-add-product">
                            <?php _e('Add', 'flashsale-core'); ?>
                        </button>
                    </div>
                `);
                $results.append($item);
            });
        }
        
        $('#fs-search-results').show();
    }
    
    // Add product to campaign
    $(document).on('click', '.fs-add-product', function() {
        const $item = $(this).closest('.fs-search-result-item');
        const productId = $item.data('product-id');
        const $button = $(this);

        // Check if product already added to current campaign
        if ($(`#fs-products-tbody tr[data-product-id="${productId}"]`).length > 0) {
            alert('<?php _e('Product already added to campaign.', 'flashsale-core'); ?>');
            return;
        }

        // Check for conflicts with other campaigns
        $button.prop('disabled', true).text('<?php _e('Checking...', 'flashsale-core'); ?>');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'fs_check_product_conflicts',
                product_id: productId,
                exclude_campaign_id: $('#campaign_id').val() || 0,
                nonce: '<?php echo wp_create_nonce('fs_check_product_conflicts'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    if (response.data.has_conflicts) {
                        const conflicts = response.data.conflicts;
                        let message = '<?php _e('This product is already in the following campaigns:', 'flashsale-core'); ?>\n\n';
                        conflicts.forEach(function(conflict) {
                            message += `• ${conflict.campaign_name} (Priority: ${conflict.priority})\n`;
                        });
                        message += '\n<?php _e('Do you want to continue anyway?', 'flashsale-core'); ?>';

                        if (confirm(message)) {
                            addProductToTable(productId, $item);
                        }
                    } else {
                        addProductToTable(productId, $item);
                    }
                } else {
                    alert(response.data.message || '<?php _e('Error checking product conflicts', 'flashsale-core'); ?>');
                }
            },
            error: function() {
                alert('<?php _e('Error checking product conflicts', 'flashsale-core'); ?>');
            },
            complete: function() {
                $button.prop('disabled', false).text('<?php _e('Add', 'flashsale-core'); ?>');
            }
        });
    });
    
    function addProductToTable(productId, $searchItem) {
        // Hide empty state
        $('#fs-empty-products').hide();

        // Get product data from search item
        const productName = $searchItem.find('strong').text();
        const productMeta = $searchItem.find('.fs-product-meta').html();
        const productPrice = $searchItem.find('.fs-product-meta').text().match(/Price: ([0-9.,]+)/);
        const displayPrice = productPrice ? productPrice[1] : '-';

        // Get additional product info via AJAX for accurate data
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'fs_get_product_info',
                product_id: productId,
                nonce: '<?php echo wp_create_nonce('fs_get_product_info'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    const product = response.data;
                    updateProductRow(productId, product);
                }
            }
        });

        const $newRow = $(`
            <tr data-product-id="${productId}" class="fs-product-row">
                <td class="fs-checkbox-column">
                    <div class="fs-checkbox-wrapper">
                        <input type="checkbox" class="fs-product-checkbox fs-checkbox" id="product-${productId}" value="${productId}">
                        <label for="product-${productId}" class="fs-checkbox-label"></label>
                    </div>
                </td>
                <td class="fs-product-info-cell">
                    <div class="fs-product-info-enhanced">
                        <div class="fs-product-image">
                            <div class="fs-product-thumb-placeholder">
                                <span class="dashicons dashicons-products"></span>
                            </div>
                        </div>
                        <div class="fs-product-details">
                            <strong class="fs-product-name">${productName}</strong>
                            <div class="fs-product-meta">${productMeta}</div>
                        </div>
                    </div>
                </td>
                <td class="fs-price-cell">
                    <span class="fs-regular-price">${displayPrice}</span>
                </td>
                <td class="fs-discount-cell">
                    <div class="fs-discount-controls">
                        <div class="fs-discount-type-toggle">
                            <button type="button" class="fs-discount-type-btn active" data-type="percentage">%</button>
                            <button type="button" class="fs-discount-type-btn" data-type="fixed">$</button>
                        </div>
                        <input type="number" name="products[${productId}][discount_value]" value="0" min="0" step="0.01" class="fs-input-small fs-discount-input" placeholder="0">
                        <input type="hidden" name="products[${productId}][discount_type]" value="percentage">
                    </div>
                </td>
                <td class="fs-input-cell">
                    <input type="number" name="products[${productId}][max_discount]" value="0" min="0" step="0.01" class="fs-input-small" placeholder="0">
                </td>
                <td class="fs-input-cell">
                    <input type="number" name="products[${productId}][sold_quantity]" value="0" min="0" class="fs-input-small" placeholder="0">
                </td>
                <td class="fs-input-cell">
                    <input type="number" name="products[${productId}][max_quantity]" value="0" min="0" class="fs-input-small" placeholder="∞">
                </td>
                <td class="fs-input-cell">
                    <input type="text" name="products[${productId}][time_range]" class="fs-daterange-picker fs-input-small" placeholder="<?php _e('Select time range', 'flashsale-core'); ?>" readonly>
                </td>
                <td class="fs-actions-cell">
                    <button type="button" class="fs-btn fs-btn-danger fs-btn-small fs-remove-product" title="<?php _e('Remove product', 'flashsale-core'); ?>">
                        <span class="dashicons dashicons-trash"></span>
                    </button>
                    <input type="hidden" name="products[${productId}][product_id]" value="${productId}">
                </td>
            </tr>
        `);

        $('#fs-products-tbody').append($newRow);
        updateProductCount();
    }

    function updateProductRow(productId, product) {
        const $row = $(`tr[data-product-id="${productId}"]`);
        if ($row.length) {
            // Update price (numeric only)
            const price = product.price_raw || product.price || '-';
            $row.find('.fs-regular-price').text(price !== '-' ? parseFloat(price).toFixed(2) : '-');

            // Update product image if available
            if (product.image_url) {
                $row.find('.fs-product-thumb-placeholder').replaceWith(
                    `<img src="${product.image_url}" alt="${product.name}" class="fs-product-thumb">`
                );
            }

            // Update stock status
            const stockHtml = product.in_stock ?
                '<span class="fs-stock-status fs-in-stock"><?php _e('In Stock', 'flashsale-core'); ?></span>' :
                '<span class="fs-stock-status fs-out-of-stock"><?php _e('Out of Stock', 'flashsale-core'); ?></span>';

            $row.find('.fs-product-meta').append(stockHtml);
        }
    }

    function updateProductCount() {
        const count = $('#fs-products-tbody tr').length;
        $('.fs-product-count').text(`(${count} <?php _e('products', 'flashsale-core'); ?>)`);
    }

    // Initialize bulk actions using shared library
    if (window.FSBulkActions) {
        FSBulkActions.init({
            container: '.fs-products-section',
            selectAllId: '#fs-select-all-products',
            checkboxClass: '.fs-product-checkbox',
            bulkButtonId: '#fs-bulk-actions-btn',
            bulkMenuId: '#fs-bulk-actions-menu',
            actions: {
                'delete': {
                    label: '<?php _e('Delete Selected', 'flashsale-core'); ?>',
                    icon: 'dashicons-trash',
                    class: 'fs-bulk-delete',
                    confirm: '<?php _e('Are you sure you want to delete the selected products?', 'flashsale-core'); ?>'
                }
            },
            callbacks: {
                onBulkAction: function(action, selectedItems) {
                    handleFlashSaleBulkAction(action, selectedItems);
                }
            }
        });
    }

    // Handle flash sale specific bulk actions
    function handleFlashSaleBulkAction(action, selectedItems) {
        switch (action) {
            case 'delete':
                bulkDeleteProducts(selectedItems);
                break;
        }
    }

    function bulkDeleteProducts(selectedItems) {
        selectedItems.forEach(item => {
            $(item.row).fadeOut(300, function() {
                $(this).remove();
                updateProductCount();
            });
        });

        if (window.FlashSaleAdmin) {
            FlashSaleAdmin.showNotification('<?php _e('Selected products deleted successfully.', 'flashsale-core'); ?>', 'success');
        }
    }

    // Remove product - DOM only (no AJAX needed since hidden inputs are inside table rows)
    $(document).on('click', '.fs-remove-product', function() {
        const $btn = $(this);
        const $row = $btn.closest('tr');
        const productId = $row.data('product-id') || $row.attr('data-product-id');

        // Show confirmation
        if (!confirm('<?php _e('Are you sure you want to remove this product from the campaign?', 'flashsale-core'); ?>')) {
            return;
        }

        // Remove row with animation (hidden input is inside the row, so it gets removed too)
        $row.fadeOut(300, function() {
            $(this).remove();
            updateProductCount();

            // Show empty state if no products
            if ($('#fs-products-tbody tr').length === 0) {
                $('#fs-empty-products').show();
            }
        });

        // Show success toast
        if (typeof Toastify !== 'undefined') {
            Toastify({
                text: "<?php _e('Product removed successfully', 'flashsale-core'); ?>",
                duration: 3000,
                gravity: "top",
                position: "right",
                backgroundColor: "#4CAF50",
                stopOnFocus: true
            }).showToast();
        }
    });
    
    // Discount type toggle
    $(document).on('click', '.fs-discount-type-btn', function() {
        const $btn = $(this);
        const $toggle = $btn.closest('.fs-discount-type-toggle');
        const type = $btn.data('type');
        
        $toggle.find('.fs-discount-type-btn').removeClass('active');
        $btn.addClass('active');
        $toggle.siblings('input[type="hidden"]').val(type);
    });
    
    // Close search results
    $('#fs-close-search').on('click', function() {
        $('#fs-search-results').hide();
    });
    
    // Load category products
    $('#fs-load-category-products').on('click', function() {
        const $btn = $(this);
        const categoryId = $('#fs-category-filter').val();

        if (categoryId) {
            // Show loading state
            $btn.prop('disabled', true);
            $btn.find('.fs-btn-text').hide();
            $btn.find('.fs-loading-spinner').show();

            // Use modified search function for category loading
            loadCategoryProducts(categoryId, $btn);
        } else {
            alert('<?php _e('Please select a category first.', 'flashsale-core'); ?>');
        }
    });

    function loadCategoryProducts(categoryId, $btn) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'fs_search_products',
                query: '',
                category_id: categoryId,
                nonce: '<?php echo wp_create_nonce('fs_search_products'); ?>'
            },
            success: function(response) {
                // Reset button state
                $btn.prop('disabled', false);
                $btn.find('.fs-btn-text').show();
                $btn.find('.fs-loading-spinner').hide();

                if (response.success) {
                    displaySearchResults(response.data);
                } else {
                    displaySearchResults([]);
                }
            },
            error: function() {
                // Reset button state
                $btn.prop('disabled', false);
                $btn.find('.fs-btn-text').show();
                $btn.find('.fs-loading-spinner').hide();
                displaySearchResults([]);
            }
        });
    }

    // Initialize date range pickers
    function initializeDateRangePickers() {
        $('.fs-daterange-picker').each(function() {
            const $input = $(this);

            // Skip if already initialized
            if ($input.data('fs-initialized')) return;
            $input.data('fs-initialized', true);

            // Create a simple date-time picker
            $input.on('click', function() {
                const currentValue = $input.val();
                let startDate = '';
                let endDate = '';

                if (currentValue && currentValue.includes(' - ')) {
                    const parts = currentValue.split(' - ');
                    startDate = parts[0];
                    endDate = parts[1];
                }

                const modal = $(`
                    <div class="fs-datetime-modal">
                        <div class="fs-datetime-content">
                            <h3><?php _e('Select Time Range', 'flashsale-core'); ?></h3>
                            <div class="fs-datetime-fields">
                                <div class="fs-datetime-field">
                                    <label><?php _e('Start Date & Time', 'flashsale-core'); ?></label>
                                    <input type="datetime-local" class="fs-start-datetime" value="${startDate}">
                                </div>
                                <div class="fs-datetime-field">
                                    <label><?php _e('End Date & Time', 'flashsale-core'); ?></label>
                                    <input type="datetime-local" class="fs-end-datetime" value="${endDate}">
                                </div>
                            </div>
                            <div class="fs-datetime-actions">
                                <button type="button" class="fs-btn fs-btn-secondary fs-datetime-cancel"><?php _e('Cancel', 'flashsale-core'); ?></button>
                                <button type="button" class="fs-btn fs-btn-warning fs-datetime-clear"><?php _e('Clear', 'flashsale-core'); ?></button>
                                <button type="button" class="fs-btn fs-btn-primary fs-datetime-apply"><?php _e('Apply', 'flashsale-core'); ?></button>
                            </div>
                        </div>
                    </div>
                `);

                $('body').append(modal);

                // Handle apply button
                modal.find('.fs-datetime-apply').on('click', function() {
                    const start = modal.find('.fs-start-datetime').val();
                    const end = modal.find('.fs-end-datetime').val();

                    if (start && end) {
                        $input.val(start + ' - ' + end);
                    }

                    modal.remove();
                });

                // Handle clear button
                modal.find('.fs-datetime-clear').on('click', function() {
                    $input.val('');
                    modal.remove();
                });

                // Handle cancel button
                modal.find('.fs-datetime-cancel').on('click', function() {
                    modal.remove();
                });

                // Handle click outside
                modal.on('click', function(e) {
                    if (e.target === modal[0]) {
                        modal.remove();
                    }
                });
            });
        });
    }

    // Initialize on page load
    $(document).ready(function() {
        initializeDateRangePickers();

        // Re-initialize when new products are added
        $(document).on('click', '.fs-search-result-item', function() {
            setTimeout(initializeDateRangePickers, 100);
        });
    });
});
</script>
