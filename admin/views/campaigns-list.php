<?php

/**
 * Campaigns list page template
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <div class="flashsale-admin-container">
        <div class="fs-top-page">
            <h1 class="fs-title">
                <span class="fs-icon">🎯</span>
                <?php _e('Flash Sale Campaigns', 'flashsale-core'); ?>
            </h1>
            <div class="fs-top-page-actions">
                <a href="<?php echo admin_url('admin.php?page=flashsale-campaigns&action=new'); ?>" class="fs-btn fs-btn-primary">
                    <span class="dashicons dashicons-plus-alt"></span>
                    <?php _e('New Campaign', 'flashsale-core'); ?>
                </a>
            </div>
        </div>

        <div class="fs-dashboard">
            <div class="fs-card">
                <div class="fs-card-top-page">
                    <h2><?php _e('All Campaigns', 'flashsale-core'); ?></h2>
                    <div class="fs-page-actions">
                        <div class="fs-filters">
                            <select id="fs-status-filter">
                                <option value=""><?php _e('All Status', 'flashsale-core'); ?></option>
                                <option value="1"><?php _e('Active', 'flashsale-core'); ?></option>
                                <option value="0"><?php _e('Inactive', 'flashsale-core'); ?></option>
                            </select>
                            <select id="fs-type-filter">
                                <option value=""><?php _e('All Types', 'flashsale-core'); ?></option>
                                <?php
                                $campaign_types = apply_filters('fs_campaign_types', [
                                    'flash-sale' => [
                                        'name' => __('Flash Sale', 'flashsale-core'),
                                        'description' => __('Flash sale campaigns', 'flashsale-core')
                                    ]
                                ]);
                                foreach ($campaign_types as $type => $config) {
                                    echo '<option value="' . esc_attr($type) . '">' . esc_html($config['name']) . '</option>';
                                }
                                ?>
                            </select>

                            <div class="fs-campaign-type-selector">
                                <form method="GET" action="<?php echo admin_url('admin.php'); ?>" class="fs-inline-form">
                                    <input type="hidden" name="page" value="flashsale-campaigns">
                                    <input type="hidden" name="action" value="new">
                                    <select name="campaign_type" class="fs-select" required>
                                        <option value=""><?php _e('Select Campaign Type', 'flashsale-core'); ?></option>
                                        <?php
                                        foreach ($campaign_types as $type => $config) {
                                            echo '<option value="' . esc_attr($type) . '">' . esc_html($config['name']) . '</option>';
                                        }
                                        ?>
                                    </select>
                                    <button type="submit" class="fs-btn fs-btn-primary">
                                        <span class="dashicons dashicons-plus-alt"></span>
                                        <?php _e('Create Campaign', 'flashsale-core'); ?>
                                    </button>
                                </form>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="fs-card-content">
                    <?php if (!empty($campaigns)) : ?>
                        <div class="fs-campaigns-grid">
                            <?php foreach ($campaigns as $campaign) : ?>
                                <div class="fs-campaign-card" data-status="<?php echo $campaign->status; ?>" data-type="<?php echo $campaign->type; ?>">
                                    <div class="fs-campaign-header">
                                        <h3 class="fs-campaign-title">
                                            <a href="<?php echo admin_url('admin.php?page=flashsale-campaigns&action=edit&campaign_id=' . $campaign->id); ?>">
                                                <?php echo esc_html($campaign->name); ?>
                                            </a>
                                        </h3>
                                        <div class="fs-campaign-status">
                                            <?php if ($campaign->status) : ?>
                                                <span class="fs-status fs-status-active"><?php _e('Active', 'flashsale-core'); ?></span>
                                            <?php else : ?>
                                                <span class="fs-status fs-status-inactive"><?php _e('Inactive', 'flashsale-core'); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="fs-campaign-meta">
                                        <div class="fs-campaign-type">
                                            <span class="fs-meta-label"><?php _e('Type:', 'flashsale-core'); ?></span>
                                            <span class="fs-meta-value"><?php echo esc_html(ucfirst(str_replace('-', ' ', $campaign->type))); ?></span>
                                        </div>
                                        <div class="fs-campaign-priority">
                                            <span class="fs-meta-label"><?php _e('Priority:', 'flashsale-core'); ?></span>
                                            <span class="fs-meta-value"><?php echo $campaign->priority; ?></span>
                                        </div>
                                    </div>

                                    <div class="fs-campaign-dates">
                                        <div class="fs-date-item">
                                            <span class="fs-date-label"><?php _e('Start:', 'flashsale-core'); ?></span>
                                            <span class="fs-date-value">
                                                <?php echo $campaign->start_date ? date('M j, Y H:i', strtotime($campaign->start_date)) : '-'; ?>
                                            </span>
                                        </div>
                                        <div class="fs-date-item">
                                            <span class="fs-date-label"><?php _e('End:', 'flashsale-core'); ?></span>
                                            <span class="fs-date-value">
                                                <?php echo $campaign->end_date ? date('M j, Y H:i', strtotime($campaign->end_date)) : '-'; ?>
                                            </span>
                                        </div>
                                    </div>

                                    <?php if (!empty($campaign->description)) : ?>
                                        <div class="fs-campaign-description">
                                            <?php echo esc_html($campaign->description); ?>
                                        </div>
                                    <?php endif; ?>

                                    <div class="fs-campaign-actions">
                                        <a href="<?php echo admin_url('admin.php?page=flashsale-campaigns&action=edit&campaign_id=' . $campaign->id); ?>" class="fs-btn fs-btn-secondary">
                                            <span class="dashicons dashicons-edit"></span>
                                            <?php _e('Edit', 'flashsale-core'); ?>
                                        </a>

                                        <button class="fs-btn fs-btn-danger fs-delete-campaign" data-campaign-id="<?php echo $campaign->id; ?>" data-campaign-name="<?php echo esc_attr($campaign->name); ?>">
                                            <span class="dashicons dashicons-trash"></span>
                                            <?php _e('Delete', 'flashsale-core'); ?>
                                        </button>

                                        <button class="fs-btn fs-btn-outline fs-toggle-status" data-campaign-id="<?php echo $campaign->id; ?>" data-current-status="<?php echo $campaign->status; ?>">
                                            <?php if ($campaign->status) : ?>
                                                <span class="dashicons dashicons-controls-pause"></span>
                                                <?php _e('Deactivate', 'flashsale-core'); ?>
                                            <?php else : ?>
                                                <span class="dashicons dashicons-controls-play"></span>
                                                <?php _e('Activate', 'flashsale-core'); ?>
                                            <?php endif; ?>
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else : ?>
                        <div class="fs-empty-state">
                            <span class="dashicons dashicons-megaphone"></span>
                            <h3><?php _e('No campaigns found', 'flashsale-core'); ?></h3>
                            <p><?php _e('Create your first flash sale campaign to get started with promotions.', 'flashsale-core'); ?></p>
                            <a href="<?php echo admin_url('admin.php?page=flashsale-campaigns&action=new'); ?>" class="fs-btn fs-btn-primary">
                                <span class="dashicons dashicons-plus-alt"></span>
                                <?php _e('Create First Campaign', 'flashsale-core'); ?>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>



<script>
    jQuery(document).ready(function($) {
        // Filter functionality
        $('#fs-status-filter, #fs-type-filter').on('change', function() {
            var statusFilter = $('#fs-status-filter').val();
            var typeFilter = $('#fs-type-filter').val();

            $('.fs-campaign-card').each(function() {
                var $card = $(this);
                var cardStatus = $card.data('status').toString();
                var cardType = $card.data('type');

                var showCard = true;

                if (statusFilter && cardStatus !== statusFilter) {
                    showCard = false;
                }

                if (typeFilter && cardType !== typeFilter) {
                    showCard = false;
                }

                if (showCard) {
                    $card.show();
                } else {
                    $card.hide();
                }
            });
        });

        // Delete campaign
        $('.fs-delete-campaign').on('click', function() {
            var $btn = $(this);
            var campaignId = $btn.data('campaign-id');
            var campaignName = $btn.data('campaign-name');

            if (confirm('<?php _e('Are you sure you want to delete', 'flashsale-core'); ?> "' + campaignName + '"?')) {
                $btn.prop('disabled', true);

                $.post(fs_admin.ajax_url, {
                    action: 'fs_admin_action',
                    fs_action: 'delete_campaign',
                    campaign_id: campaignId,
                    nonce: fs_admin.nonce
                }, function(response) {
                    if (response.success) {
                        $btn.closest('.fs-campaign-card').fadeOut(function() {
                            $(this).remove();

                            // Check if no campaigns left
                            if ($('.fs-campaign-card').length === 0) {
                                location.reload();
                            }
                        });
                    } else {
                        alert(response.data.message || '<?php _e('Error occurred!', 'flashsale-core'); ?>');
                        $btn.prop('disabled', false);
                    }
                }).fail(function() {
                    alert('<?php _e('Error occurred!', 'flashsale-core'); ?>');
                    $btn.prop('disabled', false);
                });
            }
        });

        // Toggle status
        $('.fs-toggle-status').on('click', function() {
            var $btn = $(this);
            var campaignId = $btn.data('campaign-id');
            var currentStatus = $btn.data('current-status');

            $btn.prop('disabled', true);

            // For now, just show a message since we don't have a toggle status endpoint
            alert('<?php _e('Status toggle functionality will be implemented in a future update.', 'flashsale-core'); ?>');
            $btn.prop('disabled', false);
        });
    });
</script>