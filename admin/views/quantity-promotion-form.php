<?php
/**
 * Quantity Promotion Campaign Type Form - Addon Template
 *
 * @package CanhcamPromotion_Quantity_Promotion
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get existing campaign data if editing
$existing_rules = [];
$existing_products = [];
if (isset($campaign) && $campaign) {
    $database = new FSQP_Database();
    $existing_rules = $database->get_campaign_quantity_rules($campaign->id, false);

    // Group rules by product for multiple ranges per product
    foreach ($existing_rules as $rule) {
        $product_id = $rule->product_id;
        if (!isset($existing_products[$product_id])) {
            $existing_products[$product_id] = [
                'product_id' => $product_id,
                'product_name' => $rule->product_name ?? 'Product #' . $product_id,
                'sku' => $rule->sku ?? '',
                'regular_price' => $rule->regular_price ?? 0,
                'ranges' => []
            ];
        }
        $existing_products[$product_id]['ranges'][] = $rule;
    }
}
?>

<div class="fs-campaign-type-content">
    <div class="fs-section-header">
        <h2><?php _e('Quantity Promotion Products', 'canhcampromotion-quantity-promotion'); ?></h2>
        <p><?php _e('Add products to your quantity promotion campaign and configure multiple quantity ranges with different discounts.', 'canhcampromotion-quantity-promotion'); ?></p>
    </div>

    <!-- Product Search & Selection -->
    <div class="fs-product-search-section">
        <div class="fs-search-bar">
            <div class="fs-search-input-group">
                <input type="text" id="fsqp-product-search" placeholder="<?php _e('Search products by name, SKU, or ID...', 'canhcampromotion-quantity-promotion'); ?>">
                <div class="fs-search-loading">
                    <div class="fs-loading-spinner"></div>
                </div>
                <button type="button" id="fsqp-search-btn" class="fs-btn fs-btn-secondary">
                    <span class="dashicons dashicons-search"></span>
                    <?php _e('Search', 'canhcampromotion-quantity-promotion'); ?>
                </button>
            </div>

            <div class="fs-search-filters">
                <div class="fs-select-wrapper">
                    <select id="fsqp-category-filter" class="fs-select">
                        <option value=""><?php _e('All Categories', 'canhcampromotion-quantity-promotion'); ?></option>
                        <?php
                        $categories = get_terms([
                            'taxonomy' => 'product_cat',
                            'hide_empty' => false,
                            'orderby' => 'name'
                        ]);
                        foreach ($categories as $category) {
                            echo '<option value="' . $category->term_id . '">' . esc_html($category->name) . '</option>';
                        }
                        ?>
                    </select>
                </div>

                <button type="button" id="fsqp-load-category-products" class="fs-btn fs-btn-outline">
                    <span class="fs-btn-text"><?php _e('Load Category Products', 'canhcampromotion-quantity-promotion'); ?></span>
                    <span class="fs-loading-spinner" style="display: none;"></span>
                </button>
            </div>
        </div>

        <!-- Search Results -->
        <div id="fsqp-search-results" class="fs-search-results" style="display: none;">
            <div class="fs-search-results-header">
                <h4><?php _e('Search Results', 'canhcampromotion-quantity-promotion'); ?></h4>
                <button type="button" id="fsqp-close-search" class="fs-btn fs-btn-small">×</button>
            </div>
            <div class="fs-search-results-list"></div>
        </div>
    </div>

    <!-- Campaign Products Section -->
    <div class="fs-products-section">
        <div class="fs-section-header">
            <h3><?php _e('Campaign Products', 'canhcampromotion-quantity-promotion'); ?>
                <span class="fs-product-count">(<?php echo !empty($existing_products) ? count($existing_products) : 0; ?> <?php _e('products', 'canhcampromotion-quantity-promotion'); ?>)</span>
            </h3>
            <div class="fs-section-actions">
                <button type="button" id="fsqp-bulk-actions-btn" class="fs-btn fs-btn-outline" disabled>
                    <?php _e('Bulk Actions', 'canhcampromotion-quantity-promotion'); ?>
                </button>
            </div>
        </div>

        <!-- Products Table Container -->
        <div class="fs-products-container">
            <div class="fs-products-table-wrapper">
                <table id="fsqp-products-table" class="fs-table fs-products-table">
                    <thead>
                        <tr>
                            <th width="40px" class="fs-checkbox-column">
                                <div class="fs-checkbox-wrapper">
                                    <input type="checkbox" id="fsqp-select-all-products" class="fs-checkbox">
                                    <label for="fsqp-select-all-products" class="fs-checkbox-label"></label>
                                </div>
                            </th>
                            <th class="fs-product-info-column"><?php _e('Product Information & Quantity Ranges', 'canhcampromotion-quantity-promotion'); ?></th>
                            <th width="100px" class="fs-price-column"><?php _e('Regular Price', 'canhcampromotion-quantity-promotion'); ?></th>
                            <th width="80px" class="fs-actions-column"><?php _e('Actions', 'canhcampromotion-quantity-promotion'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="fsqp-products-tbody">
                    <?php if (!empty($existing_products)): ?>
                        <?php foreach ($existing_products as $product_data): ?>
                            <?php
                            $product = wc_get_product($product_data['product_id']);
                            if (!$product) continue;
                            ?>
                            <tr data-product-id="<?php echo $product_data['product_id']; ?>" class="fs-product-row">
                                <td class="fs-checkbox-column">
                                    <div class="fs-checkbox-wrapper">
                                        <input type="checkbox" class="fs-product-checkbox fs-checkbox"
                                               id="product-<?php echo $product_data['product_id']; ?>"
                                               value="<?php echo $product_data['product_id']; ?>">
                                        <label for="product-<?php echo $product_data['product_id']; ?>" class="fs-checkbox-label"></label>
                                    </div>
                                </td>
                                <td class="fs-product-info-cell">
                                    <div class="fs-product-info-enhanced">
                                        <div class="fs-product-image">
                                            <?php
                                            $image = wp_get_attachment_image_src(get_post_thumbnail_id($product->get_id()), 'thumbnail');
                                            if ($image): ?>
                                                <img src="<?php echo $image[0]; ?>" alt="<?php echo esc_attr($product->get_name()); ?>" class="fs-product-thumb">
                                            <?php else: ?>
                                                <div class="fs-product-thumb-placeholder">
                                                    <span class="dashicons dashicons-products"></span>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="fs-product-details">
                                            <strong class="fs-product-name"><?php echo esc_html($product_data['product_name']); ?></strong>
                                            <div class="fs-product-meta">
                                                <span class="fs-product-sku">SKU: <?php echo $product->get_sku() ?: '-'; ?></span>
                                                <span class="fs-product-id">ID: <?php echo $product_data['product_id']; ?></span>
                                                <?php if ($product->get_stock_status() === 'instock'): ?>
                                                    <span class="fs-stock-status fs-in-stock"><?php _e('In Stock', 'canhcampromotion-quantity-promotion'); ?></span>
                                                <?php else: ?>
                                                    <span class="fs-stock-status fs-out-of-stock"><?php _e('Out of Stock', 'canhcampromotion-quantity-promotion'); ?></span>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Quantity Ranges moved here -->
                                            <div class="fsqp-quantity-ranges" data-product-id="<?php echo $product_data['product_id']; ?>">
                                                <?php foreach ($product_data['ranges'] as $range_index => $range): ?>
                                                    <div class="fsqp-range-item-horizontal" data-range-index="<?php echo $range_index; ?>">
                                                        <div class="fsqp-range-controls-horizontal">
                                                            <div class="fsqp-range-field-inline">
                                                                <label><?php _e('Qty', 'canhcampromotion-quantity-promotion'); ?></label>
                                                                <input type="number"
                                                                       name="products[<?php echo $product_data['product_id']; ?>][ranges][<?php echo $range_index; ?>][min_quantity]"
                                                                       value="<?php echo $range->min_quantity; ?>"
                                                                       min="1"
                                                                       class="fs-input-tiny"
                                                                       required>
                                                                <span class="fsqp-range-separator">-</span>
                                                                <input type="number"
                                                                       name="products[<?php echo $product_data['product_id']; ?>][ranges][<?php echo $range_index; ?>][max_quantity]"
                                                                       value="<?php echo $range->max_quantity; ?>"
                                                                       min="0"
                                                                       class="fs-input-tiny"
                                                                       placeholder="∞">
                                                            </div>
                                                            <div class="fsqp-range-field-inline">
                                                                <label><?php _e('Discount', 'canhcampromotion-quantity-promotion'); ?></label>
                                                                <div class="fs-discount-controls-inline">
                                                                    <div class="fs-discount-type-toggle-small">
                                                                        <button type="button" class="fs-discount-type-btn-small <?php echo $range->discount_type == 1 ? 'active' : ''; ?>" data-type="1">%</button>
                                                                        <button type="button" class="fs-discount-type-btn-small <?php echo $range->discount_type == 2 ? 'active' : ''; ?>" data-type="2">$</button>
                                                                    </div>
                                                                    <input type="number"
                                                                           name="products[<?php echo $product_data['product_id']; ?>][ranges][<?php echo $range_index; ?>][discount_value]"
                                                                           value="<?php echo $range->discount_value; ?>"
                                                                           step="0.01"
                                                                           min="0"
                                                                           class="fs-input-tiny fs-discount-input"
                                                                           required>
                                                                    <input type="hidden"
                                                                           name="products[<?php echo $product_data['product_id']; ?>][ranges][<?php echo $range_index; ?>][discount_type]"
                                                                           value="<?php echo $range->discount_type; ?>">
                                                                </div>
                                                            </div>
                                                            <div class="fsqp-range-field-inline">
                                                                <label><?php _e('Max Disc.', 'canhcampromotion-quantity-promotion'); ?></label>
                                                                <input type="number"
                                                                       name="products[<?php echo $product_data['product_id']; ?>][ranges][<?php echo $range_index; ?>][max_discount_amount]"
                                                                       value="<?php echo $range->max_discount_amount; ?>"
                                                                       step="0.01"
                                                                       min="0"
                                                                       class="fs-input-tiny"
                                                                       placeholder="0">
                                                            </div>
                                                            <div class="fsqp-range-actions-inline">
                                                                <button type="button" class="fs-btn fs-btn-danger fs-btn-tiny fsqp-remove-range" title="<?php _e('Remove range', 'canhcampromotion-quantity-promotion'); ?>">
                                                                    <span class="dashicons dashicons-trash"></span>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endforeach; ?>
                                                <div class="fsqp-add-range-inline">
                                                    <button type="button" class="fs-btn fs-btn-secondary fs-btn-small fsqp-add-range-btn" data-product-id="<?php echo $product_data['product_id']; ?>">
                                                        <span class="dashicons dashicons-plus-alt"></span>
                                                        <?php _e('Add Range', 'canhcampromotion-quantity-promotion'); ?>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="fs-price-cell">
                                    <span class="fs-regular-price"><?php echo $product_data['regular_price'] ? number_format($product_data['regular_price'], 2) : '-'; ?></span>
                                </td>
                                <td class="fs-actions-cell">
                                    <button type="button" class="fs-btn fs-btn-danger fs-btn-small fsqp-remove-product" title="<?php _e('Remove product', 'canhcampromotion-quantity-promotion'); ?>">
                                        <span class="dashicons dashicons-trash"></span>
                                    </button>
                                </td>
                            </tr>
                            <input type="hidden" name="products[<?php echo $product_data['product_id']; ?>][product_id]" value="<?php echo $product_data['product_id']; ?>">
                        <?php endforeach; ?>
                    <?php endif; ?>
                    </tbody>
                </table>

                <?php if (empty($existing_products)): ?>
                    <div id="fsqp-empty-products" class="fs-empty-state">
                        <span class="dashicons dashicons-chart-bar"></span>
                        <h3><?php _e('No products added yet', 'canhcampromotion-quantity-promotion'); ?></h3>
                        <p><?php _e('Search and add products to your quantity promotion campaign.', 'canhcampromotion-quantity-promotion'); ?></p>
                    </div>
                <?php endif; ?>
            </div> <!-- End products-table-wrapper -->
        </div> <!-- End products-container -->
    </div> <!-- End products-section -->
</div>



<script>
jQuery(document).ready(function($) {
    // Initialize product search using shared library
    if (window.FSProductSearch) {
        FSProductSearch.init({
            searchInputId: '#fsqp-product-search',
            searchButtonId: '#fsqp-search-btn',
            resultsContainerId: '.fs-search-results-list',
            categoryFilterId: '#fsqp-category-filter',
            searchAction: 'fsqp_search_products',
            nonce: '<?php echo wp_create_nonce('fsqp_search_products'); ?>',
            templates: {
                resultItem: function(product) {
                    return `
                        <div class="fs-search-result-item" data-product-id="${product.id}">
                            <div class="fs-product-info">
                                <strong>${product.name}</strong>
                                <div class="fs-product-meta">
                                    <span>SKU: ${product.sku || '-'}</span>
                                    <span>ID: ${product.id}</span>
                                    <span>Price: ${product.price}</span>
                                </div>
                            </div>
                            <button type="button" class="fs-btn fs-btn-primary fs-btn-small fsqp-add-product">
                                <?php _e('Add', 'canhcampromotion-quantity-promotion'); ?>
                            </button>
                        </div>
                    `;
                },
                noResults: '<p><?php _e('No products found.', 'canhcampromotion-quantity-promotion'); ?></p>'
            },
            callbacks: {
                onProductAdd: function(productId, $item, $button) {
                    handleQuantityPromotionProductAdd(productId, $item, $button);
                },
                onSearchStart: function(query) {
                    $('#fsqp-search-results').hide();
                },
                onShowResults: function() {
                    $('#fsqp-search-results').show();
                },
                onHideResults: function() {
                    $('#fsqp-search-results').hide();
                }
            }
        });
    }


    // Add product to campaign
    $(document).on('click', '.fsqp-add-product', function() {
        const $item = $(this).closest('.fs-search-result-item');
        const productId = $item.data('product-id');
        const $button = $(this);

        // Check if product already added to current campaign
        if ($(`#fsqp-products-tbody tr[data-product-id="${productId}"]`).length > 0) {
            alert('<?php _e('Product already added to campaign.', 'canhcampromotion-quantity-promotion'); ?>');
            return;
        }

        // Check for conflicts with other campaigns
        $button.prop('disabled', true).text('<?php _e('Checking...', 'canhcampromotion-quantity-promotion'); ?>');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'fsqp_check_product_conflicts',
                product_id: productId,
                exclude_campaign_id: $('#campaign_id').val() || 0,
                nonce: '<?php echo wp_create_nonce('fsqp_check_product_conflicts'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    if (response.data.has_conflicts) {
                        const conflicts = response.data.conflicts;
                        let message = '<?php _e('This product is already in the following campaigns:', 'canhcampromotion-quantity-promotion'); ?>\n\n';
                        conflicts.forEach(function(conflict) {
                            message += `• ${conflict.campaign_name} (Priority: ${conflict.priority})\n`;
                        });
                        message += '\n<?php _e('Do you want to continue anyway?', 'canhcampromotion-quantity-promotion'); ?>';

                        if (confirm(message)) {
                            addProductToTable(productId, $item);
                        }
                    } else {
                        addProductToTable(productId, $item);
                    }
                } else {
                    alert(response.data.message || '<?php _e('Error checking product conflicts', 'canhcampromotion-quantity-promotion'); ?>');
                }
            },
            error: function() {
                alert('<?php _e('Error checking product conflicts', 'canhcampromotion-quantity-promotion'); ?>');
            },
            complete: function() {
                $button.prop('disabled', false).text('<?php _e('Add', 'canhcampromotion-quantity-promotion'); ?>');
            }
        });
    });

    function addProductToTable(productId, $searchItem) {
        // Hide empty state
        $('#fsqp-empty-products').hide();

        // Get product data from search item
        const productName = $searchItem.find('strong').text();
        const productMeta = $searchItem.find('.fs-product-meta').html();
        const productPrice = $searchItem.find('.fs-product-meta').text().match(/Price: ([0-9.,]+)/);
        const displayPrice = productPrice ? productPrice[1] : '-';

        // Get additional product info via AJAX for accurate data
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'fsqp_get_product_info',
                product_id: productId,
                nonce: '<?php echo wp_create_nonce('fsqp_get_product_info'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    const product = response.data;
                    updateProductRow(productId, product);
                }
            }
        });

        const $newRow = $(`
            <tr data-product-id="${productId}" class="fs-product-row">
                <td class="fs-checkbox-column">
                    <div class="fs-checkbox-wrapper">
                        <input type="checkbox" class="fs-product-checkbox fs-checkbox" id="product-${productId}" value="${productId}">
                        <label for="product-${productId}" class="fs-checkbox-label"></label>
                    </div>
                </td>
                <td class="fs-product-info-cell">
                    <div class="fs-product-info-enhanced">
                        <div class="fs-product-image">
                            <div class="fs-product-thumb-placeholder">
                                <span class="dashicons dashicons-products"></span>
                            </div>
                        </div>
                        <div class="fs-product-details">
                            <strong class="fs-product-name">${productName}</strong>
                            <div class="fs-product-meta">${productMeta}</div>
                            <div class="fsqp-quantity-ranges" data-product-id="${productId}">
                                <div class="fsqp-add-range-inline">
                                    <button type="button" class="fs-btn fs-btn-secondary fs-btn-small fsqp-add-range-btn" data-product-id="${productId}">
                                        <span class="dashicons dashicons-plus-alt"></span>
                                        <?php _e('Add Range', 'canhcampromotion-quantity-promotion'); ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </td>
                <td class="fs-price-cell">
                    <span class="fs-regular-price">${displayPrice}</span>
                </td>
                <td class="fs-actions-cell">
                    <button type="button" class="fs-btn fs-btn-danger fs-btn-small fsqp-remove-product" title="<?php _e('Remove product', 'canhcampromotion-quantity-promotion'); ?>">
                        <span class="dashicons dashicons-trash"></span>
                    </button>
                </td>
            </tr>
            <input type="hidden" name="products[${productId}][product_id]" value="${productId}">
        `);

        $('#fsqp-products-tbody').append($newRow);
        updateProductCount();
    }

    function updateProductRow(productId, product) {
        const $row = $(`tr[data-product-id="${productId}"]`);
        if ($row.length) {
            // Update product name with variation info
            $row.find('.fs-product-name').text(product.name);
            
            // Update price (numeric only)
            const price = product.price_raw || product.price || '-';
            $row.find('.fs-regular-price').text(price !== '-' ? parseFloat(price).toFixed(2) : '-');

            // Update product image if available
            if (product.image_url) {
                $row.find('.fs-product-thumb-placeholder').replaceWith(
                    `<img src="${product.image_url}" alt="${product.name}" class="fs-product-thumb">`
                );
            }

            // Update stock status and variation info
            let stockHtml = product.in_stock ?
                '<span class="fs-stock-status fs-in-stock"><?php _e('In Stock', 'canhcampromotion-quantity-promotion'); ?></span>' :
                '<span class="fs-stock-status fs-out-of-stock"><?php _e('Out of Stock', 'canhcampromotion-quantity-promotion'); ?></span>';
                
            // Add variation indicator
            if (product.is_variation) {
                stockHtml += '<span class="fs-variation-badge"><?php _e('Variation', 'canhcampromotion-quantity-promotion'); ?></span>';
            } else if (product.type === 'variable') {
                stockHtml += '<span class="fs-variable-badge"><?php _e('Variable Product', 'canhcampromotion-quantity-promotion'); ?></span>';
            }

            $row.find('.fs-product-meta').append(stockHtml);
        }
    }

    function updateProductCount() {
        const count = $('#fsqp-products-tbody tr').length;
        $('.fs-product-count').text(`(${count} <?php _e('products', 'canhcampromotion-quantity-promotion'); ?>)`);
    }

    // Initialize bulk actions using shared library from core plugin
    if (window.FSBulkActions) {
        FSBulkActions.init({
            container: '.fs-products-section',
            selectAllId: '#fsqp-select-all-products',
            checkboxClass: '.fs-product-checkbox',
            bulkButtonId: '#fsqp-bulk-actions-btn',
            bulkMenuId: '#fsqp-bulk-actions-menu',
            actions: {
                'delete': {
                    label: '<?php _e('Delete Selected', 'canhcampromotion-quantity-promotion'); ?>',
                    icon: 'dashicons-trash',
                    class: 'fs-bulk-delete',
                    confirm: '<?php _e('Are you sure you want to delete the selected products?', 'canhcampromotion-quantity-promotion'); ?>'
                }
            },
            callbacks: {
                onBulkAction: function(action, selectedItems) {
                    handleQuantityPromotionBulkAction(action, selectedItems);
                }
            }
        });
    }

    // Handle quantity promotion specific bulk actions
    function handleQuantityPromotionBulkAction(action, selectedItems) {
        switch (action) {
            case 'delete':
                bulkDeleteProducts(selectedItems);
                break;
        }
    }

    function bulkDeleteProducts(selectedItems) {
        selectedItems.forEach(item => {
            $(item.row).fadeOut(300, function() {
                $(this).remove();
                updateProductCount();
            });
        });

        showMessage('<?php _e('Selected products deleted successfully.', 'canhcampromotion-quantity-promotion'); ?>', 'success');
    }



    // Remove product
    $(document).on('click', '.fsqp-remove-product', function() {
        $(this).closest('tr').remove();
        updateProductCount();

        // Show empty state if no products
        if ($('#fsqp-products-tbody tr').length === 0) {
            $('#fsqp-empty-products').show();
        }
    });

    // Discount type toggle
    $(document).on('click', '.fs-discount-type-btn, .fs-discount-type-btn-small', function() {
        const $btn = $(this);
        const $toggle = $btn.closest('.fs-discount-type-toggle, .fs-discount-type-toggle-small');
        const type = $btn.data('type');

        $toggle.find('.fs-discount-type-btn, .fs-discount-type-btn-small').removeClass('active');
        $btn.addClass('active');
        $toggle.siblings('input[type="hidden"]').val(type);
    });

    // Add quantity range
    $(document).on('click', '.fsqp-add-range-btn', function() {
        const productId = $(this).data('product-id');
        const $rangesContainer = $(`.fsqp-quantity-ranges[data-product-id="${productId}"]`);
        const rangeIndex = $rangesContainer.find('.fsqp-range-item-horizontal').length;

        const rangeHtml = `
            <div class="fsqp-range-item-horizontal" data-range-index="${rangeIndex}">
                <div class="fsqp-range-controls-horizontal">
                    <div class="fsqp-range-field-inline">
                        <label><?php _e('Qty', 'canhcampromotion-quantity-promotion'); ?></label>
                        <input type="number" name="products[${productId}][ranges][${rangeIndex}][min_quantity]"
                               value="1" min="1" class="fs-input-tiny" required>
                        <span class="fsqp-range-separator">-</span>
                        <input type="number" name="products[${productId}][ranges][${rangeIndex}][max_quantity]"
                               value="" min="0" class="fs-input-tiny" placeholder="∞">
                    </div>
                    <div class="fsqp-range-field-inline">
                        <label><?php _e('Discount', 'canhcampromotion-quantity-promotion'); ?></label>
                        <div class="fs-discount-controls-inline">
                            <div class="fs-discount-type-toggle-small">
                                <button type="button" class="fs-discount-type-btn-small active" data-type="1">%</button>
                                <button type="button" class="fs-discount-type-btn-small" data-type="2">$</button>
                            </div>
                            <input type="number" name="products[${productId}][ranges][${rangeIndex}][discount_value]"
                                   value="" step="0.01" min="0" class="fs-input-tiny fs-discount-input" required>
                            <input type="hidden" name="products[${productId}][ranges][${rangeIndex}][discount_type]" value="1">
                        </div>
                    </div>
                    <div class="fsqp-range-field-inline">
                        <label><?php _e('Max Disc.', 'canhcampromotion-quantity-promotion'); ?></label>
                        <input type="number" name="products[${productId}][ranges][${rangeIndex}][max_discount_amount]"
                               value="" step="0.01" min="0" class="fs-input-tiny" placeholder="0">
                    </div>
                    <div class="fsqp-range-actions-inline">
                        <button type="button" class="fs-btn fs-btn-danger fs-btn-tiny fsqp-remove-range" title="<?php _e('Remove range', 'canhcampromotion-quantity-promotion'); ?>">
                            <span class="dashicons dashicons-trash"></span>
                        </button>
                    </div>
                </div>
            </div>
        `;

        $rangesContainer.find('.fsqp-add-range-inline').before(rangeHtml);
    });

    // Remove quantity range
    $(document).on('click', '.fsqp-remove-range', function() {
        $(this).closest('.fsqp-range-item, .fsqp-range-item-horizontal').remove();
    });

    // Close search results
    $('#fsqp-close-search').on('click', function() {
        $('#fsqp-search-results').hide();
    });

    // Load category products
    $('#fsqp-load-category-products').on('click', function() {
        const $btn = $(this);
        const categoryId = $('#fsqp-category-filter').val();

        if (categoryId) {
            // Show loading state
            $btn.prop('disabled', true);
            $btn.find('.fs-btn-text').hide();
            $btn.find('.fs-loading-spinner').show();

            // Use modified search function for category loading
            loadCategoryProducts(categoryId, $btn);
        } else {
            alert('<?php _e('Please select a category first.', 'canhcampromotion-quantity-promotion'); ?>');
        }
    });

    function loadCategoryProducts(categoryId, $btn) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'fsqp_search_products',
                query: '',
                category_id: categoryId,
                nonce: '<?php echo wp_create_nonce('fsqp_search_products'); ?>'
            },
            success: function(response) {
                // Reset button state
                $btn.prop('disabled', false);
                $btn.find('.fs-btn-text').show();
                $btn.find('.fs-loading-spinner').hide();

                if (response.success) {
                    displaySearchResults(response.data);
                } else {
                    displaySearchResults([]);
                }
            },
            error: function() {
                // Reset button state
                $btn.prop('disabled', false);
                $btn.find('.fs-btn-text').show();
                $btn.find('.fs-loading-spinner').hide();
                displaySearchResults([]);
            }
        });
    }

    // Hide search results when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.fs-product-search-section').length) {
            $('#fsqp-search-results').hide();
        }
    });

    function displaySearchResults(products) {
        const $results = $('#fsqp-search-results .fsqp-search-results-list');
        $results.empty();

        if (products.length === 0) {
            $results.html('<p><?php _e('No products found.', 'canhcampromotion-quantity-promotion'); ?></p>');
        } else {
            products.forEach(product => {
                // Check if product already exists
                const exists = $(`#fsqp-products-tbody tr[data-product-id="${product.id}"]`).length > 0;

                // Add variation indicator to the product meta
                let variationInfo = '';
                if (product.is_variation) {
                    variationInfo = '<span class="fs-variation-indicator"><?php _e('Variation', 'canhcampromotion-quantity-promotion'); ?></span>';
                } else if (product.type === 'variable') {
                    variationInfo = '<span class="fs-variable-indicator"><?php _e('Variable Product', 'canhcampromotion-quantity-promotion'); ?></span>';
                }

                const $item = $(`
                    <div class="fsqp-search-result-item" data-product-id="${product.id}">
                        <div class="fsqp-product-info">
                            <strong>${product.name}</strong>
                            <div class="fsqp-product-meta">
                                <span>SKU: ${product.sku || '-'}</span>
                                <span>ID: ${product.id}</span>
                                <span>Price: $${parseFloat(product.price || 0).toFixed(2)}</span>
                                ${variationInfo}
                            </div>
                        </div>
                        <button type="button" class="fs-btn ${exists ? 'fs-btn-disabled' : 'fs-btn-primary'} fs-btn-small fsqp-add-product" ${exists ? 'disabled' : ''}>
                            ${exists ? '<?php _e('Added', 'canhcampromotion-quantity-promotion'); ?>' : '<?php _e('Add', 'canhcampromotion-quantity-promotion'); ?>'}
                        </button>
                    </div>
                `);
                $results.append($item);
            });
        }

        $('#fsqp-search-results').show();
    }
});
</script>
