<?php
/**
 * Quantity Promotion Campaign Form - Core Style
 *
 * @package FlashSale_Quantity_Promotion
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get existing campaign data if editing
$existing_rules = [];
$existing_products = [];
if (isset($campaign) && $campaign) {
    $database = new FSQP_Database();
    $existing_rules = $database->get_product_quantity_rules($campaign->id, false);

    // Group rules by product for multiple ranges per product
    foreach ($existing_rules as $rule) {
        $product_id = $rule->product_id;
        if (!isset($existing_products[$product_id])) {
            $product = wc_get_product($product_id);
            $existing_products[$product_id] = [
                'product_id' => $product_id,
                'product_name' => $product ? $product->get_name() : 'Product #' . $product_id,
                'sku' => $product ? $product->get_sku() : '',
                'regular_price' => $product ? $product->get_regular_price() : 0,
                'ranges' => []
            ];
        }
        $existing_products[$product_id]['ranges'][] = $rule;
    }
}
?>

<div class="fs-campaign-type-content">
    <div class="fs-section-header">
        <h2><?php _e('Quantity Promotion Products', 'canhcampromotion-quantity-promotion'); ?></h2>
        <p><?php _e('Add products to your quantity promotion campaign and configure multiple quantity ranges with different discounts.', 'canhcampromotion-quantity-promotion'); ?></p>
    </div>

    <!-- Product Search & Selection -->
    <div class="fs-product-search-section">
        <div class="fs-search-bar">
            <div class="fs-search-input-group">
                <input type="text" id="fsqp-product-search" placeholder="<?php _e('Search products by name, SKU, or ID...', 'canhcampromotion-quantity-promotion'); ?>">
                <div class="fs-search-loading">
                    <div class="fs-loading-spinner"></div>
                </div>
                <button type="button" id="fsqp-search-btn" class="fs-btn fs-btn-secondary">
                    <span class="dashicons dashicons-search"></span>
                    <?php _e('Search', 'canhcampromotion-quantity-promotion'); ?>
                </button>
            </div>

            <div class="fs-search-filters">
                <div class="fs-select-wrapper">
                    <select id="fsqp-category-filter" class="fs-select">
                        <option value=""><?php _e('All Categories', 'canhcampromotion-quantity-promotion'); ?></option>
                        <?php
                        $categories = get_terms([
                            'taxonomy' => 'product_cat',
                            'hide_empty' => false,
                            'orderby' => 'name'
                        ]);
                        foreach ($categories as $category) {
                            echo '<option value="' . $category->term_id . '">' . esc_html($category->name) . '</option>';
                        }
                        ?>
                    </select>
                </div>

                <button type="button" id="fsqp-load-category-products" class="fs-btn fs-btn-outline">
                    <span class="fs-btn-text"><?php _e('Load Category Products', 'canhcampromotion-quantity-promotion'); ?></span>
                    <span class="fs-loading-spinner" style="display: none;"></span>
                </button>
            </div>
        </div>

        <!-- Search Results -->
        <div id="fsqp-search-results" class="fs-search-results" style="display: none;">
            <div class="fs-search-results-header">
                <h4><?php _e('Search Results', 'canhcampromotion-quantity-promotion'); ?></h4>
                <button type="button" id="fsqp-close-search" class="fs-btn fs-btn-small">×</button>
            </div>
            <div class="fsqp-search-results-list"></div>
        </div>
    </div>
    <!-- Campaign Products Section -->
    <div class="fs-products-section">
        <div class="fs-section-header">
            <h3><?php _e('Campaign Products', 'canhcampromotion-quantity-promotion'); ?>
                <span class="fs-product-count" id="fsqp-products-count">(<?php echo !empty($existing_products) ? count($existing_products) : 0; ?> <?php _e('products', 'canhcampromotion-quantity-promotion'); ?>)</span>
            </h3>
            <div class="fs-section-actions">
                <button type="button" id="fsqp-bulk-actions-btn" class="fs-btn fs-btn-outline" disabled>
                    <?php _e('Bulk Actions', 'canhcampromotion-quantity-promotion'); ?>
                </button>
            </div>
        </div>

        <!-- Products Table Container -->
        <div class="fs-products-container">
            <div class="fs-products-table-wrapper">
                <table id="fsqp-products-table" class="fs-table fs-products-table">
                    <thead>
                        <tr>
                            <th width="40px" class="fs-checkbox-column">
                                <div class="fs-checkbox-wrapper">
                                    <input type="checkbox" id="fsqp-select-all-products" class="fs-checkbox">
                                    <label for="fsqp-select-all-products" class="fs-checkbox-label"></label>
                                </div>
                            </th>
                            <th width="280px" class="fs-product-info-column"><?php _e('Product Information', 'canhcampromotion-quantity-promotion'); ?></th>
                            <th width="100px" class="fs-price-column"><?php _e('Regular Price', 'canhcampromotion-quantity-promotion'); ?></th>
                            <th class="fs-ranges-column"><?php _e('Quantity Ranges', 'canhcampromotion-quantity-promotion'); ?></th>
                            <th width="80px" class="fs-actions-column"><?php _e('Actions', 'canhcampromotion-quantity-promotion'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="fsqp-products-tbody">
                    <?php if (!empty($existing_products)): ?>
                        <?php foreach ($existing_products as $product_data): ?>
                            <?php
                            $product = wc_get_product($product_data['product_id']);
                            if (!$product) continue;
                            ?>
                            <tr data-product-id="<?php echo $product_data['product_id']; ?>" class="fsqp-product-row">
                                <td class="fs-checkbox-cell">
                                    <div class="fs-checkbox-wrapper">
                                        <input type="checkbox" class="fsqp-product-checkbox fs-checkbox"
                                               id="product-<?php echo $product_data['product_id']; ?>"
                                               value="<?php echo $product_data['product_id']; ?>">
                                        <label for="product-<?php echo $product_data['product_id']; ?>" class="fs-checkbox-label"></label>
                                    </div>
                                </td>
                                <td class="fs-product-info-cell">
                                    <div class="fs-product-info-enhanced">
                                        <div class="fs-product-image">
                                            <?php
                                            $image = wp_get_attachment_image_src(get_post_thumbnail_id($product->get_id()), 'thumbnail');
                                            if ($image): ?>
                                                <img src="<?php echo $image[0]; ?>" alt="<?php echo esc_attr($product->get_name()); ?>" class="fs-product-thumb">
                                            <?php else: ?>
                                                <div class="fs-product-thumb-placeholder">
                                                    <span class="dashicons dashicons-products"></span>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="fs-product-details">
                                            <strong class="fs-product-name"><?php echo esc_html($product->get_name()); ?></strong>
                                            <div class="fs-product-meta">
                                                <span class="fs-product-sku">SKU: <?php echo $product->get_sku() ?: '-'; ?></span>
                                                <span class="fs-product-id">ID: <?php echo $product->get_id(); ?></span>
                                                <?php if ($product->get_stock_status() === 'instock'): ?>
                                                    <span class="fs-stock-status fs-in-stock"><?php _e('In Stock', 'canhcampromotion-quantity-promotion'); ?></span>
                                                <?php else: ?>
                                                    <span class="fs-stock-status fs-out-of-stock"><?php _e('Out of Stock', 'canhcampromotion-quantity-promotion'); ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="fs-price-cell">
                                    <span class="fs-regular-price">$<?php echo $product->get_regular_price() ? number_format($product->get_regular_price(), 2) : '-'; ?></span>
                                </td>
                                <td class="fs-ranges-cell">
                                    <div class="fsqp-quantity-ranges" data-product-id="<?php echo $product_data['product_id']; ?>">
                                        <?php if (!empty($product_data['ranges'])): ?>
                                            <?php foreach ($product_data['ranges'] as $range): ?>
                                                <div class="fsqp-range-item" data-range-index="<?php echo $range->id; ?>">
                                                    <div class="fsqp-range-controls">
                                                        <div class="fsqp-range-field">
                                                            <label>Min Qty</label>
                                                            <input type="number"
                                                                   name="products[<?php echo $product_data['product_id']; ?>][ranges][<?php echo $range->id; ?>][min_quantity]"
                                                                   value="<?php echo $range->min_quantity; ?>"
                                                                   min="1"
                                                                   class="fs-input-small"
                                                                   required>
                                                        </div>
                                                        <div class="fsqp-range-field">
                                                            <label>Max Qty</label>
                                                            <input type="number"
                                                                   name="products[<?php echo $product_data['product_id']; ?>][ranges][<?php echo $range->id; ?>][max_quantity]"
                                                                   value="<?php echo $range->max_quantity ?: ''; ?>"
                                                                   min="0"
                                                                   class="fs-input-small"
                                                                   placeholder="∞">
                                                        </div>
                                                        <div class="fsqp-range-field">
                                                            <label>Discount</label>
                                                            <div class="fs-discount-controls">
                                                                <div class="fs-discount-type-toggle">
                                                                    <button type="button" class="fs-discount-type-btn <?php echo ($range->discount_type == 1) ? 'active' : ''; ?>" data-type="1">%</button>
                                                                    <button type="button" class="fs-discount-type-btn <?php echo ($range->discount_type == 2) ? 'active' : ''; ?>" data-type="2">$</button>
                                                                </div>
                                                                <input type="number"
                                                                       name="products[<?php echo $product_data['product_id']; ?>][ranges][<?php echo $range->id; ?>][discount_value]"
                                                                       value="<?php echo $range->discount_value; ?>"
                                                                       step="0.01"
                                                                       min="0"
                                                                       class="fs-input-small fs-discount-input"
                                                                       required>
                                                                <input type="hidden"
                                                                       name="products[<?php echo $product_data['product_id']; ?>][ranges][<?php echo $range->id; ?>][discount_type]"
                                                                       value="<?php echo $range->discount_type; ?>">
                                                            </div>
                                                        </div>
                                                        <div class="fsqp-range-field">
                                                            <label>Max Discount</label>
                                                            <input type="number"
                                                                   name="products[<?php echo $product_data['product_id']; ?>][ranges][<?php echo $range->id; ?>][max_discount_amount]"
                                                                   value="<?php echo $range->max_discount_amount ?: ''; ?>"
                                                                   step="0.01"
                                                                   min="0"
                                                                   class="fs-input-small"
                                                                   placeholder="0">
                                                        </div>
                                                        <div class="fsqp-range-actions">
                                                            <button type="button" class="fs-btn fs-btn-danger fs-btn-small fsqp-remove-range" title="Remove range">
                                                                <span class="dashicons dashicons-trash"></span>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                        <div class="fsqp-add-range">
                                            <button type="button" class="fs-btn fs-btn-secondary fs-btn-small fsqp-add-range-btn" data-product-id="<?php echo $product_data['product_id']; ?>">
                                                <span class="dashicons dashicons-plus-alt"></span>
                                                Add Range
                                            </button>
                                        </div>
                                    </div>
                                </td>
                                <td class="fs-actions-cell">
                                    <button type="button" class="fs-btn fs-btn-danger fs-btn-small fsqp-remove-product"
                                            title="<?php _e('Remove product', 'canhcampromotion-quantity-promotion'); ?>">
                                        <span class="dashicons dashicons-trash"></span>
                                    </button>
                                </td>
                            </tr>
                            <input type="hidden" name="products[<?php echo $product_data['product_id']; ?>][product_id]" value="<?php echo $product_data['product_id']; ?>">
                        <?php endforeach; ?>
                    <?php endif; ?>
                    </tbody>
                </table>

                <?php if (empty($existing_products)): ?>
                    <div id="fsqp-empty-products" class="fs-empty-state">
                        <span class="dashicons dashicons-chart-bar"></span>
                        <h3><?php _e('No products added yet', 'canhcampromotion-quantity-promotion'); ?></h3>
                        <p><?php _e('Search and add products to your quantity promotion campaign.', 'canhcampromotion-quantity-promotion'); ?></p>
                    </div>
                <?php endif; ?>
            </div> <!-- End products-table-wrapper -->
        </div> <!-- End products-container -->
    </div> <!-- End products-section -->
    <!-- Settings Section -->
    <div class="fsqp-settings">
        <h4><?php _e('Promotion Settings', 'canhcampromotion-quantity-promotion'); ?></h4>

        <div class="fs-form-row">
            <div class="fs-form-group">
                <label>
                    <input type="checkbox" name="apply_to_variations" value="1"
                           <?php checked(!empty($campaign->global_settings['apply_to_variations'])); ?>>
                    <?php _e('Apply to product variations', 'canhcampromotion-quantity-promotion'); ?>
                </label>
                <small class="fs-help-text">
                    <?php _e('When enabled, quantity discounts will apply to individual variations of variable products.', 'canhcampromotion-quantity-promotion'); ?>
                </small>
            </div>
        </div>

        <div class="fs-form-row">
            <div class="fs-form-group">
                <label>
                    <input type="checkbox" name="combine_with_other_discounts" value="1"
                           <?php checked(!empty($campaign->global_settings['combine_with_other_discounts'])); ?>>
                    <?php _e('Combine with other discounts', 'canhcampromotion-quantity-promotion'); ?>
                </label>
                <small class="fs-help-text">
                    <?php _e('Allow this promotion to stack with other active promotions.', 'canhcampromotion-quantity-promotion'); ?>
                </small>
            </div>
        </div>

        <div class="fs-form-row">
            <div class="fs-form-group">
                <label>
                    <input type="checkbox" name="show_savings_message" value="1"
                           <?php checked(!empty($campaign->global_settings['show_savings_message'])); ?>>
                    <?php _e('Show savings message', 'canhcampromotion-quantity-promotion'); ?>
                </label>
                <small class="fs-help-text">
                    <?php _e('Display how much customers are saving with quantity discounts.', 'canhcampromotion-quantity-promotion'); ?>
                </small>
            </div>
        </div>
    </div>
</div>

<style>
/* Quantity Promotion Styles - Core Compatible */
.fs-campaign-type-content {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
}

.fs-section-header h2 {
    margin: 0 0 8px 0;
    color: #1d2327;
    font-size: 20px;
    font-weight: 600;
}

.fs-section-header p {
    margin: 0 0 20px 0;
    color: #646970;
}

/* Product Search Section */
.fs-product-search-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 6px;
}

.fs-search-bar {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.fs-search-input-group {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.fs-search-input-group input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.fs-search-input-group input:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 1px #0073aa;
}

.fs-search-loading {
    position: absolute;
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
    display: none;
}

.fs-loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fs-search-filters {
    display: flex;
    gap: 10px;
    align-items: center;
}

.fs-select-wrapper select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    min-width: 150px;
}

/* Buttons */
.fs-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f9f9f9;
    color: #1d2327;
    cursor: pointer;
    font-size: 13px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s;
}

.fs-btn:hover {
    background: #e9e9e9;
    border-color: #ccc;
}

.fs-btn-primary {
    background: #0073aa;
    color: #fff;
    border-color: #0073aa;
}

.fs-btn-primary:hover {
    background: #005a87;
    border-color: #005a87;
}

.fs-btn-secondary {
    background: #0073aa;
    color: #fff;
    border-color: #0073aa;
}

.fs-btn-secondary:hover {
    background: #005a87;
    border-color: #005a87;
}

.fs-btn-outline {
    background: transparent;
    color: #0073aa;
    border-color: #0073aa;
}

.fs-btn-outline:hover {
    background: #0073aa;
    color: #fff;
}

.fs-btn-danger {
    background: #d63638;
    color: #fff;
    border-color: #d63638;
}

.fs-btn-danger:hover {
    background: #b32d2e;
    border-color: #b32d2e;
}

.fs-btn-small {
    padding: 4px 8px;
    font-size: 12px;
}

.fs-btn-disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Search Results */
.fs-search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    margin-top: 5px;
}

.fs-search-results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #f0f0f1;
    background: #f9f9f9;
}

.fs-search-results-header h4 {
    margin: 0;
    font-size: 14px;
}

.fsqp-search-results-list {
    max-height: 250px;
    overflow-y: auto;
}

.fsqp-search-result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f1;
    cursor: pointer;
    transition: background-color 0.2s;
}

.fsqp-search-result-item:hover {
    background: #f6f7f7;
}

.fsqp-search-result-item:last-child {
    border-bottom: none;
}

.fsqp-product-info strong {
    display: block;
    color: #1d2327;
    font-size: 14px;
    margin-bottom: 4px;
}

.fsqp-product-meta {
    font-size: 12px;
    color: #646970;
}

.fsqp-product-meta span {
    margin-right: 10px;
}

/* Products Table */
.fs-products-container {
    margin-top: 20px;
}

.fs-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.fs-section-header h3 {
    margin: 0;
    color: #1d2327;
    font-size: 16px;
    font-weight: 600;
}

.fs-product-count {
    color: #646970;
    font-size: 14px;
    font-weight: normal;
}

.fs-products-table-wrapper {
    border: 1px solid #ddd;
    border-radius: 6px;
    overflow: hidden;
}

.fs-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
}

.fs-table th {
    background: #f6f7f7;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #1d2327;
    border-bottom: 1px solid #ddd;
}

.fs-table td {
    padding: 15px;
    border-bottom: 1px solid #f0f0f1;
    vertical-align: top;
}

.fs-table tr:last-child td {
    border-bottom: none;
}

/* Checkbox Column */
.fs-checkbox-column,
.fs-checkbox-cell {
    width: 40px;
    text-align: center;
}

.fs-checkbox-wrapper {
    position: relative;
}

.fs-checkbox {
    margin: 0;
}

/* Product Info */
.fs-product-info-cell {
    min-width: 280px;
}

.fs-product-info-enhanced {
    display: flex;
    gap: 12px;
    align-items: center;
}

.fs-product-image {
    flex-shrink: 0;
}

.fs-product-thumb {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 4px;
}

.fs-product-thumb-placeholder {
    width: 50px;
    height: 50px;
    background: #f0f0f1;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #646970;
}

.fs-product-name {
    display: block;
    color: #1d2327;
    font-size: 14px;
    margin-bottom: 4px;
}

.fs-product-meta {
    font-size: 12px;
    color: #646970;
}

.fs-product-meta span {
    margin-right: 10px;
}

.fs-stock-status {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
}

.fs-in-stock {
    background: #d4edda;
    color: #155724;
}

.fs-out-of-stock {
    background: #f8d7da;
    color: #721c24;
}

/* Price Column */
.fs-price-cell {
    width: 100px;
}

.fs-regular-price {
    font-weight: 600;
    color: #1d2327;
}

/* Quantity Ranges */
.fs-ranges-cell {
    min-width: 500px;
}

.fsqp-quantity-ranges {
    width: 100%;
}

.fsqp-range-item {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 10px;
}

.fsqp-range-controls {
    display: grid;
    grid-template-columns: 80px 80px 200px 100px auto;
    gap: 10px;
    align-items: end;
}

.fsqp-range-field label {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: #1d2327;
    margin-bottom: 4px;
}

.fs-input-small {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
}

.fs-input-small:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 1px #0073aa;
}

/* Discount Controls */
.fs-discount-controls {
    display: flex;
    gap: 4px;
    align-items: center;
}

.fs-discount-type-toggle {
    display: flex;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.fs-discount-type-btn {
    padding: 6px 10px;
    border: none;
    background: #f9f9f9;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.2s;
}

.fs-discount-type-btn:hover {
    background: #e9e9e9;
}

.fs-discount-type-btn.active {
    background: #0073aa;
    color: #fff;
}

.fs-discount-input {
    flex: 1;
    min-width: 60px;
}

.fsqp-add-range {
    margin-top: 10px;
}

/* Empty State */
.fs-empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #646970;
}

.fs-empty-state .dashicons {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.fs-empty-state h3 {
    margin: 8px 0;
    color: #646970;
}

.fs-empty-state p {
    margin: 8px 0;
}

/* Settings */
.fsqp-settings {
    margin-top: 30px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 6px;
}

.fsqp-settings h4 {
    margin: 0 0 15px 0;
    color: #1d2327;
    font-size: 16px;
    font-weight: 600;
}

.fs-form-row {
    margin-bottom: 15px;
}

.fs-form-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-weight: 500;
}

.fs-help-text {
    display: block;
    margin-top: 4px;
    color: #646970;
    font-size: 12px;
    font-style: italic;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Product search functionality - following flash-sale.php structure
    let searchTimeout;

    $('#fsqp-product-search').on('input', function() {
        clearTimeout(searchTimeout);
        const query = $(this).val().trim();

        if (query.length >= 2) {
            searchTimeout = setTimeout(() => {
                searchProducts(query);
            }, 500);
        } else {
            $('#fsqp-search-results').hide();
            $('.fs-search-loading').hide();
        }
    });

    $('#fsqp-search-btn').on('click', function() {
        const query = $('#fsqp-product-search').val().trim();
        if (query) {
            searchProducts(query);
        }
    });

    function searchProducts(query) {
        const categoryId = $('#fsqp-category-filter').val();

        // Show loading
        $('.fs-search-loading').show();

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'fsqp_search_products',
                query: query,
                category_id: categoryId,
                nonce: '<?php echo wp_create_nonce("fsqp_search_products"); ?>'
            },
            success: function(response) {
                $('.fs-search-loading').hide();
                if (response.success) {
                    displaySearchResults(response.data);
                } else {
                    displaySearchResults([]);
                }
            },
            error: function() {
                $('.fs-search-loading').hide();
                displaySearchResults([]);
            }
        });
    }

    function displaySearchResults(products) {
        const $results = $('#fsqp-search-results .fsqp-search-results-list');
        $results.empty();

        if (products.length === 0) {
            $results.html('<p>No products found.</p>');
        } else {
            products.forEach(product => {
                // Check if product already exists
                const exists = $(`#fsqp-products-tbody tr[data-product-id="${product.id}"]`).length > 0;

                const $item = $(`
                    <div class="fsqp-search-result-item" data-product-id="${product.id}">
                        <div class="fsqp-product-info">
                            <strong>${product.name}</strong>
                            <div class="fsqp-product-meta">
                                <span>SKU: ${product.sku || '-'}</span>
                                <span>ID: ${product.id}</span>
                                <span>Price: $${parseFloat(product.price || 0).toFixed(2)}</span>
                            </div>
                        </div>
                        <button type="button" class="fs-btn ${exists ? 'fs-btn-disabled' : 'fs-btn-primary'} fs-btn-small fsqp-add-product" ${exists ? 'disabled' : ''}>
                            ${exists ? 'Added' : 'Add'}
                        </button>
                    </div>
                `);
                $results.append($item);
            });
        }

        $('#fsqp-search-results').show();
    }

    // Add product to campaign
    $(document).on('click', '.fsqp-add-product', function() {
        const $item = $(this).closest('.fsqp-search-result-item');
        const productId = $item.data('product-id');
        const $button = $(this);

        // Check if product already added
        if ($(`#fsqp-products-tbody tr[data-product-id="${productId}"]`).length > 0) {
            alert('Product already added to campaign.');
            return;
        }

        addProductToTable(productId, $item);
    });

    function addProductToTable(productId, $searchItem) {
        // Hide empty state
        $('#fsqp-empty-products').hide();

        // Get product data from search item
        const productName = $searchItem.find('strong').text();
        const productMeta = $searchItem.find('.fsqp-product-meta').html();
        const productPrice = $searchItem.find('.fsqp-product-meta').text().match(/Price: \$([0-9.,]+)/);
        const displayPrice = productPrice ? productPrice[1] : '-';

        const $newRow = $(`
            <tr data-product-id="${productId}" class="fsqp-product-row">
                <td class="fs-checkbox-cell">
                    <div class="fs-checkbox-wrapper">
                        <input type="checkbox" class="fsqp-product-checkbox fs-checkbox" id="product-${productId}" value="${productId}">
                        <label for="product-${productId}" class="fs-checkbox-label"></label>
                    </div>
                </td>
                <td class="fs-product-info-cell">
                    <div class="fs-product-info-enhanced">
                        <div class="fs-product-image">
                            <div class="fs-product-thumb-placeholder">
                                <span class="dashicons dashicons-products"></span>
                            </div>
                        </div>
                        <div class="fs-product-details">
                            <strong class="fs-product-name">${productName}</strong>
                            <div class="fs-product-meta">${productMeta}</div>
                        </div>
                    </div>
                </td>
                <td class="fs-price-cell">
                    <span class="fs-regular-price">$${displayPrice}</span>
                </td>
                <td class="fs-ranges-cell">
                    <div class="fsqp-quantity-ranges" data-product-id="${productId}">
                        <div class="fsqp-add-range">
                            <button type="button" class="fs-btn fs-btn-secondary fs-btn-small fsqp-add-range-btn" data-product-id="${productId}">
                                <span class="dashicons dashicons-plus-alt"></span>
                                Add Range
                            </button>
                        </div>
                    </div>
                </td>
                <td class="fs-actions-cell">
                    <button type="button" class="fs-btn fs-btn-danger fs-btn-small fsqp-remove-product" title="Remove product">
                        <span class="dashicons dashicons-trash"></span>
                    </button>
                </td>
            </tr>
            <input type="hidden" name="products[${productId}][product_id]" value="${productId}">
        `);

        $('#fsqp-products-tbody').append($newRow);
        updateProductCount();

        // Update search result button
        $searchItem.find('.fsqp-add-product').addClass('fs-btn-disabled').prop('disabled', true).text('Added');

        // Hide search results
        $('#fsqp-search-results').hide();
        $('#fsqp-product-search').val('');
    }

    function updateProductCount() {
        const count = $('#fsqp-products-tbody tr').length;
        $('#fsqp-products-count').text(`(${count} products)`);
    }

    // Select all products functionality
    $('#fsqp-select-all-products').on('change', function() {
        const isChecked = $(this).is(':checked');
        $('.fsqp-product-checkbox').prop('checked', isChecked);
        updateBulkActionsButton();
    });

    // Individual checkbox change
    $(document).on('change', '.fsqp-product-checkbox', function() {
        const totalCheckboxes = $('.fsqp-product-checkbox').length;
        const checkedCheckboxes = $('.fsqp-product-checkbox:checked').length;
        $('#fsqp-select-all-products').prop('checked', totalCheckboxes === checkedCheckboxes);
        updateBulkActionsButton();
    });

    function updateBulkActionsButton() {
        const checkedCount = $('.fsqp-product-checkbox:checked').length;
        $('#fsqp-bulk-actions-btn').prop('disabled', checkedCount === 0);

        if (checkedCount > 0) {
            $('#fsqp-bulk-actions-btn').text(`Bulk Actions (${checkedCount})`);
        } else {
            $('#fsqp-bulk-actions-btn').text('Bulk Actions');
        }
    }

    // Remove product
    $(document).on('click', '.fsqp-remove-product', function() {
        $(this).closest('tr').remove();
        updateProductCount();

        // Show empty state if no products
        if ($('#fsqp-products-tbody tr').length === 0) {
            $('#fsqp-empty-products').show();
        }
    });

    // Add quantity range functionality
    $(document).on('click', '.fsqp-add-range-btn', function() {
        const $btn = $(this);
        const productId = $btn.data('product-id');
        const $rangesContainer = $(`.fsqp-quantity-ranges[data-product-id="${productId}"]`);
        const $addRangeDiv = $rangesContainer.find('.fsqp-add-range');
        const rangeIndex = Date.now(); // Use timestamp as unique index

        const rangeHtml = `
            <div class="fsqp-range-item" data-range-index="${rangeIndex}">
                <div class="fsqp-range-controls">
                    <div class="fsqp-range-field">
                        <label>Min Qty</label>
                        <input type="number"
                               name="products[${productId}][ranges][${rangeIndex}][min_quantity]"
                               value="1"
                               min="1"
                               class="fs-input-small"
                               required>
                    </div>
                    <div class="fsqp-range-field">
                        <label>Max Qty</label>
                        <input type="number"
                               name="products[${productId}][ranges][${rangeIndex}][max_quantity]"
                               value=""
                               min="0"
                               class="fs-input-small"
                               placeholder="∞">
                    </div>
                    <div class="fsqp-range-field">
                        <label>Discount</label>
                        <div class="fs-discount-controls">
                            <div class="fs-discount-type-toggle">
                                <button type="button" class="fs-discount-type-btn active" data-type="1">%</button>
                                <button type="button" class="fs-discount-type-btn" data-type="2">$</button>
                            </div>
                            <input type="number"
                                   name="products[${productId}][ranges][${rangeIndex}][discount_value]"
                                   value="0"
                                   step="0.01"
                                   min="0"
                                   class="fs-input-small fs-discount-input"
                                   required>
                            <input type="hidden"
                                   name="products[${productId}][ranges][${rangeIndex}][discount_type]"
                                   value="1">
                        </div>
                    </div>
                    <div class="fsqp-range-field">
                        <label>Max Discount</label>
                        <input type="number"
                               name="products[${productId}][ranges][${rangeIndex}][max_discount_amount]"
                               value=""
                               step="0.01"
                               min="0"
                               class="fs-input-small"
                               placeholder="0">
                    </div>
                    <div class="fsqp-range-actions">
                        <button type="button" class="fs-btn fs-btn-danger fs-btn-small fsqp-remove-range" title="Remove range">
                            <span class="dashicons dashicons-trash"></span>
                        </button>
                    </div>
                </div>
            </div>
        `;

        $addRangeDiv.before(rangeHtml);
    });

    // Remove quantity range
    $(document).on('click', '.fsqp-remove-range', function() {
        $(this).closest('.fsqp-range-item').remove();
    });

    // Discount type toggle
    $(document).on('click', '.fs-discount-type-btn', function() {
        const $btn = $(this);
        const $toggle = $btn.closest('.fs-discount-type-toggle');
        const type = $btn.data('type');

        $toggle.find('.fs-discount-type-btn').removeClass('active');
        $btn.addClass('active');
        $toggle.siblings('input[type="hidden"]').val(type);
    });

    // Close search results
    $('#fsqp-close-search').on('click', function() {
        $('#fsqp-search-results').hide();
    });

    // Load category products
    $('#fsqp-load-category-products').on('click', function() {
        const $btn = $(this);
        const categoryId = $('#fsqp-category-filter').val();

        if (categoryId) {
            // Show loading state
            $btn.prop('disabled', true);
            $btn.find('.fs-btn-text').hide();
            $btn.find('.fs-loading-spinner').show();

            loadCategoryProducts(categoryId, $btn);
        } else {
            alert('Please select a category first.');
        }
    });

    function loadCategoryProducts(categoryId, $btn) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'fsqp_search_products',
                query: '',
                category_id: categoryId,
                nonce: '<?php echo wp_create_nonce("fsqp_search_products"); ?>'
            },
            success: function(response) {
                // Reset button state
                $btn.prop('disabled', false);
                $btn.find('.fs-btn-text').show();
                $btn.find('.fs-loading-spinner').hide();

                if (response.success) {
                    displaySearchResults(response.data);
                } else {
                    displaySearchResults([]);
                }
            },
            error: function() {
                // Reset button state
                $btn.prop('disabled', false);
                $btn.find('.fs-btn-text').show();
                $btn.find('.fs-loading-spinner').hide();
                displaySearchResults([]);
            }
        });
    }
});
</script>
