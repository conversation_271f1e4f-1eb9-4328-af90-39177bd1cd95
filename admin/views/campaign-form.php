<?php
/**
 * Campaign creation/edit form
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

$campaign_type = $_GET['campaign_type'] ?? '';
$campaign_id = $_GET['campaign_id'] ?? 0;
$is_edit = !empty($campaign_id);

// Get campaign data if editing
$campaign = null;
if ($is_edit) {
    $campaign = FS()->campaigns()->get_campaign($campaign_id);
}

// Get available campaign types from filter
$campaign_types = apply_filters('fs_campaign_types', [
    'flash-sale' => [
        'name' => __('Flash Sale', 'flashsale-core'),
        'description' => __('Flash sale campaigns', 'flashsale-core'),
        'template' => 'campaign-types/flash-sale.php'
    ]
]);

$page_title = $is_edit ?
    sprintf(__('Edit %s Campaign', 'flashsale-core'), $campaign_types[$campaign->type]['name'] ?? __('Campaign', 'flashsale-core')) :
    sprintf(__('Create %s Campaign', 'flashsale-core'), $campaign_types[$campaign_type]['name'] ?? __('Campaign', 'flashsale-core'));
?>

<div class="wrap">
<div class="flashsale-admin-container">
    <div class="fs-top-page">
        <h1 class="fs-title">
            <span class="fs-icon">🎯</span>
            <?php echo esc_html($page_title); ?>
        </h1>
        <div class="fs-top-page-actions">
            <a href="<?php echo admin_url('admin.php?page=flashsale-campaigns'); ?>" class="fs-btn fs-btn-secondary">
                <span class="dashicons dashicons-arrow-left-alt"></span>
                <?php _e('Back to Campaigns', 'flashsale-core'); ?>
            </a>

            <div class="fs-main-actions">
                <?php if ($is_edit): ?>
                    <button type="submit" form="fs-campaign-form" class="fs-btn fs-btn-primary">
                        <span class="dashicons dashicons-update"></span>
                        <?php _e('Update Campaign', 'flashsale-core'); ?>
                    </button>
                    <button type="button" class="fs-btn fs-btn-danger fs-delete-campaign"
                            data-campaign-id="<?php echo $campaign->id; ?>">
                        <span class="dashicons dashicons-trash"></span>
                        <?php _e('Delete Campaign', 'flashsale-core'); ?>
                    </button>
                <?php else: ?>
                    <button type="submit" form="fs-campaign-form" class="fs-btn fs-btn-primary">
                        <span class="dashicons dashicons-saved"></span>
                        <?php _e('Create Campaign', 'flashsale-core'); ?>
                    </button>
                <?php endif; ?>

                <button type="button" class="fs-btn fs-btn-secondary" onclick="window.history.back();">
                    <?php _e('Cancel', 'flashsale-core'); ?>
                </button>
            </div>
        </div>
    </div>

    <?php
    // Display messages
    if (isset($_GET['message'])) {
        if ($_GET['message'] === 'success') {
            echo '<div class="notice notice-success is-dismissible"><p>' . __('Campaign saved successfully!', 'flashsale-core') . '</p></div>';
        } elseif ($_GET['message'] === 'error') {
            $error_msg = isset($_GET['error_msg']) ? urldecode($_GET['error_msg']) : __('An error occurred while saving the campaign.', 'flashsale-core');
            echo '<div class="notice notice-error is-dismissible"><p>' . esc_html($error_msg) . '</p></div>';
        }
    }
    ?>

    <form id="fs-campaign-form" method="post" action="">
        <?php wp_nonce_field('fs_save_campaign', 'fs_campaign_nonce'); ?>
        <input type="hidden" name="action" value="save_campaign">
        <input type="hidden" name="campaign_id" value="<?php echo $campaign_id; ?>">
        <input type="hidden" name="campaign_type" value="<?php echo $is_edit ? $campaign->type : $campaign_type; ?>">

        <div class="fs-campaign-form-container">
            <div class="fs-form-sidebar">
                <!-- Basic Information -->
                <div class="fs-form-section">
                    <h3><?php _e('Basic Information', 'flashsale-core'); ?></h3>
                    
                    <div class="fs-field">
                        <label for="campaign_name"><?php _e('Campaign Name', 'flashsale-core'); ?> *</label>
                        <input type="text" id="campaign_name" name="campaign_name" 
                               value="<?php echo $is_edit ? esc_attr($campaign->name) : ''; ?>" 
                               placeholder="<?php _e('Enter campaign name', 'flashsale-core'); ?>" required>
                    </div>

                    <div class="fs-field">
                        <label for="campaign_description"><?php _e('Description', 'flashsale-core'); ?></label>
                        <textarea id="campaign_description" name="campaign_description" 
                                  placeholder="<?php _e('Enter campaign description', 'flashsale-core'); ?>"><?php echo $is_edit ? esc_textarea($campaign->description) : ''; ?></textarea>
                    </div>

                    <div class="fs-field">
                        <label for="campaign_priority"><?php _e('Priority', 'flashsale-core'); ?></label>
                        <select id="campaign_priority" name="campaign_priority">
                            <?php for ($i = 0; $i <= 10; $i++): ?>
                                <option value="<?php echo $i; ?>" <?php selected($is_edit ? $campaign->priority : 5, $i); ?>>
                                    <?php echo $i; ?> <?php echo $i === 0 ? __('(Lowest)', 'flashsale-core') : ($i === 10 ? __('(Highest)', 'flashsale-core') : ''); ?>
                                </option>
                            <?php endfor; ?>
                        </select>
                        <small><?php _e('Higher numbers = higher priority', 'flashsale-core'); ?></small>
                    </div>
                </div>

                <!-- Schedule -->
                <div class="fs-form-section">
                    <h3><?php _e('Schedule', 'flashsale-core'); ?></h3>
                    
                    <div class="fs-field">
                        <label for="start_date"><?php _e('Start Date & Time', 'flashsale-core'); ?></label>
                        <input type="datetime-local" id="start_date" name="start_date" 
                               value="<?php echo $is_edit && $campaign->start_date ? date('Y-m-d\TH:i', strtotime($campaign->start_date)) : ''; ?>">
                        <small><?php _e('Leave empty for immediate start', 'flashsale-core'); ?></small>
                    </div>

                    <div class="fs-field">
                        <label for="end_date"><?php _e('End Date & Time', 'flashsale-core'); ?></label>
                        <input type="datetime-local" id="end_date" name="end_date" 
                               value="<?php echo $is_edit && $campaign->end_date ? date('Y-m-d\TH:i', strtotime($campaign->end_date)) : ''; ?>">
                        <small><?php _e('Leave empty for no end date', 'flashsale-core'); ?></small>
                    </div>
                </div>

                <!-- Status -->
                <div class="fs-form-section">
                    <h3><?php _e('Status', 'flashsale-core'); ?></h3>
                    
                    <div class="fs-field">
                        <label>
                            <input type="checkbox" name="campaign_status" value="1" 
                                   <?php checked($is_edit ? $campaign->status : true); ?>>
                            <?php _e('Activate campaign immediately', 'flashsale-core'); ?>
                        </label>
                    </div>
                </div>


            </div>

            <div class="fs-form-main">
                <!-- Campaign Type Specific Content -->
                <div class="fs-campaign-content">
                    <?php
                    // Load campaign type specific form
                    $campaign_type_to_load = $is_edit ? $campaign->type : $campaign_type;

                    if (isset($campaign_types[$campaign_type_to_load])) {
                        $type_config = $campaign_types[$campaign_type_to_load];

                        // Check if template exists
                        if (isset($type_config['template'])) {
                            $template_path = $type_config['template'];

                            // If it's a relative path, make it relative to admin/views/
                            if (!file_exists($template_path) && strpos($template_path, '/') !== 0) {
                                $template_path = FLASHSALE_CORE_PLUGIN_DIR . 'admin/views/' . $template_path;
                            }

                            if (file_exists($template_path)) {
                                include $template_path;
                            } else {
                                echo '<div class="fs-notice fs-notice-error">';
                                echo '<p>' . sprintf(__('Template file not found for %s campaign type.', 'flashsale-core'), $type_config['name']) . '</p>';
                                echo '</div>';
                            }
                        } else {
                            echo '<div class="fs-notice fs-notice-error">';
                            echo '<p>' . sprintf(__('No template defined for %s campaign type.', 'flashsale-core'), $type_config['name']) . '</p>';
                            echo '</div>';
                        }
                    } else {
                        echo '<div class="fs-notice fs-notice-error">';
                        echo '<p>' . __('Invalid campaign type. Please select a valid campaign type.', 'flashsale-core') . '</p>';
                        echo '</div>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </form>
</div>
</div>

<script>
jQuery(document).ready(function($) {
    // Form validation
    $('#fs-campaign-form').on('submit', function(e) {
        var campaignName = $('#campaign_name').val().trim();
        if (!campaignName) {
            alert('<?php _e('Please enter a campaign name.', 'flashsale-core'); ?>');
            e.preventDefault();
            return false;
        }
    });
    
    // Delete campaign
    $('.fs-delete-campaign').on('click', function() {
        var campaignId = $(this).data('campaign-id');
        if (confirm('<?php _e('Are you sure you want to delete this campaign? This action cannot be undone.', 'flashsale-core'); ?>')) {
            // AJAX delete request
            window.location.href = '<?php echo admin_url('admin.php?page=flashsale-campaigns&action=delete&campaign_id='); ?>' + campaignId;
        }
    });
});
</script>
