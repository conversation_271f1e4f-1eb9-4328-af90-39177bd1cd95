<?php
/**
 * Edit campaign page template
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get campaign data
$campaign = isset($campaign) ? $campaign : null;
if (!$campaign) {
    echo '<div class="notice notice-error"><p>' . __('Campaign not found.', 'flashsale-core') . '</p></div>';
    return;
}
?>

<div class="wrap fs-admin-wrap">
    <div class="fs-top-page">
        <h1 class="fs-title">
            <span class="fs-icon">✏️</span>
            <?php printf(__('Edit Campaign: %s', 'flashsale-core'), esc_html($campaign->name)); ?>
        </h1>
        <div class="fs-top-page-actions">
            <a href="<?php echo admin_url('admin.php?page=flashsale-campaigns'); ?>" class="fs-btn fs-btn-secondary">
                <span class="dashicons dashicons-arrow-left-alt"></span>
                <?php _e('Back to Campaigns', 'flashsale-core'); ?>
            </a>
        </div>
    </div>

    <div class="fs-dashboard">
        <form id="fs-campaign-form" method="post">
            <?php wp_nonce_field('fs_campaign_nonce', '_wpnonce'); ?>
            <input type="hidden" name="campaign_id" value="<?php echo $campaign->id; ?>">
            
            <div class="fs-form-grid">
                <!-- Basic Information -->
                <div class="fs-card">
                    <div class="fs-card-top-page">
                        <h2><?php _e('Basic Information', 'flashsale-core'); ?></h2>
                    </div>
                    <div class="fs-card-content">
                        <div class="fs-form-group">
                            <label for="campaign_name" class="fs-label">
                                <?php _e('Campaign Name', 'flashsale-core'); ?>
                                <span class="fs-required">*</span>
                            </label>
                            <input type="text" 
                                   id="campaign_name" 
                                   name="campaign_name" 
                                   class="fs-input" 
                                   value="<?php echo esc_attr($campaign->name); ?>"
                                   required>
                        </div>
                        
                        <div class="fs-form-group">
                            <label for="campaign_description" class="fs-label">
                                <?php _e('Description', 'flashsale-core'); ?>
                            </label>
                            <textarea id="campaign_description" 
                                      name="campaign_description" 
                                      class="fs-textarea" 
                                      rows="3"><?php echo esc_textarea($campaign->description); ?></textarea>
                        </div>
                        
                        <div class="fs-form-row">
                            <div class="fs-form-group">
                                <label for="campaign_type" class="fs-label">
                                    <?php _e('Campaign Type', 'flashsale-core'); ?>
                                    <span class="fs-required">*</span>
                                </label>
                                <select id="campaign_type" name="campaign_type" class="fs-select" required>
                                    <option value=""><?php _e('Select campaign type', 'flashsale-core'); ?></option>
                                    <option value="flash-sale" <?php selected($campaign->type, 'flash-sale'); ?>>
                                        <?php _e('Flash Sale', 'flashsale-core'); ?>
                                    </option>
                                </select>
                            </div>
                            
                            <div class="fs-form-group">
                                <label for="campaign_priority" class="fs-label">
                                    <?php _e('Priority', 'flashsale-core'); ?>
                                </label>
                                <select id="campaign_priority" name="campaign_priority" class="fs-select">
                                    <?php for ($i = 0; $i <= 10; $i++): ?>
                                        <option value="<?php echo $i; ?>" <?php selected($campaign->priority, $i); ?>>
                                            <?php echo $i; ?>
                                        </option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Schedule -->
                <div class="fs-card">
                    <div class="fs-card-top-page">
                        <h2><?php _e('Schedule', 'flashsale-core'); ?></h2>
                    </div>
                    <div class="fs-card-content">
                        <div class="fs-form-row">
                            <div class="fs-form-group">
                                <label for="start_date" class="fs-label">
                                    <?php _e('Start Date & Time', 'flashsale-core'); ?>
                                </label>
                                <input type="datetime-local" 
                                       id="start_date" 
                                       name="start_date" 
                                       class="fs-input"
                                       value="<?php echo $campaign->start_date ? date('Y-m-d\TH:i', strtotime($campaign->start_date)) : ''; ?>">
                            </div>
                            
                            <div class="fs-form-group">
                                <label for="end_date" class="fs-label">
                                    <?php _e('End Date & Time', 'flashsale-core'); ?>
                                </label>
                                <input type="datetime-local" 
                                       id="end_date" 
                                       name="end_date" 
                                       class="fs-input"
                                       value="<?php echo $campaign->end_date ? date('Y-m-d\TH:i', strtotime($campaign->end_date)) : ''; ?>"
                                       <?php echo !$campaign->end_date ? 'disabled' : ''; ?>>
                            </div>
                        </div>
                        
                        <div class="fs-form-group">
                            <label class="fs-checkbox-label">
                                <input type="checkbox" 
                                       id="no_end_date" 
                                       name="no_end_date" 
                                       class="fs-checkbox"
                                       <?php checked(!$campaign->end_date); ?>>
                                <span class="fs-checkmark"></span>
                                <?php _e('No end date (run indefinitely)', 'flashsale-core'); ?>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Campaign Statistics -->
                <div class="fs-card">
                    <div class="fs-card-top-page">
                        <h2><?php _e('Statistics', 'flashsale-core'); ?></h2>
                    </div>
                    <div class="fs-card-content">
                        <div class="fs-stats-mini">
                            <div class="fs-stat-mini">
                                <span class="fs-stat-mini-label"><?php _e('Created', 'flashsale-core'); ?></span>
                                <span class="fs-stat-mini-value">
                                    <?php echo date('M j, Y H:i', strtotime($campaign->created_at)); ?>
                                </span>
                            </div>
                            
                            <div class="fs-stat-mini">
                                <span class="fs-stat-mini-label"><?php _e('Last Updated', 'flashsale-core'); ?></span>
                                <span class="fs-stat-mini-value">
                                    <?php echo $campaign->updated_at ? date('M j, Y H:i', strtotime($campaign->updated_at)) : '-'; ?>
                                </span>
                            </div>
                            
                            <div class="fs-stat-mini">
                                <span class="fs-stat-mini-label"><?php _e('Total Sales', 'flashsale-core'); ?></span>
                                <span class="fs-stat-mini-value">0</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Status -->
                <div class="fs-card">
                    <div class="fs-card-top-page">
                        <h2><?php _e('Status', 'flashsale-core'); ?></h2>
                    </div>
                    <div class="fs-card-content">
                        <div class="fs-form-group">
                            <label class="fs-checkbox-label">
                                <input type="checkbox" 
                                       id="campaign_status" 
                                       name="campaign_status" 
                                       class="fs-checkbox" 
                                       value="1" 
                                       <?php checked($campaign->status); ?>>
                                <span class="fs-checkmark"></span>
                                <?php _e('Campaign is active', 'flashsale-core'); ?>
                            </label>
                        </div>
                        
                        <div class="fs-current-status">
                            <span class="fs-status-label"><?php _e('Current Status:', 'flashsale-core'); ?></span>
                            <?php if ($campaign->status): ?>
                                <span class="fs-status fs-status-active"><?php _e('Active', 'flashsale-core'); ?></span>
                            <?php else: ?>
                                <span class="fs-status fs-status-inactive"><?php _e('Inactive', 'flashsale-core'); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Campaign Settings -->
                <div class="fs-card fs-full-width" id="campaign-settings">
                    <div class="fs-card-top-page">
                        <h2><?php _e('Campaign Settings', 'flashsale-core'); ?></h2>
                    </div>
                    <div class="fs-card-content">
                        <?php
                        // Load campaign-specific settings based on type
                        $global_settings = json_decode($campaign->global_settings, true);
                        ?>
                        
                        <div id="current-settings">
                            <h3><?php printf(__('%s Settings', 'flashsale-core'), ucfirst(str_replace('-', ' ', $campaign->type))); ?></h3>
                            
                            <?php if ($campaign->type === 'flash-sale'): ?>
                                <div class="fs-form-group">
                                    <label class="fs-label"><?php _e('Default Discount Type', 'flashsale-core'); ?></label>
                                    <select name="discount_type" class="fs-select">
                                        <option value="percentage" <?php selected($global_settings['discount_type'] ?? '', 'percentage'); ?>>
                                            <?php _e('Percentage', 'flashsale-core'); ?>
                                        </option>
                                        <option value="fixed" <?php selected($global_settings['discount_type'] ?? '', 'fixed'); ?>>
                                            <?php _e('Fixed Amount', 'flashsale-core'); ?>
                                        </option>
                                    </select>
                                </div>

                                <div class="fs-form-group">
                                    <label class="fs-label"><?php _e('Default Discount Value', 'flashsale-core'); ?></label>
                                    <input type="number"
                                           name="discount_value"
                                           class="fs-input"
                                           value="<?php echo esc_attr($global_settings['discount_value'] ?? ''); ?>"
                                           min="0"
                                           step="0.01">
                                </div>

                                <div class="fs-form-group">
                                    <label class="fs-label"><?php _e('Default Max Quantity per Customer', 'flashsale-core'); ?></label>
                                    <input type="number"
                                           name="max_quantity"
                                           class="fs-input"
                                           value="<?php echo esc_attr($global_settings['max_quantity'] ?? ''); ?>"
                                           min="0">
                                </div>
                            <?php endif; ?>
                            
                            <?php if (empty($global_settings)): ?>
                                <div class="fs-empty-state">
                                    <span class="dashicons dashicons-admin-settings"></span>
                                    <h3><?php _e('No settings configured', 'flashsale-core'); ?></h3>
                                    <p><?php _e('This campaign doesn\'t have any specific settings configured yet.', 'flashsale-core'); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="fs-form-actions">
                <button type="submit" class="fs-btn fs-btn-primary fs-btn-large">
                    <span class="dashicons dashicons-yes"></span>
                    <?php _e('Update Campaign', 'flashsale-core'); ?>
                </button>
                
                <button type="button" class="fs-btn fs-btn-danger fs-btn-large" id="delete-campaign">
                    <span class="dashicons dashicons-trash"></span>
                    <?php _e('Delete Campaign', 'flashsale-core'); ?>
                </button>
                
                <a href="<?php echo admin_url('admin.php?page=flashsale-campaigns'); ?>" class="fs-btn fs-btn-outline fs-btn-large">
                    <?php _e('Cancel', 'flashsale-core'); ?>
                </a>
            </div>
        </form>
    </div>
</div>



<script>
jQuery(document).ready(function($) {
    // No end date checkbox
    $('#no_end_date').on('change', function() {
        if ($(this).is(':checked')) {
            $('#end_date').prop('disabled', true).val('');
        } else {
            $('#end_date').prop('disabled', false);
        }
    });
    
    // Delete campaign
    $('#delete-campaign').on('click', function() {
        if (confirm('<?php _e('Are you sure you want to delete this campaign? This action cannot be undone.', 'flashsale-core'); ?>')) {
            // Create a form to submit delete request
            var form = $('<form>', {
                'method': 'POST',
                'action': window.location.href
            });
            
            form.append($('<input>', {
                'type': 'hidden',
                'name': 'action',
                'value': 'delete'
            }));
            
            form.append($('<input>', {
                'type': 'hidden',
                'name': 'campaign_id',
                'value': '<?php echo $campaign->id; ?>'
            }));
            
            form.append($('<input>', {
                'type': 'hidden',
                'name': '_wpnonce',
                'value': '<?php echo wp_create_nonce('fs_campaign_nonce'); ?>'
            }));
            
            $('body').append(form);
            form.submit();
        }
    });
    
    // Form validation
    $('#fs-campaign-form').on('submit', function(e) {
        var isValid = true;
        var errors = [];
        
        // Check required fields
        if (!$('#campaign_name').val().trim()) {
            errors.push('<?php _e('Campaign name is required', 'flashsale-core'); ?>');
            isValid = false;
        }
        
        if (!$('#campaign_type').val()) {
            errors.push('<?php _e('Campaign type is required', 'flashsale-core'); ?>');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
            alert(errors.join('\n'));
        }
    });
});
</script>
