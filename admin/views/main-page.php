<?php

/**
 * Main admin page template
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <!-- Admin notices sẽ hiển thị ở đây bởi WordPress -->
    
    <div class="flashsale-admin-container">
        <div class="fs-top-page">
            <h1 class="fs-title">
                <span class="fs-icon">⚡</span>
                <?php _e('FlashSale Core Dashboard', 'flashsale-core'); ?>
            </h1>
            <div class="fs-top-page-actions">
                <a href="<?php echo admin_url('admin.php?page=flashsale-campaigns&action=new'); ?>" class="fs-btn fs-btn-primary">
                    <span class="dashicons dashicons-plus-alt"></span>
                    <?php _e('New Campaign', 'flashsale-core'); ?>
                </a>
            </div>
        </div>

        <!-- Quick Stats Overview -->
        <div class="fs-stats-overview">
            <div class="fs-stats-grid">
                <div class="fs-stat-card fs-stat-primary">
                    <div class="fs-stat-icon">
                        <span class="dashicons dashicons-megaphone"></span>
                    </div>
                    <div class="fs-stat-content">
                        <div class="fs-stat-number"><?php echo count($campaigns); ?></div>
                        <div class="fs-stat-label"><?php _e('Active Campaigns', 'flashsale-core'); ?></div>
                    </div>
                </div>

                <div class="fs-stat-card fs-stat-success">
                    <div class="fs-stat-icon">
                        <span class="dashicons dashicons-admin-plugins"></span>
                    </div>
                    <div class="fs-stat-content">
                        <div class="fs-stat-number"><?php echo is_array($addons) ? count($addons) : 0; ?></div>
                        <div class="fs-stat-label"><?php _e('Installed Addons', 'flashsale-core'); ?></div>
                    </div>
                </div>

                <div class="fs-stat-card fs-stat-info">
                    <div class="fs-stat-icon">
                        <span class="dashicons dashicons-chart-line"></span>
                    </div>
                    <div class="fs-stat-content">
                        <div class="fs-stat-number">0</div>
                        <div class="fs-stat-label"><?php _e('Total Sales', 'flashsale-core'); ?></div>
                    </div>
                </div>

                <div class="fs-stat-card fs-stat-warning">
                    <div class="fs-stat-icon">
                        <span class="dashicons dashicons-money-alt"></span>
                    </div>
                    <div class="fs-stat-content">
                        <div class="fs-stat-number">$0</div>
                        <div class="fs-stat-label"><?php _e('Revenue', 'flashsale-core'); ?></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="fs-dashboard">

            <!-- Recent Campaigns -->
            <div class="fs-card">
                <div class="fs-card-top-page">
                    <h2><?php _e('Recent Campaigns', 'flashsale-core'); ?></h2>
                    <a href="<?php echo admin_url('admin.php?page=flashsale-campaigns'); ?>" class="fs-btn fs-btn-outline fs-btn-small">
                        <?php _e('View All', 'flashsale-core'); ?>
                    </a>
                </div>
                <div class="fs-card-content">
                    <?php if (!empty($campaigns)) : ?>
                        <?php foreach (array_slice($campaigns, 0, 3) as $campaign) : ?>
                            <div style="padding: 12px 0; border-bottom: 1px solid #e9ecef;">
                                <h4 style="margin: 0 0 6px; font-size: 15px; color: #495057;">
                                    <a href="<?php echo admin_url('admin.php?page=flashsale-campaigns&action=edit&campaign_id=' . $campaign->id); ?>" style="text-decoration: none;">
                                        <?php echo esc_html($campaign->name); ?>
                                    </a>
                                </h4>
                                <p style="margin: 0; color: #6c757d; font-size: 13px;">
                                    <?php echo esc_html($campaign->type); ?>
                                </p>
                                <div style="margin-top: 6px;">
                                    <?php if ($campaign->status) : ?>
                                        <span class="fs-status fs-status-active"><?php _e('Active', 'flashsale-core'); ?></span>
                                    <?php else : ?>
                                        <span class="fs-status fs-status-inactive"><?php _e('Inactive', 'flashsale-core'); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <?php if (count($campaigns) > 3) : ?>
                            <div style="padding: 16px 0; text-align: center;">
                                <a href="<?php echo admin_url('admin.php?page=flashsale-campaigns'); ?>" class="fs-btn fs-btn-outline">
                                    <?php _e('View All Campaigns', 'flashsale-core'); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php else : ?>
                        <div class="fs-empty-state">
                            <span class="dashicons dashicons-megaphone"></span>
                            <h3><?php _e('No campaigns yet', 'flashsale-core'); ?></h3>
                            <p><?php _e('Create your first promotion campaign to get started.', 'flashsale-core'); ?></p>
                            <a href="<?php echo admin_url('admin.php?page=flashsale-campaigns&action=new'); ?>" class="fs-btn fs-btn-primary">
                                <?php _e('Create Campaign', 'flashsale-core'); ?>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Addons Overview -->
            <?php if (!empty($addons) && is_array($addons) && count($addons) > 0) : ?>
                <div class="fs-card">
                    <div class="fs-card-top-page">
                        <h2><?php _e('Active Addons', 'flashsale-core'); ?></h2>
                        <a href="<?php echo admin_url('admin.php?page=flashsale-addons'); ?>" class="fs-btn fs-btn-outline fs-btn-small">
                            <?php _e('Manage', 'flashsale-core'); ?>
                        </a>
                    </div>
                    <div class="fs-card-content">
                        <div class="fs-addon-grid">
                            <?php foreach ($addons as $slug => $addon) : ?>
                                <div class="fs-addon-card">
                                    <div class="fs-addon-icon">
                                        <span class="dashicons dashicons-admin-plugins"></span>
                                    </div>
                                    <div class="fs-addon-info">
                                        <h4><?php echo esc_html($addon['name']); ?></h4>
                                        <p><?php echo esc_html($addon['version']); ?></p>
                                    </div>
                                    <div class="fs-addon-status">
                                        <span class="fs-status fs-status-active"><?php _e('Active', 'flashsale-core'); ?></span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php else : ?>
                <div class="fs-card">
                    <div class="fs-card-top-page">
                        <h2><?php _e('Addons', 'flashsale-core'); ?></h2>
                        <a href="<?php echo admin_url('admin.php?page=flashsale-addons'); ?>" class="fs-btn fs-btn-outline fs-btn-small">
                            <?php _e('Browse', 'flashsale-core'); ?>
                        </a>
                    </div>
                    <div class="fs-card-content">
                        <div class="fs-empty-state">
                            <span class="dashicons dashicons-admin-plugins"></span>
                            <h3><?php _e('No addons installed', 'flashsale-core'); ?></h3>
                            <p><?php _e('Install addons to extend FlashSale functionality with additional promotion types and features.', 'flashsale-core'); ?></p>
                            <a href="<?php echo admin_url('admin.php?page=flashsale-addons'); ?>" class="fs-btn fs-btn-primary">
                                <?php _e('Browse Addons', 'flashsale-core'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Quick Actions -->
            <div class="fs-card">
                <div class="fs-card-top-page">
                    <h2><?php _e('Quick Actions', 'flashsale-core'); ?></h2>
                </div>
                <div class="fs-card-content">
                    <div class="fs-quick-actions">
                        <a href="<?php echo admin_url('admin.php?page=flashsale-campaigns&action=new'); ?>" class="fs-quick-action">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <?php _e('New Campaign', 'flashsale-core'); ?>
                        </a>

                        <a href="<?php echo admin_url('admin.php?page=flashsale-addons'); ?>" class="fs-quick-action">
                            <span class="dashicons dashicons-admin-plugins"></span>
                            <?php _e('Manage Addons', 'flashsale-core'); ?>
                        </a>

                        <a href="<?php echo admin_url('admin.php?page=flashsale-settings'); ?>" class="fs-quick-action">
                            <span class="dashicons dashicons-admin-settings"></span>
                            <?php _e('Settings', 'flashsale-core'); ?>
                        </a>

                        <a href="#" class="fs-quick-action" onclick="location.reload();">
                            <span class="dashicons dashicons-update"></span>
                            <?php _e('Refresh Data', 'flashsale-core'); ?>
                        </a>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
