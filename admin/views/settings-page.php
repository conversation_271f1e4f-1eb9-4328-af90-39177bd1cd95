<?php
/**
 * Settings page template
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap fs-admin-wrap">
    <div class="fs-top-page">
        <h1 class="fs-title">
            <span class="fs-icon">⚙️</span>
            <?php _e('FlashSale Settings', 'flashsale-core'); ?>
        </h1>
    </div>

    <div class="fs-dashboard">
        <form method="post" action="">
            <?php wp_nonce_field('fs_settings_nonce'); ?>
            
            <div class="fs-settings-grid">
                <!-- General Settings -->
                <div class="fs-card">
                    <div class="fs-card-top-page">
                        <h2><?php _e('General Settings', 'flashsale-core'); ?></h2>
                    </div>
                    <div class="fs-card-content">
                        <div class="fs-form-group">
                            <label class="fs-checkbox-label">
                                <input type="checkbox" 
                                       name="enable_legacy_support" 
                                       value="1" 
                                       <?php checked(get_option('fs_enable_legacy_support', 0)); ?>>
                                <span class="fs-checkmark"></span>
                                <?php _e('Enable Legacy Support', 'flashsale-core'); ?>
                            </label>
                            <p class="fs-help-text">
                                <?php _e('Enable compatibility with older FlashSale plugin versions.', 'flashsale-core'); ?>
                            </p>
                        </div>
                        
                        <div class="fs-form-group">
                            <label class="fs-checkbox-label">
                                <input type="checkbox" 
                                       name="enable_debug" 
                                       value="1" 
                                       <?php checked(get_option('fs_enable_debug', 0)); ?>>
                                <span class="fs-checkmark"></span>
                                <?php _e('Enable Debug Mode', 'flashsale-core'); ?>
                            </label>
                            <p class="fs-help-text">
                                <?php _e('Enable debug logging for troubleshooting. Only enable when needed.', 'flashsale-core'); ?>
                            </p>
                        </div>
                        
                        <div class="fs-form-group">
                            <label class="fs-checkbox-label">
                                <input type="checkbox" 
                                       name="enable_stats" 
                                       value="1" 
                                       <?php checked(get_option('fs_enable_stats', 1)); ?>>
                                <span class="fs-checkmark"></span>
                                <?php _e('Enable Statistics', 'flashsale-core'); ?>
                            </label>
                            <p class="fs-help-text">
                                <?php _e('Track campaign performance and sales statistics.', 'flashsale-core'); ?>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Performance Settings -->
                <div class="fs-card">
                    <div class="fs-card-top-page">
                        <h2><?php _e('Performance', 'flashsale-core'); ?></h2>
                    </div>
                    <div class="fs-card-content">
                        <div class="fs-form-group">
                            <label class="fs-checkbox-label">
                                <input type="checkbox" 
                                       name="cache_campaigns" 
                                       value="1" 
                                       <?php checked(get_option('fs_cache_campaigns', 1)); ?>>
                                <span class="fs-checkmark"></span>
                                <?php _e('Cache Campaigns', 'flashsale-core'); ?>
                            </label>
                            <p class="fs-help-text">
                                <?php _e('Cache active campaigns to improve performance.', 'flashsale-core'); ?>
                            </p>
                        </div>
                        
                        <div class="fs-form-group">
                            <label for="cache_duration" class="fs-label">
                                <?php _e('Cache Duration (minutes)', 'flashsale-core'); ?>
                            </label>
                            <input type="number" 
                                   id="cache_duration" 
                                   name="cache_duration" 
                                   class="fs-input" 
                                   value="<?php echo esc_attr(get_option('fs_cache_duration', 15)); ?>"
                                   min="1" 
                                   max="1440">
                            <p class="fs-help-text">
                                <?php _e('How long to cache campaign data (1-1440 minutes).', 'flashsale-core'); ?>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- API Settings -->
                <div class="fs-card">
                    <div class="fs-card-top-page">
                        <h2><?php _e('API Settings', 'flashsale-core'); ?></h2>
                    </div>
                    <div class="fs-card-content">
                        <div class="fs-form-group">
                            <label class="fs-checkbox-label">
                                <input type="checkbox" 
                                       name="enable_api" 
                                       value="1" 
                                       <?php checked(get_option('fs_enable_api', 0)); ?>>
                                <span class="fs-checkmark"></span>
                                <?php _e('Enable REST API', 'flashsale-core'); ?>
                            </label>
                            <p class="fs-help-text">
                                <?php _e('Enable REST API endpoints for external integrations.', 'flashsale-core'); ?>
                            </p>
                        </div>
                        
                        <?php if (get_option('fs_enable_api', 0)): ?>
                            <div class="fs-api-info">
                                <h4><?php _e('API Endpoints', 'flashsale-core'); ?></h4>
                                <div class="fs-api-endpoints">
                                    <div class="fs-api-endpoint">
                                        <code>GET /wp-json/flashsale/v1/campaigns</code>
                                        <span><?php _e('Get all campaigns', 'flashsale-core'); ?></span>
                                    </div>
                                    <div class="fs-api-endpoint">
                                        <code>GET /wp-json/flashsale/v1/campaigns/{id}</code>
                                        <span><?php _e('Get specific campaign', 'flashsale-core'); ?></span>
                                    </div>
                                    <div class="fs-api-endpoint">
                                        <code>POST /wp-json/flashsale/v1/campaigns</code>
                                        <span><?php _e('Create new campaign', 'flashsale-core'); ?></span>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Display Settings -->
                <div class="fs-card">
                    <div class="fs-card-top-page">
                        <h2><?php _e('Display Settings', 'flashsale-core'); ?></h2>
                    </div>
                    <div class="fs-card-content">
                        <div class="fs-form-group">
                            <label for="price_display_format" class="fs-label">
                                <?php _e('Price Display Format', 'flashsale-core'); ?>
                            </label>
                            <select id="price_display_format" name="price_display_format" class="fs-select">
                                <option value="default" <?php selected(get_option('fs_price_display_format', 'default'), 'default'); ?>>
                                    <?php _e('Default (Sale price + Original price)', 'flashsale-core'); ?>
                                </option>
                                <option value="percentage" <?php selected(get_option('fs_price_display_format', 'default'), 'percentage'); ?>>
                                    <?php _e('With percentage discount', 'flashsale-core'); ?>
                                </option>
                                <option value="savings" <?php selected(get_option('fs_price_display_format', 'default'), 'savings'); ?>>
                                    <?php _e('With savings amount', 'flashsale-core'); ?>
                                </option>
                            </select>
                        </div>
                        
                        <div class="fs-form-group">
                            <label class="fs-checkbox-label">
                                <input type="checkbox" 
                                       name="show_countdown_timer" 
                                       value="1" 
                                       <?php checked(get_option('fs_show_countdown_timer', 1)); ?>>
                                <span class="fs-checkmark"></span>
                                <?php _e('Show Countdown Timer', 'flashsale-core'); ?>
                            </label>
                            <p class="fs-help-text">
                                <?php _e('Display countdown timer on product pages for time-limited campaigns.', 'flashsale-core'); ?>
                            </p>
                        </div>
                        
                        <div class="fs-form-group">
                            <label class="fs-checkbox-label">
                                <input type="checkbox" 
                                       name="show_stock_progress" 
                                       value="1" 
                                       <?php checked(get_option('fs_show_stock_progress', 1)); ?>>
                                <span class="fs-checkmark"></span>
                                <?php _e('Show Stock Progress Bar', 'flashsale-core'); ?>
                            </label>
                            <p class="fs-help-text">
                                <?php _e('Display stock progress bar for limited quantity campaigns.', 'flashsale-core'); ?>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- System Info -->
                <div class="fs-card">
                    <div class="fs-card-top-page">
                        <h2><?php _e('System Information', 'flashsale-core'); ?></h2>
                    </div>
                    <div class="fs-card-content">
                        <div class="fs-system-info">
                            <div class="fs-info-item">
                                <span class="fs-info-label"><?php _e('Plugin Version:', 'flashsale-core'); ?></span>
                                <span class="fs-info-value"><?php echo FLASHSALE_CORE_VERSION; ?></span>
                            </div>
                            
                            <div class="fs-info-item">
                                <span class="fs-info-label"><?php _e('WordPress Version:', 'flashsale-core'); ?></span>
                                <span class="fs-info-value"><?php echo get_bloginfo('version'); ?></span>
                            </div>
                            
                            <div class="fs-info-item">
                                <span class="fs-info-label"><?php _e('WooCommerce Version:', 'flashsale-core'); ?></span>
                                <span class="fs-info-value">
                                    <?php echo defined('WC_VERSION') ? WC_VERSION : __('Not installed', 'flashsale-core'); ?>
                                </span>
                            </div>
                            
                            <div class="fs-info-item">
                                <span class="fs-info-label"><?php _e('PHP Version:', 'flashsale-core'); ?></span>
                                <span class="fs-info-value"><?php echo PHP_VERSION; ?></span>
                            </div>
                            
                            <div class="fs-info-item">
                                <span class="fs-info-label"><?php _e('Database Version:', 'flashsale-core'); ?></span>
                                <span class="fs-info-value"><?php echo get_option('fs_db_version', '1.0.0'); ?></span>
                            </div>
                        </div>
                        
                        <div class="fs-actions-group">
                            <button type="button" class="fs-btn fs-btn-outline" id="clear-cache">
                                <span class="dashicons dashicons-update"></span>
                                <?php _e('Clear Cache', 'flashsale-core'); ?>
                            </button>

                            <button type="button" class="fs-btn fs-btn-outline" id="export-settings">
                                <span class="dashicons dashicons-download"></span>
                                <?php _e('Export Settings', 'flashsale-core'); ?>
                            </button>

                            <button type="button" class="fs-btn fs-btn-warning" id="force-create-tables">
                                <span class="dashicons dashicons-database-add"></span>
                                <?php _e('Force Create Tables', 'flashsale-core'); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="fs-form-actions">
                <button type="submit" name="submit" class="fs-btn fs-btn-primary fs-btn-large">
                    <span class="dashicons dashicons-yes"></span>
                    <?php _e('Save Settings', 'flashsale-core'); ?>
                </button>
            </div>
        </form>
    </div>
</div>



<script>
jQuery(document).ready(function($) {
    // Clear cache
    $('#clear-cache').on('click', function() {
        var $btn = $(this);
        $btn.prop('disabled', true).text('<?php _e('Clearing...', 'flashsale-core'); ?>');
        
        // AJAX request would go here
        setTimeout(function() {
            $btn.prop('disabled', false).html('<span class="dashicons dashicons-yes"></span> <?php _e('Cache Cleared', 'flashsale-core'); ?>');
            
            setTimeout(function() {
                $btn.html('<span class="dashicons dashicons-update"></span> <?php _e('Clear Cache', 'flashsale-core'); ?>');
            }, 2000);
        }, 1000);
    });
    
    // Export settings
    $('#export-settings').on('click', function() {
        // Create and download settings file
        var settings = {
            version: '<?php echo FLASHSALE_CORE_VERSION; ?>',
            exported_at: new Date().toISOString(),
            settings: {}
        };

        // Collect form data
        $('form input, form select').each(function() {
            var $input = $(this);
            var name = $input.attr('name');

            if (name && name !== '_wpnonce' && name !== 'submit') {
                if ($input.attr('type') === 'checkbox') {
                    settings.settings[name] = $input.is(':checked');
                } else {
                    settings.settings[name] = $input.val();
                }
            }
        });

        var dataStr = JSON.stringify(settings, null, 2);
        var dataBlob = new Blob([dataStr], {type: 'application/json'});
        var url = URL.createObjectURL(dataBlob);

        var link = document.createElement('a');
        link.href = url;
        link.download = 'flashsale-settings-' + new Date().toISOString().split('T')[0] + '.json';
        link.click();

        URL.revokeObjectURL(url);
    });

    // Force create tables
    $('#force-create-tables').on('click', function() {
        if (!confirm('<?php _e('This will recreate all database tables. Are you sure?', 'flashsale-core'); ?>')) {
            return;
        }

        var $btn = $(this);
        $btn.prop('disabled', true).text('<?php _e('Creating Tables...', 'flashsale-core'); ?>');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'fs_force_create_tables',
                nonce: '<?php echo wp_create_nonce('fs_admin_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    alert('<?php _e('Database tables created successfully!', 'flashsale-core'); ?>');
                    location.reload();
                } else {
                    alert('<?php _e('Error: ', 'flashsale-core'); ?>' + response.data.message);
                }
            },
            error: function() {
                alert('<?php _e('AJAX error occurred', 'flashsale-core'); ?>');
            },
            complete: function() {
                $btn.prop('disabled', false).html('<span class="dashicons dashicons-database-add"></span> <?php _e('Force Create Tables', 'flashsale-core'); ?>');
            }
        });
    });
});
</script>
