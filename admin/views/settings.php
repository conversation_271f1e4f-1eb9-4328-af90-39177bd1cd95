<?php
/**
 * Settings view for Quantity Promotion addon
 *
 * @package FlashSale_Quantity_Promotion
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get current settings
$enable_logging = get_option('fsqp_enable_logging', 1);
$apply_to_variations = get_option('fsqp_apply_to_variations', 1);
$show_savings_message = get_option('fsqp_show_savings_message', 1);
$max_rules_per_product = get_option('fsqp_max_rules_per_product', 10);
$cleanup_logs_days = get_option('fsqp_cleanup_logs_days', 30);
?>

<div class="fsqp-settings-container">
    <h3><?php _e('Quantity Promotion Settings', 'flashsale-quantity-promotion'); ?></h3>
    
    <form method="post" action="options.php">
        <?php
        settings_fields('fsqp_settings');
        do_settings_sections('fsqp_settings');
        ?>
        
        <table class="form-table">
            <tbody>
                <tr>
                    <th scope="row">
                        <label for="fsqp_enable_logging">
                            <?php _e('Enable Logging', 'flashsale-quantity-promotion'); ?>
                        </label>
                    </th>
                    <td>
                        <input type="checkbox" 
                               id="fsqp_enable_logging" 
                               name="fsqp_enable_logging" 
                               value="1" 
                               <?php checked($enable_logging, 1); ?> />
                        <p class="description">
                            <?php _e('Log promotion usage for analytics and reporting.', 'flashsale-quantity-promotion'); ?>
                        </p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="fsqp_apply_to_variations">
                            <?php _e('Apply to Product Variations', 'flashsale-quantity-promotion'); ?>
                        </label>
                    </th>
                    <td>
                        <input type="checkbox" 
                               id="fsqp_apply_to_variations" 
                               name="fsqp_apply_to_variations" 
                               value="1" 
                               <?php checked($apply_to_variations, 1); ?> />
                        <p class="description">
                            <?php _e('Apply quantity promotions to product variations as well as main products.', 'flashsale-quantity-promotion'); ?>
                        </p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="fsqp_show_savings_message">
                            <?php _e('Show Savings Message', 'flashsale-quantity-promotion'); ?>
                        </label>
                    </th>
                    <td>
                        <input type="checkbox" 
                               id="fsqp_show_savings_message" 
                               name="fsqp_show_savings_message" 
                               value="1" 
                               <?php checked($show_savings_message, 1); ?> />
                        <p class="description">
                            <?php _e('Display savings message in cart and checkout.', 'flashsale-quantity-promotion'); ?>
                        </p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="fsqp_max_rules_per_product">
                            <?php _e('Max Rules per Product', 'flashsale-quantity-promotion'); ?>
                        </label>
                    </th>
                    <td>
                        <input type="number" 
                               id="fsqp_max_rules_per_product" 
                               name="fsqp_max_rules_per_product" 
                               value="<?php echo esc_attr($max_rules_per_product); ?>" 
                               min="1" 
                               max="50" 
                               class="small-text" />
                        <p class="description">
                            <?php _e('Maximum number of quantity rules allowed per product.', 'flashsale-quantity-promotion'); ?>
                        </p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="fsqp_cleanup_logs_days">
                            <?php _e('Log Cleanup (Days)', 'flashsale-quantity-promotion'); ?>
                        </label>
                    </th>
                    <td>
                        <input type="number" 
                               id="fsqp_cleanup_logs_days" 
                               name="fsqp_cleanup_logs_days" 
                               value="<?php echo esc_attr($cleanup_logs_days); ?>" 
                               min="1" 
                               max="365" 
                               class="small-text" />
                        <p class="description">
                            <?php _e('Automatically delete promotion logs older than this many days. Set to 0 to disable cleanup.', 'flashsale-quantity-promotion'); ?>
                        </p>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <?php submit_button(); ?>
    </form>
    
    <hr>
    
    <h3><?php _e('Statistics', 'flashsale-quantity-promotion'); ?></h3>
    
    <?php
    // Get some basic statistics
    global $wpdb;
    $logs_table = $wpdb->prefix . 'fsqp_promotion_logs';
    
    if ($wpdb->get_var("SHOW TABLES LIKE '$logs_table'") === $logs_table) {
        $total_uses = $wpdb->get_var("SELECT COUNT(*) FROM $logs_table");
        $total_savings = $wpdb->get_var("SELECT SUM(discount_amount) FROM $logs_table");
        $last_30_days = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $logs_table WHERE created_at >= %s",
            date('Y-m-d H:i:s', strtotime('-30 days'))
        ));
        ?>
        
        <table class="widefat">
            <tbody>
                <tr>
                    <td><strong><?php _e('Total Promotions Used', 'flashsale-quantity-promotion'); ?></strong></td>
                    <td><?php echo number_format($total_uses); ?></td>
                </tr>
                <tr>
                    <td><strong><?php _e('Total Customer Savings', 'flashsale-quantity-promotion'); ?></strong></td>
                    <td><?php echo wc_price($total_savings ?: 0); ?></td>
                </tr>
                <tr>
                    <td><strong><?php _e('Uses in Last 30 Days', 'flashsale-quantity-promotion'); ?></strong></td>
                    <td><?php echo number_format($last_30_days); ?></td>
                </tr>
            </tbody>
        </table>
        
        <?php
    } else {
        echo '<p>' . __('Statistics will be available after the first promotion is used.', 'flashsale-quantity-promotion') . '</p>';
    }
    ?>
    
    <hr>
    
    <h3><?php _e('Tools', 'flashsale-quantity-promotion'); ?></h3>
    
    <p>
        <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=flashsale&action=fsqp_cleanup_logs'), 'fsqp_cleanup_logs'); ?>" 
           class="button" 
           onclick="return confirm('<?php _e('Are you sure you want to clean up old logs?', 'flashsale-quantity-promotion'); ?>');">
            <?php _e('Clean Up Old Logs', 'flashsale-quantity-promotion'); ?>
        </a>
    </p>
    
    <p>
        <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=flashsale&action=fsqp_export_stats'), 'fsqp_export_stats'); ?>" 
           class="button">
            <?php _e('Export Statistics', 'flashsale-quantity-promotion'); ?>
        </a>
    </p>
</div>

<style>
.fsqp-settings-container {
    max-width: 800px;
}

.fsqp-settings-container .form-table th {
    width: 200px;
}

.fsqp-settings-container .widefat td {
    padding: 10px;
}

.fsqp-settings-container .widefat td:first-child {
    width: 250px;
}
</style>
