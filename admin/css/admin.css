/* FlashSale Core Admin Styles - WordPress Compatible */

/* Reset & Base - Use separate container to avoid conflicts */
.flashsale-admin-container {
    margin: 20px 0 0 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    border-radius: 8px;
    overflow: hidden;
}

.flashsale-admin-container * {
    box-sizing: border-box;
}



/* Header - WordPress style */
.fs-top-page {
    background: #0073aa;
    color: white;
    padding: 20px 30px;
    margin: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #005177;
    margin-bottom: 30px;
}

.fs-title {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
    color: white;
}

.fs-icon {
    font-size: 28px;
}

.fs-top-page-actions {
    display: flex;
    gap: 12px;
}

/* Form Elements */
.fs-select-wrapper {
    position: relative;
    display: inline-block;
    min-width: 200px;
}

.fs-select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 10px 40px 10px 16px;
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    width: 100%;
    cursor: pointer;
    transition: all 0.2s ease;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
}

.fs-select:hover {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px rgba(0, 115, 170, 0.1);
}

.fs-select:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.2);
}

.fs-input-small {
    padding: 8px 12px;
    font-size: 13px;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.fs-input-small:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px rgba(0, 115, 170, 0.2);
}

/* Form Elements */
.fs-select-wrapper {
    position: relative;
    display: inline-block;
    min-width: 200px;
}

.fs-select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 10px 40px 10px 16px;
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    width: 100%;
    cursor: pointer;
    transition: all 0.2s ease;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
}

.fs-select:hover {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px rgba(0, 115, 170, 0.1);
}

.fs-select:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.2);
}

.fs-input-small {
    padding: 8px 12px;
    font-size: 13px;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.fs-input-small:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px rgba(0, 115, 170, 0.2);
}

/* Discount Type Toggle (inspired by source) */
.fs-discount-type-toggle {
    display: flex;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.fs-discount-type-btn {
    background: white;
    border: none;
    padding: 8px 16px;
    font-size: 13px;
    font-weight: 600;
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    text-align: center;
}

.fs-discount-type-btn:hover {
    background: #f8f9fa;
    color: #0073aa;
}

.fs-discount-type-btn.active {
    background: #0073aa;
    color: white;
}

.fs-discount-type-btn:first-child {
    border-right: 1px solid #ddd;
}

.fs-discount-type-btn.active:first-child {
    border-right-color: #0073aa;
}

/* Flash Sale Table Styling */
.fs-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.fs-table th {
    background: #f8f9fa;
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 1px solid #e9ecef;
    font-size: 13px;
}

.fs-table td {
    padding: 16px;
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
}

.fs-table tbody tr:hover {
    background: #f8f9fa;
}

.fs-product-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.fs-product-info strong {
    color: #495057;
    font-size: 14px;
    line-height: 1.3;
}

.fs-product-meta {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: #6c757d;
}

.fs-product-meta span {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
}

.fs-regular-price {
    font-weight: 600;
    color: #495057;
}

.fs-products-table-container {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.fs-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.fs-table-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #495057;
}

/* Buttons */
.fs-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    cursor: pointer;
    line-height: 1.4;
}

.fs-btn-primary {
    background: #0073aa;
    color: white;
    border-color: #005177;
}

.fs-btn-primary:hover {
    background: #005177;
    color: white;
    text-decoration: none;
}

.fs-btn-secondary {
    background: #50575e;
    color: white;
}

.fs-btn-success {
    background: #00a32a;
    color: white;
}

.fs-btn-danger {
    background: #d63638;
    color: white;
}

.fs-btn-outline {
    background: transparent;
    border-color: #0073aa;
    color: #0073aa;
}

.fs-btn:hover {
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
}

.fs-btn-small {
    padding: 6px 12px;
    font-size: 12px;
}

.fs-btn-large {
    padding: 14px 28px;
    font-size: 16px;
}

/* Dashboard Layout */
.fs-dashboard {
    padding: 0;
    display: grid;
    gap: 30px;
}

.fs-stats-overview {
    margin-bottom: 40px;
    /* padding: 0 40px; */
}

.fs-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 0;
}

/* Stat Cards */
.fs-stat-card {
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.2s ease;
}

.fs-stat-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.fs-stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    flex-shrink: 0;
}

.fs-stat-primary .fs-stat-icon {
    background: #0073aa;
    color: white;
}

.fs-stat-success .fs-stat-icon {
    background: #00a32a;
    color: white;
}

.fs-stat-info .fs-stat-icon {
    background: #00a0d2;
    color: white;
}

.fs-stat-warning .fs-stat-icon {
    background: #dba617;
    color: white;
}

.fs-stat-content {
    flex: 1;
}

.fs-stat-number {
    margin: 0 0 4px 0;
    font-size: 24px;
    font-weight: 700;
    color: #495057;
    line-height: 1;
}

.fs-stat-label {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
}

/* Cards */
.fs-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    overflow: hidden;
    transition: all 0.2s ease;
}

.fs-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.fs-card-top-page {
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.fs-card-top-page h2 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.fs-card-content {
    padding: 24px;
}

.fs-card-footer {
    padding: 16px 24px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    text-align: center;
}

/* Status Badges */
.fs-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.fs-status-active {
    background: #d4edda;
    color: #155724;
}

.fs-status-inactive {
    background: #f8d7da;
    color: #721c24;
}

/* Empty State */
.fs-empty-state {
    text-align: center;
    padding: 60px 30px;
}

.fs-empty-state .dashicons {
    font-size: 48px;
    color: #adb5bd;
    margin-bottom: 16px;
}

.fs-empty-state h3 {
    margin: 0 0 8px;
    color: #6c757d;
    font-size: 18px;
    font-weight: 600;
}

.fs-empty-state p {
    color: #adb5bd;
    margin-bottom: 20px;
    font-size: 14px;
}

/* Quick Actions */
.fs-quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 16px;
}

.fs-quick-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 20px 16px;
    text-decoration: none;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
    color: #495057;
    font-weight: 500;
    font-size: 14px;
}

.fs-quick-action:hover {
    border-color: #0073aa;
    background: white;
    color: #0073aa;
    text-decoration: none;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
}

.fs-quick-action .dashicons {
    font-size: 24px;
    color: #0073aa;
}

/* Addon Categories */
.fs-addon-categories {
    display: flex;
    gap: 12px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.fs-category-btn {
    padding: 8px 16px;
    border: 1px solid #ddd;
    background: white;
    color: #666;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.fs-category-btn:hover {
    border-color: #0073aa;
    color: #0073aa;
}

.fs-category-btn.active {
    background: #0073aa;
    border-color: #0073aa;
    color: white;
}

/* Addon Grid */
.fs-addons-container {
    margin-bottom: 30px;
}

.fs-addons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.fs-addon-item {
    margin-bottom: 0;
}

.fs-addon-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.fs-addon-card:hover {
    border-color: #0073aa;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
}

.fs-addon-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.fs-addon-icon {
    font-size: 24px;
    color: #0073aa;
}

.fs-addon-content {
    flex: 1;
    margin-bottom: 16px;
}

.fs-addon-title {
    margin: 0 0 8px;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.fs-addon-description {
    margin: 0 0 12px;
    color: #6c757d;
    font-size: 14px;
    line-height: 1.4;
}

.fs-addon-meta {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: #adb5bd;
}

.fs-addon-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.fs-addon-status-badge {
    margin-left: auto;
}

.fs-status-coming-soon {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.fs-btn-disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.fs-addons-empty {
    margin-top: 40px;
}

/* Campaign Grid */
.fs-campaigns-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.fs-campaign-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.fs-campaign-card:hover {
    border-color: #0073aa;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
}

.fs-campaign-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.fs-campaign-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.fs-campaign-title a {
    color: #495057;
    text-decoration: none;
}

.fs-campaign-title a:hover {
    color: #0073aa;
}

.fs-campaign-meta {
    display: flex;
    gap: 16px;
    margin-bottom: 12px;
    font-size: 13px;
}

.fs-meta-label {
    color: #6c757d;
    font-weight: 500;
}

.fs-meta-value {
    color: #495057;
}

.fs-campaign-dates {
    margin-bottom: 12px;
    font-size: 13px;
}

.fs-date-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
}

.fs-date-label {
    color: #6c757d;
    font-weight: 500;
}

.fs-date-value {
    color: #495057;
}

.fs-campaign-description {
    margin-bottom: 16px;
    color: #6c757d;
    font-size: 14px;
    line-height: 1.4;
}

.fs-campaign-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.fs-filters {
    display: flex;
    gap: 12px;
}

.fs-filters select {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .fs-top-page,
    .fs-dashboard {
        padding-left: 30px;
        padding-right: 30px;
    }
}

@media (max-width: 768px) {
    .fs-top-page {
        flex-direction: column;
        gap: 16px;
        text-align: center;
        padding: 20px;
    }
    
    .fs-dashboard {
        padding: 20px;
    }
    
    .fs-stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .fs-stat-card {
        padding: 20px;
        flex-direction: column;
        text-align: center;
    }
    
    .fs-quick-actions {
        grid-template-columns: 1fr;
    }
}

/* Form Elements */
.fs-form-group {
    margin-bottom: 20px;
}

.fs-form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.fs-form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 24px;
}

.fs-full-width {
    grid-column: 1 / -1;
}

.fs-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.fs-required {
    color: #dc3545;
    margin-left: 2px;
}

.fs-input,
.fs-textarea,
.fs-select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease;
    background: white;
    font-family: inherit;
}

.fs-input:focus,
.fs-textarea:focus,
.fs-select:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

.fs-help-text {
    display: block;
    margin-top: 4px;
    color: #6c757d;
    font-size: 12px;
}

.fs-checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-weight: 500;
    color: #495057;
}

.fs-checkbox {
    width: 16px;
    height: 16px;
    accent-color: #0073aa;
}

/* Form Actions */
.fs-form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-start;
    padding: 20px 0;
    border-top: 1px solid #e9ecef;
    margin-top: 24px;
}

/* Tables */
.fs-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.fs-table th,
.fs-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.fs-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.fs-table td {
    color: #6c757d;
    font-size: 14px;
}

.fs-table tr:hover {
    background: #f8f9fa;
}

/* Tabs */
.fs-tabs {
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 24px;
}

.fs-tab-nav {
    display: flex;
    gap: 0;
    list-style: none;
    margin: 0;
    padding: 0;
}

.fs-tab-nav li {
    margin: 0;
}

.fs-tab-nav a {
    display: block;
    padding: 12px 20px;
    text-decoration: none;
    color: #6c757d;
    font-weight: 500;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.fs-tab-nav a:hover,
.fs-tab-nav a.active {
    color: #0073aa;
    border-bottom-color: #0073aa;
}

.fs-tab-content {
    display: none;
}

.fs-tab-content.active {
    display: block;
}

/* Alerts */
.fs-alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 16px;
    border: 1px solid transparent;
}

.fs-alert-success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.fs-alert-error {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.fs-alert-warning {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.fs-alert-info {
    background: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}



/* Campaign Form Styles */
.fs-campaign-form-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
    margin-top: 20px;
}

.fs-form-sidebar {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0;
}

.fs-form-main {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    min-height: 600px;
}

.fs-form-section {
    padding: 20px;
    border-right: 1px solid #eee;
}

.fs-form-section:last-child {
    border-right: none;
}

/* Mobile responsive for form sections */
@media (max-width: 768px) {
    .fs-form-sidebar {
        grid-template-columns: 1fr;
    }

    .fs-form-section {
        border-right: none;
        border-bottom: 1px solid #eee;
    }

    .fs-form-section:last-child {
        border-bottom: none;
    }
}

.fs-form-section h3 {
    margin: 0 0 15px;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.fs-field {
    margin-bottom: 15px;
}

.fs-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.fs-field input:not([type="checkbox"]):not([type="radio"]),
.fs-field textarea,
.fs-field select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.fs-field input[type="checkbox"],
.fs-field input[type="radio"] {
    width: auto;
    margin-right: 8px;
}

.fs-field input:focus,
.fs-field textarea:focus,
.fs-field select:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

.fs-field small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 12px;
}

/* Page Actions */
.fs-page-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.fs-top-page-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.fs-main-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.fs-campaign-type-selector .fs-inline-form {
    display: flex;
    gap: 10px;
    align-items: center;
}

.fs-inline-form select {
    min-width: 200px;
}

.fs-campaign-type-selector .fs-btn-primary {
    flex: 0 0 auto;
}
.fs-filters select {padding-right: 2rem;}
/* Campaign Type Content */
.fs-campaign-type-content {
    max-width: 100%;
}

.fs-section-header {
    margin-bottom: 20px;
}

.fs-section-header h2 {
    margin: 0 0 10px;
    font-size: 20px;
    color: #333;
}

.fs-section-header p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Product Search */
.fs-product-search-section {
    margin-bottom: 25px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.fs-search-bar {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.fs-search-input-group {
    display: flex;
    gap: 10px;
}

.fs-search-input-group input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.fs-search-filters {
    display: flex;
    gap: 10px;
    align-items: center;
}

.fs-search-filters select {
    min-width: 200px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.fs-search-results {
    margin-top: 15px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    max-height: 300px;
    overflow-y: auto;
}

.fs-search-results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
    background: #f5f5f5;
}

.fs-search-results-header h4 {
    margin: 0;
    font-size: 14px;
}

.fs-search-results-list {
    padding: 10px;
}

.fs-search-result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.fs-search-result-item:last-child {
    border-bottom: none;
}

.fs-search-result-item:hover {
    background: #f9f9f9;
}

.fs-product-info strong {
    display: block;
    margin-bottom: 5px;
}

.fs-product-meta {
    font-size: 12px;
    color: #666;
}

.fs-product-meta span {
    margin-right: 10px;
}

/* Products Table */
.fs-products-table-section {
    margin-top: 20px;
}

.fs-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.fs-table-header h3 {
    margin: 0;
    font-size: 18px;
}

.fs-products-table-container {
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.fs-table th,
.fs-table td {
    padding: 15px 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
    vertical-align: middle;
}

.fs-table th {
    background: #f8f9fa;
    font-weight: 600;
    font-size: 12px;
    color: #333;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #dee2e6;
}

.fs-table tbody tr:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.fs-table tbody tr {
    transition: all 0.2s ease;
}

.fs-input-small {
    width: 80px !important;
    padding: 4px 6px !important;
    font-size: 12px !important;
}

.fs-discount-type-toggle {
    display: flex;
    margin-bottom: 5px;
}

.fs-discount-type-btn {
    flex: 1;
    padding: 4px 8px;
    border: 1px solid #ddd;
    background: #fff;
    cursor: pointer;
    font-size: 12px;
}

.fs-discount-type-btn:first-child {
    border-radius: 3px 0 0 3px;
}

.fs-discount-type-btn:last-child {
    border-radius: 0 3px 3px 0;
    border-left: none;
}

.fs-discount-type-btn.active {
    background: #0073aa;
    color: #fff;
    border-color: #0073aa;
}

/* Accordion Styles */
.fs-accordion {
    border: 1px solid #ddd;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 25px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.fs-accordion-header {
    background: #f8f9fa;
    padding: 18px 25px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ddd;
    transition: all 0.3s ease;
    position: relative;
}

.fs-accordion-header:hover {
    background: #e9ecef;
}

.fs-accordion-header.active {
    background: #0073aa;
    color: white;
}

.fs-accordion-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.fs-product-count {
    font-size: 14px;
    font-weight: 400;
    opacity: 0.8;
    margin-left: 8px;
}

.fs-accordion-icon {
    transition: transform 0.2s ease;
    font-size: 18px;
}

.fs-accordion-header.active .fs-accordion-icon {
    transform: rotate(180deg);
}

.fs-accordion-content {
    padding: 25px;
    display: none;
    background: white;
    border-top: 1px solid rgba(0,0,0,0.05);
}

.fs-accordion-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Loading Animations */
.fs-loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: fs-spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes fs-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fs-loading-overlay {
    position: relative;
}

.fs-loading-overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.fs-loading-overlay.loading::after {
    content: '';
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJWNk0xMiAxOFYyMk02IDEySDJNMjIgMTJIMThNMTkuMDcgMTkuMDdMMTYuMjQgMTYuMjRNNy43NiA3Ljc2TDQuOTMgNC45M00xOS4wNyA0LjkzTDE2LjI0IDcuNzZNNy43NiAxNi4yNEw0LjkzIDE5LjA3IiBzdHJva2U9IiMwMDczYWEiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxhbmltYXRlVHJhbnNmb3JtIGF0dHJpYnV0ZU5hbWU9InRyYW5zZm9ybSIgYXR0cmlidXRlVHlwZT0iWE1MIiB0eXBlPSJyb3RhdGUiIGZyb209IjAgMTIgMTIiIHRvPSIzNjAgMTIgMTIiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIi8+Cjwvc3ZnPgo=');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 24px 24px;
}

/* Custom Checkbox Styles */
.fs-checkbox-wrapper {
    position: relative;
    display: inline-block;
}

.fs-checkbox {
    opacity: 0;
    position: absolute;
    width: 18px;
    height: 18px;
    margin: 0;
}

.fs-checkbox-label {
    display: inline-block;
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 3px;
    background: #fff;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
}

.fs-checkbox:checked + .fs-checkbox-label {
    background: #0073aa;
    border-color: #0073aa;
}

.fs-checkbox:checked + .fs-checkbox-label::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.fs-checkbox-column {
    text-align: center;
    padding: 8px !important;
}

/* Enhanced Product Display */
.fs-product-row {
    transition: background-color 0.2s ease;
}

.fs-product-row:hover {
    background-color: #f8f9fa;
}

.fs-product-info-enhanced {
    display: flex;
    align-items: center;
    gap: 12px;
}

.fs-product-image {
    flex-shrink: 0;
}

.fs-product-thumb {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.fs-product-thumb-placeholder {
    width: 40px;
    height: 40px;
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
}

.fs-product-details {
    flex: 1;
    min-width: 0;
}

.fs-product-name {
    display: block;
    margin-bottom: 4px;
    font-size: 14px;
    line-height: 1.3;
}

.fs-product-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    font-size: 11px;
    color: #666;
    margin-top: 4px;
}

.fs-product-sku,
.fs-product-id {
    background: #f0f0f0;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
}

.fs-product-meta span {
    white-space: nowrap;
}

.fs-stock-status {
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 500;
}

.fs-in-stock {
    background: #d4edda;
    color: #155724;
}

.fs-out-of-stock {
    background: #f8d7da;
    color: #721c24;
}

/* Discount Controls */
.fs-discount-controls {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.fs-discount-input {
    margin-top: 4px;
}

/* Quantity Display */
.fs-quantity-display {
    text-align: center;
    padding: 8px;
}

.fs-sold-qty {
    font-weight: 600;
    color: #0073aa;
    font-size: 14px;
}

/* Cell Styling */
.fs-price-cell,
.fs-input-cell,
.fs-actions-cell {
    text-align: center;
    vertical-align: middle;
}

.fs-product-info-cell {
    max-width: 250px;
}

/* Search Enhancement */
.fs-search-input-group {
    position: relative;
}

.fs-search-input-group.loading input {
    padding-right: 40px;
}

.fs-search-loading {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    display: none;
}

.fs-search-input-group.loading .fs-search-loading {
    display: block;
}

/* Responsive */
@media (max-width: 1200px) {
    .fs-campaign-form-container {
        gap: 20px;
    }
}

/* Date Time Modal */
.fs-datetime-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.fs-datetime-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    min-width: 400px;
    max-width: 90vw;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.fs-datetime-content h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    color: #333;
}

.fs-datetime-fields {
    margin-bottom: 20px;
}

.fs-datetime-field {
    margin-bottom: 15px;
}

.fs-datetime-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #555;
}

.fs-datetime-field input[type="datetime-local"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
}

.fs-datetime-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.fs-datetime-actions .fs-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.fs-btn-secondary {
    background: #f1f1f1;
    color: #333;
}

.fs-btn-primary {
    background: #667eea;
    color: white;
}

.fs-btn-secondary:hover {
    background: #e1e1e1;
}

.fs-btn-primary:hover {
    background: #5a6fd8;
}

.fs-btn-warning {
    background: #f39c12;
    color: white;
    border: 1px solid #e67e22;
}

.fs-btn-warning:hover {
    background: #e67e22;
}

@media (max-width: 768px) {
    .fs-page-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .fs-search-input-group {
        flex-direction: column;
    }

    .fs-search-filters {
        flex-direction: column;
    }

    .fs-table-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .fs-products-table-container {
        overflow-x: auto;
    }

    .fs-table {
        min-width: 800px;
    }

    .fs-datetime-content {
        min-width: 300px;
        margin: 20px;
    }
}

/* ===== ENHANCED PRODUCTS TABLE ===== */
.fs-products-section {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.fs-products-table-wrapper {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.fs-products-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.fs-products-table thead th {
    background: #f1f1f1;
    color: #333;
    font-weight: 600;
    font-size: 13px;
    padding: 12px;
    border: 1px solid #ddd;
    text-align: center;
}

.fs-products-table thead th:first-child {
    border-radius: 0;
}

.fs-products-table thead th:last-child {
    border-radius: 0;
}

.fs-products-table tbody tr {
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease;
}

.fs-products-table tbody tr:hover {
    background: #f8f9fa;
}

.fs-products-table tbody tr:last-child {
    border-bottom: none;
}

.fs-products-table td {
    padding: 16px 12px;
    vertical-align: middle;
    border: none;
}

/* Enhanced Product Info Cell */
.fs-product-info-enhanced {
    display: flex;
    align-items: center;
    gap: 12px;
    max-width: 280px;
}

.fs-product-image {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    border-radius: 6px;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fs-product-thumb {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fs-product-thumb-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #e9ecef;
    color: #6c757d;
}

.fs-product-details {
    flex: 1;
    min-width: 0;
}

.fs-product-name {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin: 0 0 6px 0;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Enhanced Price Cell */
.fs-price-cell {
    text-align: center;
    font-weight: 600;
    color: #28a745;
    font-size: 14px;
}

/* Enhanced Discount Controls */
.fs-discount-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
}

.fs-discount-type-toggle {
    display: flex;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid #ddd;
}

.fs-discount-type-btn {
    background: #fff;
    border: none;
    padding: 6px 10px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 32px;
}

.fs-discount-type-btn:hover {
    background: #f8f9fa;
}

.fs-discount-type-btn.active {
    background: #667eea;
    color: #fff;
}

/* Enhanced Input Cells */
.fs-input-cell {
    text-align: center;
}

.fs-input-small {
    width: 100% !important;
    padding: 6px 8px !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    font-size: 12px !important;
    text-align: center !important;
    transition: all 0.2s ease !important;
    box-sizing: border-box !important;
}

.fs-input-small:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
    outline: none !important;
}

/* Enhanced Actions Cell */
.fs-actions-cell {
    text-align: center;
}

.fs-btn-small {
    padding: 6px 8px;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.fs-btn-danger:hover {
    background: #dc3545;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.fs-products-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.fs-product-card {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 0;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.fs-product-card:hover {
    border-color: #007cba;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.fs-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.fs-card-checkbox {
    display: flex;
    align-items: center;
}

.fs-card-actions {
    display: flex;
    gap: 8px;
}

.fs-card-body {
    padding: 20px;
}

.fs-product-info {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.fs-product-image {
    flex-shrink: 0;
}

.fs-product-details {
    flex: 1;
}

.fs-product-name {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    line-height: 1.3;
}

.fs-product-settings {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.fs-settings-row {
    display: flex;
    gap: 15px;
}

.fs-setting-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.fs-setting-group.fs-full-width {
    flex: 1 1 100%;
}

.fs-setting-group label {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.fs-input-medium {
    width: 100% !important;
    padding: 8px 12px !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    font-size: 13px !important;
}

.fs-bulk-header {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 20px;
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 20px;
}

.fs-select-all-text {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

/* Card responsive adjustments */
@media (max-width: 768px) {
    .fs-products-cards {
        grid-template-columns: 1fr;
    }

    .fs-settings-row {
        flex-direction: column;
        gap: 10px;
    }

    .fs-product-info {
        flex-direction: column;
        gap: 10px;
    }
}

/* ===== BULK ACTIONS STYLES ===== */
.fs-bulk-menu {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    min-width: 180px;
    z-index: 9999;
    position: absolute;
    display: none;
}

.fs-bulk-menu-item {
    padding: 10px 15px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s;
    font-size: 14px;
    color: #333;
}

.fs-bulk-menu-item:last-child {
    border-bottom: none;
}

.fs-bulk-menu-item:hover {
    background-color: #f8f9fa;
}

.fs-bulk-menu-item.fs-bulk-delete:hover {
    background-color: #fee;
    color: #d63384;
}

.fs-bulk-menu-item .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.fs-bulk-actions-btn.fs-has-selection {
    background-color: #0073aa;
    color: white;
    border-color: #0073aa;
}

.fs-bulk-actions-btn.fs-has-selection:hover {
    background-color: #005a87;
    border-color: #005a87;
}

/* Checkbox styling */
.fs-product-checkbox {
    margin: 0;
    transform: scale(1.1);
}

.fs-products-table th input[type="checkbox"] {
    margin: 0;
}

/* Selection highlight */
.fs-products-table tr.fs-selected {
    background-color: #e8f4fd;
}

.fs-products-table tr.fs-selected td {
    border-color: #0073aa;
}

/* Section header with bulk actions */
.fs-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.fs-section-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.fs-section-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* Responsive bulk actions */
@media (max-width: 768px) {
    .fs-bulk-menu {
        min-width: 150px;
    }

    .fs-bulk-menu-item {
        padding: 8px 12px;
        font-size: 14px;
    }

    .fs-section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .fs-section-actions {
        justify-content: center;
    }
}

/* Product Variations & Progress Bars */
.fs-variation-badge {
    background: #0073aa;
    color: white;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 8px;
    font-weight: 500;
}

.fs-parent-id {
    color: #666;
    font-size: 12px;
    margin-left: 4px;
}

.fs-add-progress {
    margin-top: 8px;
    padding: 6px 0;
}

.fs-progress-bar {
    background: #f0f0f0;
    border-radius: 8px;
    height: 6px;
    overflow: hidden;
    margin-bottom: 4px;
}

.fs-progress-fill {
    background: linear-gradient(90deg, #0073aa, #00a0d2);
    height: 100%;
    border-radius: 8px;
    transition: width 0.3s ease;
    animation: progress-animate 2s ease-in-out infinite;
}

@keyframes progress-animate {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

.fs-progress-text {
    font-size: 12px;
    color: #666;
    font-style: italic;
}

/* Search Result Variations */
.fs-search-result-item[data-product-type="variation"] {
    border-left: 3px solid #0073aa;
}

.fs-search-result-item[data-product-type="variation"] .fs-product-info strong {
    color: #0073aa;
}

/* Progress Bar in Stock Progress */
.fs-stock-progress .fs-progress-bar {
    background: #e9ecef;
    border-radius: 4px;
    height: 8px;
    overflow: hidden;
    margin-top: 4px;
}

.fs-stock-progress .fs-progress-fill {
    background: linear-gradient(90deg, #28a745, #20c997);
    height: 100%;
    transition: width 0.5s ease;
    animation: none;
}

/* Loading Spinner Enhanced */
.fs-loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: fs-spin 1s linear infinite;
}

/* Responsive Progress Bars */
@media (max-width: 768px) {
    .fs-add-progress {
        padding: 4px 0;
    }
    
    .fs-progress-bar {
        height: 4px;
    }
    
    .fs-progress-text {
        font-size: 11px;
    }
    
    .fs-variation-badge {
        font-size: 10px;
        padding: 1px 4px;
    }
}