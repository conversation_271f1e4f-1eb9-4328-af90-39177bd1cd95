/**
 * Admin styles for Quantity Promotion
 */

/* Campaign Form */
.fsqp-campaign-form {
    max-width: 100%;
}

.fsqp-campaign-form .fs-section {
    margin-bottom: 30px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
}

.fsqp-campaign-form .fs-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

/* Rules Container */
.fsqp-rules-container {
    margin-top: 15px;
}

.fsqp-rules-header {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    align-items: center;
}

.fsqp-rules-table-container {
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.fsqp-rules-table {
    margin: 0;
    border: none;
}

.fsqp-rules-table th,
.fsqp-rules-table td {
    padding: 12px 8px;
    text-align: left;
    vertical-align: middle;
}

.fsqp-rules-table th {
    background: #f9f9f9;
    font-weight: 600;
    border-bottom: 2px solid #ddd;
}

.fsqp-rules-table input,
.fsqp-rules-table select {
    width: 100%;
    min-width: 80px;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
}

.fsqp-rules-table input[type="number"] {
    min-width: 60px;
}

.fsqp-product-select {
    min-width: 200px;
}

.fsqp-remove-rule {
    color: #a00;
    border-color: #a00;
}

.fsqp-remove-rule:hover {
    background: #a00;
    color: #fff;
}

.fsqp-no-rules {
    padding: 40px 20px;
    text-align: center;
    color: #666;
    background: #f9f9f9;
    border-top: 1px solid #ddd;
}

/* Preview Section */
.fsqp-preview-container {
    margin-top: 15px;
}

.fsqp-preview-content {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    margin-top: 10px;
}

.fsqp-preview-discount-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.fsqp-preview-discount-table th,
.fsqp-preview-discount-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.fsqp-preview-discount-table th {
    background: #2271b1;
    color: #fff;
    font-weight: 600;
}

.fsqp-preview-empty {
    text-align: center;
    color: #666;
    font-style: italic;
}

/* Modal Styles */
.fsqp-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fsqp-modal-content {
    background: #fff;
    border-radius: 4px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.fsqp-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #ddd;
    background: #f9f9f9;
}

.fsqp-modal-header h3 {
    margin: 0;
    font-size: 18px;
}

.fsqp-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fsqp-modal-close:hover {
    color: #000;
}

.fsqp-modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

/* Search Form */
.fsqp-search-form {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.fsqp-search-form input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.fsqp-search-results {
    min-height: 200px;
}

.fsqp-search-loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.fsqp-search-results-list {
    display: grid;
    gap: 10px;
}

.fsqp-product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    transition: all 0.2s ease;
}

.fsqp-product-item:hover {
    border-color: #2271b1;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.fsqp-product-info h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
}

.fsqp-product-info p {
    margin: 0;
    font-size: 12px;
    color: #666;
}

.fsqp-product-price {
    font-weight: 600;
    color: #2271b1;
}

.fsqp-add-product-btn {
    margin-left: 15px;
}

/* Field Groups */
.fs-field-group {
    display: grid;
    gap: 15px;
}

.fs-field label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.fs-field input[type="checkbox"] {
    width: auto !important;
    margin: 0;
}

.fs-field .description {
    margin: 5px 0 0 0;
    font-size: 13px;
    color: #666;
    font-style: italic;
}

/* Responsive */
@media (max-width: 768px) {
    .fsqp-rules-table-container {
        overflow-x: auto;
    }
    
    .fsqp-rules-table {
        min-width: 800px;
    }
    
    .fsqp-modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .fsqp-search-form {
        flex-direction: column;
    }
    
    .fsqp-product-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .fsqp-add-product-btn {
        margin-left: 0;
        align-self: flex-end;
    }
}

/* Loading States */
.fsqp-loading {
    opacity: 0.6;
    pointer-events: none;
}

.fsqp-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2271b1;
    border-radius: 50%;
    animation: fsqp-spin 1s linear infinite;
}

@keyframes fsqp-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.fsqp-message {
    padding: 10px 15px;
    border-radius: 4px;
    margin: 10px 0;
}

.fsqp-message.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.fsqp-message.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Validation Styles */
.fsqp-rule-row.has-error {
    background-color: #fff2f2;
}

.fsqp-rule-row.has-error input,
.fsqp-rule-row.has-error select {
    border-color: #dc3545;
}

.fsqp-validation-error {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

/* Highlight active rule */
.fsqp-rule-row:hover {
    background-color: #f8f9fa;
}

/* Button States */
.fsqp-add-rule:disabled,
.fsqp-search-products:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}
