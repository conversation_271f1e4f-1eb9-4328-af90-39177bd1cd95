/**
 * Horizontal Layout Styles for Quantity Promotion
 * 
 * This CSS provides styling for the improved horizontal layout
 * where quantity ranges are displayed inline under product information
 */

/* Horizontal Range Items */
.fsqp-range-item-horizontal {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
    position: relative;
}

.fsqp-range-controls-horizontal {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

/* Inline Field Styling */
.fsqp-range-field-inline {
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 0;
}

.fsqp-range-field-inline label {
    font-size: 11px;
    font-weight: 600;
    color: #555;
    white-space: nowrap;
    margin: 0;
    min-width: fit-content;
}

/* Tiny Input Fields */
.fs-input-tiny {
    height: 28px !important;
    padding: 4px 6px !important;
    font-size: 12px !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    text-align: center;
}

.fs-input-tiny:focus {
    border-color: #007cba !important;
    box-shadow: 0 0 0 1px #007cba !important;
}

/* Range Separator */
.fsqp-range-separator {
    font-weight: bold;
    color: #666;
    margin: 0 2px;
}

/* Small Discount Type Toggle */
.fs-discount-type-toggle-small {
    display: flex;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.fs-discount-type-btn-small {
    background: #fff;
    border: none;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 24px;
}

.fs-discount-type-btn-small:hover {
    background: #f0f0f0;
}

.fs-discount-type-btn-small.active {
    background: #007cba;
    color: white;
}

.fs-discount-type-btn-small:first-child {
    border-right: 1px solid #ddd;
}

/* Inline Discount Controls */
.fs-discount-controls-inline {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Inline Actions */
.fsqp-range-actions-inline {
    margin-left: auto;
}

.fs-btn-tiny {
    padding: 4px 6px !important;
    font-size: 11px !important;
    line-height: 1 !important;
    min-height: 24px !important;
    width: 24px !important;
}

.fs-btn-tiny .dashicons {
    font-size: 12px !important;
    width: 12px !important;
    height: 12px !important;
}

/* Add Range Button Inline */
.fsqp-add-range-inline {
    margin-top: 8px;
}

.fsqp-add-range-inline .fs-btn {
    font-size: 11px;
    padding: 6px 10px;
}

/* Product Details Spacing */
.fs-product-details .fsqp-quantity-ranges {
    margin-top: 12px;
    padding-top: 8px;
    border-top: 1px solid #eee;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .fsqp-range-controls-horizontal {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .fsqp-range-field-inline {
        width: 100%;
        justify-content: space-between;
    }
    
    .fsqp-range-actions-inline {
        margin-left: 0;
        align-self: flex-end;
    }
}

@media (max-width: 768px) {
    .fs-input-tiny {
        width: 60px !important;
    }
    
    .fsqp-range-field-inline {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .fsqp-range-field-inline label {
        font-size: 10px;
    }
}

/* Table Layout Improvements */
.fs-products-table .fs-product-info-column {
    min-width: 400px;
}

.fs-product-info-enhanced {
    display: flex;
    gap: 12px;
    align-items: flex-start;
    max-width: 100%;
}

.fs-product-image {
    flex-shrink: 0;
}

.fs-product-details {
    flex: 1;
    min-width: 0;
}

/* Hover Effects */
.fsqp-range-item-horizontal:hover {
    border-color: #007cba;
    box-shadow: 0 2px 4px rgba(0, 124, 186, 0.1);
}

/* Focus States */
.fsqp-range-item-horizontal:focus-within {
    border-color: #007cba;
    box-shadow: 0 0 0 1px #007cba;
}

/* Empty State Styling */
.fsqp-add-range-inline:only-child {
    text-align: center;
    padding: 16px;
    background: #f9f9f9;
    border: 2px dashed #ddd;
    border-radius: 6px;
}

.fsqp-add-range-inline:only-child .fs-btn {
    background: #007cba;
    color: white;
    border-color: #007cba;
}

.fsqp-add-range-inline:only-child .fs-btn:hover {
    background: #005a87;
    border-color: #005a87;
}

/* Animation for Adding/Removing Ranges */
.fsqp-range-item-horizontal {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Improved spacing for product meta */
.fs-product-meta {
    margin-bottom: 8px;
}

.fs-product-meta span {
    margin-right: 12px;
}
