<?php
/**
 * Admin functionality for FlashSale Core
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class FS_Admin {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
        $this->cleanup_demo_addons_if_needed();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_scripts']);
        add_action('wp_ajax_fs_admin_action', [$this, 'handle_ajax_request']);
        add_action('wp_ajax_fs_search_products', [$this, 'handle_search_products']);
        add_action('wp_ajax_fs_get_product_info', [$this, 'handle_get_product_info']);
        add_action('wp_ajax_fs_force_create_tables', [$this, 'handle_force_create_tables']);

        // Handle admin notices positioning
        add_action('admin_notices', [$this, 'handle_admin_notices_positioning'], 1);
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Debug: Check if menu already exists
        global $menu;
        foreach ($menu as $item) {
            if (isset($item[2]) && ($item[2] === 'flashsale-core' || $item[2] === 'aitfs-page')) {
                error_log('FlashSale menu already exists: ' . $item[2]);
                return; // Prevent duplicate menu
            }
        }
        
        $icon = FLASHSALE_CORE_PLUGIN_URL . 'assets/admin-icon.png';
        
        add_menu_page(
            __('FlashSale Core', 'flashsale-core'),
            __('FlashSale', 'flashsale-core'),
            'manage_options',
            'flashsale-core',
            [$this, 'render_main_page'],
            $icon,
            25
        );
        
        add_submenu_page(
            'flashsale-core',
            __('Campaigns', 'flashsale-core'),
            __('Campaigns', 'flashsale-core'),
            'manage_options',
            'flashsale-campaigns',
            [$this, 'render_campaigns_page']
        );
        
        add_submenu_page(
            'flashsale-core',
            __('Addons', 'flashsale-core'),
            __('Addons', 'flashsale-core'),
            'manage_options',
            'flashsale-addons',
            [$this, 'render_addons_page']
        );
        
        // Temporarily hide Settings menu - not needed yet
        /*
        add_submenu_page(
            'flashsale-core',
            __('Settings', 'flashsale-core'),
            __('Settings', 'flashsale-core'),
            'manage_options',
            'flashsale-settings',
            [$this, 'render_settings_page']
        );
        */
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'flashsale') === false) {
            return;
        }
        
        wp_enqueue_style(
            'fs-admin-style',
            FLASHSALE_CORE_PLUGIN_URL . 'admin/css/admin.css',
            [],
            FLASHSALE_CORE_VERSION
        );
        
        wp_enqueue_script(
            'fs-admin-script',
            FLASHSALE_CORE_PLUGIN_URL . 'admin/js/admin.js',
            ['jquery'],
            FLASHSALE_CORE_VERSION,
            true
        );

        // Enqueue bulk actions library
        wp_enqueue_script(
            'fs-bulk-actions',
            FLASHSALE_CORE_PLUGIN_URL . 'admin/js/bulk-actions.js',
            ['jquery', 'fs-admin-script'],
            FLASHSALE_CORE_VERSION,
            true
        );

        // Enqueue product search library
        wp_enqueue_script(
            'fs-product-search',
            FLASHSALE_CORE_PLUGIN_URL . 'admin/js/product-search.js',
            ['jquery', 'fs-admin-script'],
            FLASHSALE_CORE_VERSION,
            true
        );
        
        wp_localize_script('fs-admin-script', 'fs_admin', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('fs_admin_nonce'),
            'strings' => [
                'confirm_delete' => __('Are you sure you want to delete this item?', 'flashsale-core'),
                'saving' => __('Saving...', 'flashsale-core'),
                'saved' => __('Saved!', 'flashsale-core'),
                'error' => __('Error occurred!', 'flashsale-core')
            ]
        ]);
    }

    /**
     * Render main page
     */
    public function render_main_page() {
        $campaigns = FS()->campaigns()->get_active_campaigns();
        $addons = FS()->addons()->get_active_addons();
        
        include FLASHSALE_CORE_PLUGIN_DIR . 'admin/views/main-page.php';
    }
    
    /**
     * Render campaigns page
     */
    public function render_campaigns_page() {
        $action = $_GET['action'] ?? 'list';
        $campaign_id = $_GET['campaign_id'] ?? 0;
        
        switch ($action) {
            case 'edit':
                $this->render_campaign_form();
                break;
                
            case 'new':
                $this->render_campaign_form();
                break;

            default:
                $campaigns = FS()->campaigns()->get_all_campaigns();
                include FLASHSALE_CORE_PLUGIN_DIR . 'admin/views/campaigns-list.php';
                break;
        }
    }

    /**
     * Render campaign form
     */
    private function render_campaign_form() {
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'save_campaign') {
            $this->handle_campaign_form_submission();
            return;
        }

        // Validate campaign type for new campaigns
        if (!isset($_GET['campaign_id']) && empty($_GET['campaign_type'])) {
            wp_redirect(admin_url('admin.php?page=flashsale-campaigns'));
            exit;
        }

        include FLASHSALE_CORE_PLUGIN_DIR . 'admin/views/campaign-form.php';
    }

    /**
     * Handle campaign form submission
     */
    private function handle_campaign_form_submission() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['fs_campaign_nonce'], 'fs_save_campaign')) {
            wp_die(__('Security check failed', 'flashsale-core'));
        }

        $campaign_data = [
            'name' => sanitize_text_field($_POST['campaign_name']),
            'description' => sanitize_textarea_field($_POST['campaign_description']),
            'type' => sanitize_text_field($_POST['campaign_type']),
            'priority' => intval($_POST['campaign_priority']),
            'status' => isset($_POST['campaign_status']) ? 1 : 0,
            'start_date' => sanitize_text_field($_POST['start_date']),
            'end_date' => sanitize_text_field($_POST['end_date']),
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        ];

        $campaign_id = intval($_POST['campaign_id']);
        $products = $_POST['products'] ?? [];

        try {
            // Debug logging
            error_log("FS_Admin Form: Saving campaign - ID: $campaign_id, Type: " . $campaign_data['type']);
            error_log("FS_Admin Form: Products count: " . count($products));

            if ($campaign_id > 0) {
                // Update existing campaign
                $result = FS()->campaigns()->update_campaign($campaign_id, $campaign_data, $products);
                $message = __('Campaign updated successfully!', 'flashsale-core');
                $final_campaign_id = $campaign_id;
            } else {
                // Create new campaign
                $result = FS()->campaigns()->create_campaign($campaign_data, $products);
                $campaign_id = $result;
                $final_campaign_id = $result;
                $message = __('Campaign created successfully!', 'flashsale-core');
            }

            // Trigger save hook AFTER saving with the actual campaign ID
            if ($result) {
                error_log("FS_Admin Form: Campaign saved successfully, triggering hooks with ID: $final_campaign_id");
                do_action('fs_save_campaign', $final_campaign_id, $campaign_data, $_POST);
                do_action('ccp_save_campaign', $final_campaign_id, $campaign_data, $_POST);
            }

            if ($result) {
                // Redirect with success message
                wp_redirect(add_query_arg([
                    'page' => 'flashsale-campaigns',
                    'action' => 'edit',
                    'campaign_id' => $campaign_id,
                    'message' => 'success'
                ], admin_url('admin.php')));
                exit;
            } else {
                throw new Exception(__('Failed to save campaign', 'flashsale-core'));
            }
        } catch (Exception $e) {
            // Redirect with error message
            wp_redirect(add_query_arg([
                'page' => 'flashsale-campaigns',
                'action' => $campaign_id > 0 ? 'edit' : 'new',
                'campaign_id' => $campaign_id > 0 ? $campaign_id : null,
                'campaign_type' => $_POST['campaign_type'],
                'message' => 'error',
                'error_msg' => urlencode($e->getMessage())
            ], admin_url('admin.php')));
            exit;
        }
    }

    /**
     * Render addons page
     */
    public function render_addons_page() {
        // Get all addons from database (both active and inactive)
        $addons = FS()->addons()->get_all_addons();
        include FLASHSALE_CORE_PLUGIN_DIR . 'admin/views/addons-page.php';
    }
    
    /**
     * Render settings page
     */
    public function render_settings_page() {
        if (isset($_POST['submit'])) {
            $this->save_settings();
        }
        
        include FLASHSALE_CORE_PLUGIN_DIR . 'admin/views/settings-page.php';
    }
    
    /**
     * Save settings
     */
    private function save_settings() {
        if (!wp_verify_nonce($_POST['_wpnonce'], 'fs_settings_nonce')) {
            wp_die(__('Security check failed', 'flashsale-core'));
        }
        
        $settings = [
            'fs_enable_legacy_support' => isset($_POST['enable_legacy_support']) ? 1 : 0,
            'fs_enable_debug' => isset($_POST['enable_debug']) ? 1 : 0,
            'fs_cache_campaigns' => isset($_POST['cache_campaigns']) ? 1 : 0,
            'fs_cache_duration' => intval($_POST['cache_duration']),
            'fs_enable_stats' => isset($_POST['enable_stats']) ? 1 : 0,
            'fs_enable_api' => isset($_POST['enable_api']) ? 1 : 0
        ];
        
        foreach ($settings as $option => $value) {
            update_option($option, $value);
        }
        
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success"><p>' . 
                 __('Settings saved successfully!', 'flashsale-core') . 
                 '</p></div>';
        });
    }
    
    /**
     * Handle AJAX requests
     */
    public function handle_ajax_request() {
        if (!wp_verify_nonce($_POST['nonce'], 'fs_admin_nonce')) {
            wp_die(__('Security check failed', 'flashsale-core'));
        }
        
        $action = $_POST['fs_action'] ?? '';
        
        switch ($action) {
            case 'toggle_addon':
                $this->toggle_addon();
                break;

            case 'delete_campaign':
                $this->delete_campaign();
                break;

            case 'save_campaign':
                $this->save_campaign();
                break;

            case 'search_products':
                $this->search_products();
                break;
                
            case 'remove_campaign_product':
                $this->remove_campaign_product();
                break;

            default:
                wp_send_json_error(['message' => __('Invalid action', 'flashsale-core')]);
                break;
        }
    }
    
    /**
     * Toggle addon status
     */
    private function toggle_addon() {
        $addon_slug = sanitize_text_field($_POST['addon_slug']);
        $status = $_POST['status'] === 'activate';
        
        if ($status) {
            $result = FS()->addons()->activate_addon($addon_slug);
        } else {
            $result = FS()->addons()->deactivate_addon($addon_slug);
        }
        
        if ($result) {
            wp_send_json_success(['message' => __('Addon status updated', 'flashsale-core')]);
        } else {
            wp_send_json_error(['message' => __('Failed to update addon status', 'flashsale-core')]);
        }
    }
    
    /**
     * Delete campaign
     */
    private function delete_campaign() {
        $campaign_id = intval($_POST['campaign_id']);
        
        $result = FS()->campaigns()->delete_campaign($campaign_id);
        
        if ($result) {
            wp_send_json_success(['message' => __('Campaign deleted successfully', 'flashsale-core')]);
        } else {
            wp_send_json_error(['message' => __('Failed to delete campaign', 'flashsale-core')]);
        }
    }
    
    /**
     * Save campaign
     */
    private function save_campaign() {
        $campaign_data = [
            'name' => sanitize_text_field($_POST['name']),
            'description' => sanitize_textarea_field($_POST['description']),
            'type' => sanitize_text_field($_POST['type']),
            'priority' => intval($_POST['priority']),
            'status' => intval($_POST['status']),
            'start_date' => sanitize_text_field($_POST['start_date']),
            'end_date' => sanitize_text_field($_POST['end_date']),
            'global_settings' => $_POST['global_settings'] ?? []
        ];

        $campaign_id = intval($_POST['campaign_id']);

        // Debug logging
        error_log("FS_Admin: Saving campaign - ID: $campaign_id, Type: " . $campaign_data['type']);
        error_log("FS_Admin: Products in POST: " . (isset($_POST['products']) ? count($_POST['products']) : 'none'));

        if ($campaign_id > 0) {
            $result = FS()->campaigns()->update_campaign($campaign_id, $campaign_data);
            $final_campaign_id = $campaign_id;
        } else {
            $result = FS()->campaigns()->create_campaign($campaign_data);
            $final_campaign_id = $result;
        }

        // Trigger save hook AFTER saving with the actual campaign ID
        if ($result) {
            error_log("FS_Admin: Campaign saved successfully, triggering hooks with ID: $final_campaign_id");
            do_action('fs_save_campaign', $final_campaign_id, $campaign_data, $_POST);
            do_action('ccp_save_campaign', $final_campaign_id, $campaign_data, $_POST);
        }

        if ($result) {
            wp_send_json_success([
                'message' => __('Campaign saved successfully', 'flashsale-core'),
                'campaign_id' => $final_campaign_id
            ]);
        } else {
            wp_send_json_error(['message' => __('Failed to save campaign', 'flashsale-core')]);
        }
    }

    /**
     * Remove product from campaign
     */
    private function remove_campaign_product() {
        $campaign_id = intval($_POST['campaign_id']);
        $product_id = intval($_POST['product_id']);
        
        if (!$campaign_id || !$product_id) {
            wp_send_json_error(['message' => __('Invalid campaign or product ID', 'flashsale-core')]);
        }
        
        $result = FS()->campaigns()->remove_campaign_product($campaign_id, $product_id);
        
        if ($result) {
            wp_send_json_success(['message' => __('Product removed from campaign successfully', 'flashsale-core')]);
        } else {
            wp_send_json_error(['message' => __('Failed to remove product from campaign', 'flashsale-core')]);
        }
    }

    /**
     * Search products for campaign
     */
    private function search_products() {
        $search_term = sanitize_text_field($_POST['search_term']);

        if (empty($search_term)) {
            wp_send_json_error(['message' => __('Search term is required', 'flashsale-core')]);
        }

        $args = [
            'post_type' => 'product',
            'post_status' => 'publish',
            's' => $search_term,
            'posts_per_page' => 20,
            'meta_query' => [
                [
                    'key' => '_visibility',
                    'value' => ['exclude-from-search', 'exclude-from-catalog'],
                    'compare' => 'NOT IN'
                ]
            ]
        ];

        $products = get_posts($args);
        $results = [];

        foreach ($products as $product) {
            $wc_product = wc_get_product($product->ID);
            if (!$wc_product) continue;

            $results[] = [
                'id' => $product->ID,
                'title' => $product->post_title,
                'price' => $wc_product->get_price(),
                'regular_price' => $wc_product->get_regular_price(),
                'sale_price' => $wc_product->get_sale_price(),
                'sku' => $wc_product->get_sku(),
                'stock_status' => $wc_product->get_stock_status(),
                'image' => wp_get_attachment_image_url($wc_product->get_image_id(), 'thumbnail')
            ];
        }

        wp_send_json_success(['products' => $results]);
    }

    /**
     * Handle search products AJAX request
     */
    public function handle_search_products() {
        if (!wp_verify_nonce($_POST['nonce'], 'fs_search_products')) {
            wp_send_json_error(['message' => __('Security check failed', 'flashsale-core')]);
        }

        $query = sanitize_text_field($_POST['query']);
        $category_id = intval($_POST['category_id']);

        // Search both products and variations
        $args = [
            'post_type' => ['product', 'product_variation'],
            'post_status' => 'publish',
            'posts_per_page' => 20,
            'meta_query' => [
                [
                    'key' => '_visibility',
                    'value' => ['exclude-from-search', 'exclude-from-catalog'],
                    'compare' => 'NOT IN'
                ]
            ]
        ];

        if (!empty($query)) {
            $args['s'] = $query;
            
            // Also search in product variations by attribute values
            $args['meta_query']['relation'] = 'OR';
            $args['meta_query'][] = [
                'key' => 'attribute_%',
                'value' => $query,
                'compare' => 'LIKE'
            ];
        }

        if ($category_id > 0) {
            $args['tax_query'] = [
                [
                    'taxonomy' => 'product_cat',
                    'field' => 'term_id',
                    'terms' => $category_id
                ]
            ];
        }

        $products = get_posts($args);
        $results = [];

        foreach ($products as $product_post) {
            $product = wc_get_product($product_post->ID);
            if ($product) {
                $product_name = $product->get_name();
                
                // For variations, show parent product name + variation attributes
                if ($product->is_type('variation')) {
                    $parent = wc_get_product($product->get_parent_id());
                    if ($parent) {
                        $variation_attributes = $product->get_variation_attributes();
                        $variation_text = implode(', ', array_filter($variation_attributes));
                        $product_name = $parent->get_name() . ' - ' . $variation_text;
                    }
                }
                
                $results[] = [
                    'id' => $product->get_id(),
                    'name' => $product_name,
                    'sku' => $product->get_sku(),
                    'price' => wc_price($product->get_regular_price()),
                    'price_raw' => $product->get_regular_price(),
                    'type' => $product->get_type(),
                    'parent_id' => $product->is_type('variation') ? $product->get_parent_id() : 0
                ];
            }
        }

        wp_send_json_success($results);
    }

    /**
     * Handle get product info AJAX request
     */
    public function handle_get_product_info() {
        if (!wp_verify_nonce($_POST['nonce'], 'fs_get_product_info')) {
            wp_send_json_error(['message' => __('Security check failed', 'flashsale-core')]);
        }

        $product_id = intval($_POST['product_id']);
        $product = wc_get_product($product_id);

        if (!$product) {
            wp_send_json_error(['message' => __('Product not found', 'flashsale-core')]);
        }

        $image_id = $product->get_image_id();
        $image_url = $image_id ? wp_get_attachment_image_url($image_id, 'thumbnail') : '';

        $product_data = [
            'id' => $product->get_id(),
            'name' => $product->get_name(),
            'sku' => $product->get_sku(),
            'price' => $product->get_regular_price(),
            'price_formatted' => wc_price($product->get_regular_price()),
            'in_stock' => $product->is_in_stock(),
            'stock_status' => $product->get_stock_status(),
            'image_url' => $image_url
        ];

        wp_send_json_success($product_data);
    }

    /**
     * Handle admin notices positioning
     */
    public function handle_admin_notices_positioning() {
        // Only on our admin pages
        $screen = get_current_screen();
        if (!$screen || strpos($screen->id, 'flashsale') === false) {
            return;
        }
        
        // Add CSS to ensure notices display outside container
        echo '<style>
            .wrap > .notice, 
            .wrap > .error,
            .wrap > .updated,
            .wrap > div[id*="setting-error"] {
                margin: 5px 0 15px !important;
                position: relative !important;
                z-index: 1000 !important;
            }
            
            #setting-error-tgmpa {
                background: #fff !important;
                border-left: 4px solid #dc3232 !important;
                box-shadow: 0 1px 1px rgba(0,0,0,.04) !important;
                padding: 1px 12px !important;
            }
        </style>';
    }

    /**
     * Clean up demo addons if they exist
     */
    private function cleanup_demo_addons_if_needed() {
        // Only run once
        if (get_option('fs_demo_addons_cleaned') === 'yes') {
            return;
        }

        global $wpdb;
        $table = FS_Database::get_table_name('addons');

        // List of demo addon slugs that should be removed
        $demo_addon_slugs = [
            'product-promotion',
            'category-promotion',
            'quantity-promotion',
            'order-promotion',
            'combo-promotion',
            'shipping-promotion',
            'upsell-promotion',
            'gift-promotion'
        ];

        // Remove demo addons
        foreach ($demo_addon_slugs as $slug) {
            $wpdb->delete($table, ['addon_slug' => $slug]);
        }

        // Mark as cleaned
        update_option('fs_demo_addons_cleaned', 'yes');
    }

    /**
     * Handle force create tables AJAX request
     */
    public function handle_force_create_tables() {
        if (!wp_verify_nonce($_POST['nonce'], 'fs_admin_nonce')) {
            wp_send_json_error(['message' => __('Security check failed', 'flashsale-core')]);
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Insufficient permissions', 'flashsale-core')]);
        }

        try {
            $database = new FS_Database();
            $results = $database->force_create_tables();

            wp_send_json_success([
                'message' => __('Database tables created successfully!', 'flashsale-core'),
                'results' => $results
            ]);
        } catch (Exception $e) {
            wp_send_json_error([
                'message' => __('Failed to create database tables: ', 'flashsale-core') . $e->getMessage()
            ]);
        }
    }
}
