/**
 * FlashSale Core - Bulk Actions Library
 * Shared functionality for bulk operations across core and addons
 */

(function($) {
    'use strict';

    // Global namespace for bulk actions
    window.FSBulkActions = {
        
        /**
         * Initialize bulk actions for a container
         * @param {Object} options Configuration options
         */
        init: function(options) {
            const defaults = {
                container: '.fs-products-section',
                selectAllId: '#fs-select-all-products',
                checkboxClass: '.fs-product-checkbox',
                bulkButtonId: '#fs-bulk-actions-btn',
                bulkMenuId: '#fs-bulk-actions-menu',
                actions: {
                    'delete': {
                        label: 'Delete Selected',
                        icon: 'dashicons-trash',
                        class: 'fs-bulk-delete',
                        confirm: 'Are you sure you want to delete the selected items?'
                    }
                },
                callbacks: {
                    onSelectionChange: null,
                    onBulkAction: null
                }
            };

            const settings = $.extend(true, {}, defaults, options);
            
            this.settings = settings;
            this.setupBulkActions();
            this.bindEvents();
        },

        /**
         * Setup bulk actions UI
         */
        setupBulkActions: function() {
            const settings = this.settings;
            
            // Create bulk actions dropdown if it doesn't exist
            if ($(settings.bulkMenuId).length === 0) {
                this.createBulkMenu();
            }

            // Initialize state
            this.updateBulkButton();
        },

        /**
         * Create bulk actions dropdown menu
         */
        createBulkMenu: function() {
            const settings = this.settings;
            
            let menuHtml = '<div id="' + settings.bulkMenuId.replace('#', '') + '" class="fs-bulk-menu" style="display: none;">';
            
            Object.keys(settings.actions).forEach(actionKey => {
                const action = settings.actions[actionKey];
                menuHtml += `
                    <div class="fs-bulk-menu-item ${action.class}" data-action="${actionKey}">
                        <span class="dashicons ${action.icon}"></span>
                        ${action.label}
                    </div>
                `;
            });
            
            menuHtml += '</div>';
            
            // Insert menu after bulk button
            $(settings.bulkButtonId).after(menuHtml);
        },

        /**
         * Bind event handlers
         */
        bindEvents: function() {
            const self = this;
            const settings = this.settings;

            // Select all functionality
            $(document).on('change', settings.selectAllId, function() {
                const isChecked = $(this).is(':checked');
                $(settings.checkboxClass).prop('checked', isChecked);
                self.updateBulkButton();
                self.triggerSelectionChange();
            });

            // Individual checkbox change
            $(document).on('change', settings.checkboxClass, function() {
                const totalCheckboxes = $(settings.checkboxClass).length;
                const checkedCheckboxes = $(settings.checkboxClass + ':checked').length;

                $(settings.selectAllId).prop('checked', totalCheckboxes === checkedCheckboxes);
                self.updateBulkButton();
                self.triggerSelectionChange();
            });

            // Bulk actions button click
            $(document).on('click', settings.bulkButtonId, function(e) {
                e.preventDefault();
                self.toggleBulkMenu();
            });

            // Bulk menu item click
            $(document).on('click', '.fs-bulk-menu-item', function(e) {
                e.preventDefault();
                const action = $(this).data('action');
                self.executeBulkAction(action);
                self.hideBulkMenu();
            });

            // Hide menu when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest(settings.bulkButtonId + ', ' + settings.bulkMenuId).length) {
                    self.hideBulkMenu();
                }
            });
        },

        /**
         * Update bulk actions button state
         */
        updateBulkButton: function() {
            const settings = this.settings;
            const checkedCount = $(settings.checkboxClass + ':checked').length;
            const $button = $(settings.bulkButtonId);

            $button.prop('disabled', checkedCount === 0);

            if (checkedCount > 0) {
                $button.text(`Bulk Actions (${checkedCount})`);
                $button.addClass('fs-has-selection');
            } else {
                $button.text('Bulk Actions');
                $button.removeClass('fs-has-selection');
            }
        },

        /**
         * Toggle bulk menu visibility
         */
        toggleBulkMenu: function() {
            const settings = this.settings;
            const $menu = $(settings.bulkMenuId);
            
            if ($menu.is(':visible')) {
                this.hideBulkMenu();
            } else {
                this.showBulkMenu();
            }
        },

        /**
         * Show bulk menu
         */
        showBulkMenu: function() {
            const settings = this.settings;
            const $button = $(settings.bulkButtonId);
            const $menu = $(settings.bulkMenuId);
            
            // Position menu below button
            const buttonOffset = $button.offset();
            const buttonHeight = $button.outerHeight();
            
            $menu.css({
                position: 'absolute',
                top: buttonOffset.top + buttonHeight + 5,
                left: buttonOffset.left,
                zIndex: 9999
            }).show();
        },

        /**
         * Hide bulk menu
         */
        hideBulkMenu: function() {
            const settings = this.settings;
            $(settings.bulkMenuId).hide();
        },

        /**
         * Execute bulk action
         */
        executeBulkAction: function(action) {
            const settings = this.settings;
            const selectedItems = this.getSelectedItems();
            
            if (selectedItems.length === 0) {
                alert('Please select items first.');
                return;
            }

            const actionConfig = settings.actions[action];
            
            // Show confirmation if required
            if (actionConfig.confirm) {
                if (!confirm(actionConfig.confirm)) {
                    return;
                }
            }

            // Trigger callback
            if (settings.callbacks.onBulkAction) {
                settings.callbacks.onBulkAction(action, selectedItems);
            } else {
                // Default actions
                this.handleDefaultAction(action, selectedItems);
            }
        },

        /**
         * Handle default bulk actions
         */
        handleDefaultAction: function(action, selectedItems) {
            switch (action) {
                case 'delete':
                    this.bulkDelete(selectedItems);
                    break;
                default:
                    console.log('Unknown bulk action:', action);
            }
        },

        /**
         * Get selected items
         */
        getSelectedItems: function() {
            const settings = this.settings;
            const selectedItems = [];
            
            $(settings.checkboxClass + ':checked').each(function() {
                const $row = $(this).closest('tr');
                selectedItems.push({
                    checkbox: this,
                    row: $row,
                    id: $(this).val() || $row.data('id'),
                    data: $row.data()
                });
            });
            
            return selectedItems;
        },

        /**
         * Bulk delete items
         */
        bulkDelete: function(selectedItems) {
            selectedItems.forEach(item => {
                $(item.row).fadeOut(300, function() {
                    $(this).remove();
                });
            });
            
            // Reset selection
            this.clearSelection();
            this.showNotification('Selected items deleted successfully.', 'success');
        },



        /**
         * Clear all selections
         */
        clearSelection: function() {
            const settings = this.settings;
            $(settings.selectAllId).prop('checked', false);
            $(settings.checkboxClass).prop('checked', false);
            this.updateBulkButton();
        },

        /**
         * Trigger selection change callback
         */
        triggerSelectionChange: function() {
            const settings = this.settings;
            if (settings.callbacks.onSelectionChange) {
                const selectedItems = this.getSelectedItems();
                settings.callbacks.onSelectionChange(selectedItems);
            }
        },

        /**
         * Show notification
         */
        showNotification: function(message, type) {
            // Use FlashSale admin notification if available
            if (window.FlashSaleAdmin && window.FlashSaleAdmin.showNotification) {
                window.FlashSaleAdmin.showNotification(message, type);
            } else {
                // Fallback to alert
                alert(message);
            }
        }
    };

})(jQuery);
