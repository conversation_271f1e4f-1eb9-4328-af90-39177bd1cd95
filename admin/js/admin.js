/**
 * FlashSale Core Admin JavaScript
 */

(function($) {
    'use strict';

    // Move admin notices outside flashsale container
    function moveAdminNotices() {
        var $notices = $('.flashsale-admin-container').find('.notice, .error, .updated, [id*="setting-error"]');
        
        if ($notices.length > 0) {
            $notices.each(function() {
                var $notice = $(this);
                // Move to right after .wrap but before .flashsale-admin-container
                $('.wrap').prepend($notice.clone());
                $notice.remove();
            });
        }
    }
    
    // Run on page load
    moveAdminNotices();
    
    // Run periodically to catch dynamically added notices
    setInterval(moveAdminNotices, 100);

    // Global FlashSale Admin object
    window.FlashSaleAdmin = {
        init: function() {
            this.initTabs();
            this.initTooltips();
            this.initConfirmDialogs();
            this.initAjaxForms();
            this.initDatePickers();
            this.initProductSearch();
            this.initNotifications();
        },

        // Initialize tab functionality
        initTabs: function() {
            $('.fs-tab').on('click', function(e) {
                e.preventDefault();
                
                var $tab = $(this);
                var target = $tab.data('target');
                
                // Remove active class from all tabs and content
                $('.fs-tab').removeClass('active');
                $('.fs-tab-content').removeClass('active');
                
                // Add active class to clicked tab and corresponding content
                $tab.addClass('active');
                $(target).addClass('active');
            });
        },

        // Initialize tooltips
        initTooltips: function() {
            $('[data-tooltip]').each(function() {
                var $element = $(this);
                var tooltip = $element.data('tooltip');
                
                $element.attr('title', tooltip);
            });
        },

        // Initialize confirmation dialogs
        initConfirmDialogs: function() {
            $('[data-confirm]').on('click', function(e) {
                var message = $(this).data('confirm');
                
                if (!confirm(message)) {
                    e.preventDefault();
                    return false;
                }
            });
        },

        // Initialize AJAX forms
        initAjaxForms: function() {
            $('.fs-ajax-form').on('submit', function(e) {
                e.preventDefault();
                
                var $form = $(this);
                var $submitBtn = $form.find('[type="submit"]');
                var originalText = $submitBtn.html();
                
                // Show loading state
                $submitBtn.prop('disabled', true).html('<span class="fs-loading"></span> ' + fs_admin.strings.saving);
                
                $.ajax({
                    url: fs_admin.ajax_url,
                    type: 'POST',
                    data: $form.serialize() + '&action=fs_admin_action&nonce=' + fs_admin.nonce,
                    success: function(response) {
                        if (response.success) {
                            FlashSaleAdmin.showNotification(fs_admin.strings.saved, 'success');
                            
                            // Trigger custom event
                            $form.trigger('fs:form:success', [response]);
                        } else {
                            FlashSaleAdmin.showNotification(response.data.message || fs_admin.strings.error, 'error');
                        }
                    },
                    error: function() {
                        FlashSaleAdmin.showNotification(fs_admin.strings.error, 'error');
                    },
                    complete: function() {
                        // Restore button state
                        $submitBtn.prop('disabled', false).html(originalText);
                    }
                });
            });
        },

        // Initialize date pickers
        initDatePickers: function() {
            if ($.fn.datepicker) {
                $('.fs-datepicker').datepicker({
                    dateFormat: 'yy-mm-dd',
                    changeMonth: true,
                    changeYear: true
                });
            }
        },

        // Initialize product search
        initProductSearch: function() {
            var searchTimeout;
            
            $('.fs-product-search').on('input', function() {
                var $input = $(this);
                var $results = $input.siblings('.fs-search-results');
                var query = $input.val().trim();
                
                clearTimeout(searchTimeout);
                
                if (query.length < 3) {
                    $results.hide().empty();
                    return;
                }
                
                searchTimeout = setTimeout(function() {
                    FlashSaleAdmin.searchProducts(query, $results);
                }, 300);
            });
            
            // Hide results when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.fs-product-search, .fs-search-results').length) {
                    $('.fs-search-results').hide();
                }
            });
        },

        // Search products via AJAX
        searchProducts: function(query, $results) {
            $results.html('<div class="fs-loading">Searching...</div>').show();
            
            $.ajax({
                url: fs_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'fs_search_products',
                    query: query,
                    nonce: fs_admin.nonce
                },
                success: function(response) {
                    if (response.success && response.data.products) {
                        var html = '';
                        
                        $.each(response.data.products, function(index, product) {
                            html += '<div class="fs-search-result" data-id="' + product.id + '">';
                            html += '<img src="' + product.image + '" alt="' + product.name + '">';
                            html += '<div class="fs-product-info">';
                            html += '<h4>' + product.name + '</h4>';
                            html += '<p>SKU: ' + product.sku + ' | Price: ' + product.price + '</p>';
                            html += '</div>';
                            html += '</div>';
                        });
                        
                        $results.html(html);
                        
                        // Handle product selection
                        $results.find('.fs-search-result').on('click', function() {
                            var $result = $(this);
                            var productId = $result.data('id');
                            var productName = $result.find('h4').text();
                            
                            FlashSaleAdmin.selectProduct(productId, productName);
                            $results.hide();
                        });
                    } else {
                        $results.html('<div class="fs-no-results">No products found</div>');
                    }
                },
                error: function() {
                    $results.html('<div class="fs-error">Search failed</div>');
                }
            });
        },

        // Select a product
        selectProduct: function(productId, productName) {
            var $selectedProducts = $('.fs-selected-products');
            
            // Check if product is already selected
            if ($selectedProducts.find('[data-product-id="' + productId + '"]').length) {
                this.showNotification('Product already selected', 'warning');
                return;
            }
            
            var html = '<div class="fs-selected-product" data-product-id="' + productId + '">';
            html += '<span class="fs-product-name">' + productName + '</span>';
            html += '<button type="button" class="fs-btn fs-btn-small fs-btn-danger fs-remove-product">Remove</button>';
            html += '<input type="hidden" name="selected_products[]" value="' + productId + '">';
            html += '</div>';
            
            $selectedProducts.append(html);
            
            // Handle product removal
            $selectedProducts.find('.fs-remove-product').last().on('click', function() {
                $(this).closest('.fs-selected-product').remove();
            });
        },

        // Show notification
        showNotification: function(message, type) {
            type = type || 'info';
            
            var $notification = $('<div class="fs-notification fs-notification-' + type + '">' + message + '</div>');
            
            $('body').append($notification);
            
            // Animate in
            $notification.addClass('fs-show');
            
            // Auto hide after 3 seconds
            setTimeout(function() {
                $notification.removeClass('fs-show');
                setTimeout(function() {
                    $notification.remove();
                }, 300);
            }, 3000);
        },

        // Initialize notifications
        initNotifications: function() {
            // Add notification styles if not already present
            if (!$('#fs-notification-styles').length) {
                var styles = `
                    <style id="fs-notification-styles">
                        .fs-notification {
                            position: fixed;
                            top: 32px;
                            right: 20px;
                            padding: 15px 20px;
                            border-radius: 8px;
                            color: white;
                            font-weight: 500;
                            z-index: 999999;
                            transform: translateX(100%);
                            transition: transform 0.3s ease;
                            max-width: 300px;
                            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                        }
                        
                        .fs-notification.fs-show {
                            transform: translateX(0);
                        }
                        
                        .fs-notification-success {
                            background: linear-gradient(135deg, #43e97b, #38f9d7);
                        }
                        
                        .fs-notification-error {
                            background: linear-gradient(135deg, #f093fb, #f5576c);
                        }
                        
                        .fs-notification-warning {
                            background: linear-gradient(135deg, #ffa726, #ff7043);
                        }
                        
                        .fs-notification-info {
                            background: linear-gradient(135deg, #667eea, #764ba2);
                        }
                    </style>
                `;
                $('head').append(styles);
            }
        },

        // Utility functions
        utils: {
            // Format currency
            formatCurrency: function(amount) {
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD'
                }).format(amount);
            },

            // Format date
            formatDate: function(date) {
                return new Date(date).toLocaleDateString();
            },

            // Debounce function
            debounce: function(func, wait) {
                var timeout;
                return function executedFunction() {
                    var context = this;
                    var args = arguments;
                    var later = function() {
                        timeout = null;
                        func.apply(context, args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            },

            // Generate random ID
            generateId: function() {
                return 'fs_' + Math.random().toString(36).substr(2, 9);
            }
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        FlashSaleAdmin.init();
    });

    // Campaign management functions
    window.FlashSaleCampaigns = {
        // Delete campaign
        deleteCampaign: function(campaignId, campaignName) {
            if (!confirm('Are you sure you want to delete "' + campaignName + '"? This action cannot be undone.')) {
                return;
            }

            $.ajax({
                url: fs_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'fs_admin_action',
                    fs_action: 'delete_campaign',
                    campaign_id: campaignId,
                    nonce: fs_admin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        FlashSaleAdmin.showNotification('Campaign deleted successfully', 'success');
                        location.reload();
                    } else {
                        FlashSaleAdmin.showNotification(response.data.message || 'Failed to delete campaign', 'error');
                    }
                },
                error: function() {
                    FlashSaleAdmin.showNotification('Failed to delete campaign', 'error');
                }
            });
        },

        // Toggle campaign status
        toggleStatus: function(campaignId, currentStatus) {
            var newStatus = currentStatus ? 0 : 1;
            var action = newStatus ? 'activate' : 'deactivate';

            $.ajax({
                url: fs_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'fs_admin_action',
                    fs_action: 'toggle_campaign_status',
                    campaign_id: campaignId,
                    status: newStatus,
                    nonce: fs_admin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        FlashSaleAdmin.showNotification('Campaign ' + action + 'd successfully', 'success');
                        location.reload();
                    } else {
                        FlashSaleAdmin.showNotification(response.data.message || 'Failed to ' + action + ' campaign', 'error');
                    }
                },
                error: function() {
                    FlashSaleAdmin.showNotification('Failed to ' + action + ' campaign', 'error');
                }
            });
        }
    };

})(jQuery);
