/**
 * Admin JavaScript for Quantity Promotion
 */

(function($) {
    'use strict';
    
    let ruleIndex = 0;
    
    $(document).ready(function() {
        initQuantityPromotion();
    });
    
    function initQuantityPromotion() {
        // Initialize existing rule indices
        $('.fsqp-rule-row').each(function() {
            const index = $(this).data('rule-id');
            if (typeof index === 'string' && index.startsWith('new-')) {
                const num = parseInt(index.replace('new-', ''));
                if (num >= ruleIndex) {
                    ruleIndex = num + 1;
                }
            }
        });
        
        // Event handlers
        bindEventHandlers();
        
        // Update preview
        updatePreview();
        
        // Update no rules message
        updateNoRulesMessage();
    }
    
    function bindEventHandlers() {
        // Add rule
        $(document).on('click', '.fsqp-add-rule', addRule);
        
        // Remove rule
        $(document).on('click', '.fsqp-remove-rule', removeRule);
        
        // Search products
        $(document).on('click', '.fsqp-search-products', openProductSearch);
        
        // Modal close
        $(document).on('click', '.fsqp-modal-close', closeModal);
        $(document).on('click', '.fsqp-modal', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
        
        // Product search
        $(document).on('click', '#fsqp-search-btn', searchProducts);
        $(document).on('keypress', '#fsqp-product-search', function(e) {
            if (e.which === 13) {
                searchProducts();
            }
        });
        
        // Add product from search
        $(document).on('click', '.fsqp-add-product-btn', addProductFromSearch);
        
        // Rule changes
        $(document).on('change', '.fsqp-rules-table input, .fsqp-rules-table select', function() {
            validateRule($(this).closest('.fsqp-rule-row'));
            updatePreview();
        });
        
        // Form submission
        $(document).on('submit', '#campaign-form', validateForm);
    }
    
    function addRule() {
        const template = $('#fsqp-rule-template').html();
        const html = template.replace(/{index}/g, ruleIndex);
        
        $('.fsqp-rules-tbody').append(html);
        $('.fsqp-no-rules').hide();
        
        ruleIndex++;
        updatePreview();
    }
    
    function removeRule() {
        const $row = $(this).closest('.fsqp-rule-row');
        
        if (confirm(fsqp_admin.strings.confirm_delete)) {
            const ruleId = $row.data('rule-id');
            
            // If it's an existing rule, send AJAX to delete
            if (ruleId && !ruleId.toString().startsWith('new-')) {
                deleteRuleAjax(ruleId, $row);
            } else {
                $row.remove();
                updateNoRulesMessage();
                updatePreview();
            }
        }
    }
    
    function deleteRuleAjax(ruleId, $row) {
        $.ajax({
            url: fsqp_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'fsqp_delete_rule',
                rule_id: ruleId,
                nonce: fsqp_admin.nonce
            },
            beforeSend: function() {
                $row.addClass('fsqp-loading');
            },
            success: function(response) {
                if (response.success) {
                    $row.remove();
                    updateNoRulesMessage();
                    updatePreview();
                    showMessage(response.data.message, 'success');
                } else {
                    showMessage(response.data.message, 'error');
                }
            },
            error: function() {
                showMessage(fsqp_admin.strings.error, 'error');
            },
            complete: function() {
                $row.removeClass('fsqp-loading');
            }
        });
    }
    
    function openProductSearch() {
        $('#fsqp-product-search-modal').show();
        $('#fsqp-product-search').focus();
    }
    
    function closeModal() {
        $('.fsqp-modal').hide();
        $('.fsqp-search-results-list').empty();
        $('#fsqp-product-search').val('');
    }
    
    function searchProducts() {
        const query = $('#fsqp-product-search').val();
        
        if (!query.trim()) {
            return;
        }
        
        $.ajax({
            url: fsqp_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'fsqp_search_products',
                query: query,
                nonce: fsqp_admin.nonce
            },
            beforeSend: function() {
                $('.fsqp-search-loading').show();
                $('.fsqp-search-results-list').empty();
            },
            success: function(response) {
                if (response.success) {
                    displaySearchResults(response.data.products);
                } else {
                    showMessage(response.data.message, 'error');
                }
            },
            error: function() {
                showMessage(fsqp_admin.strings.error, 'error');
            },
            complete: function() {
                $('.fsqp-search-loading').hide();
            }
        });
    }
    
    function displaySearchResults(products) {
        const $container = $('.fsqp-search-results-list');
        $container.empty();
        
        if (products.length === 0) {
            $container.html('<p>No products found.</p>');
            return;
        }
        
        products.forEach(function(product) {
            const html = `
                <div class="fsqp-product-item" data-product-id="${product.id}">
                    <div class="fsqp-product-info">
                        <h4>${product.name}</h4>
                        <p>SKU: ${product.sku || 'N/A'} | Type: ${product.type}</p>
                    </div>
                    <div class="fsqp-product-actions">
                        <span class="fsqp-product-price">${product.price_html}</span>
                        <button type="button" class="button button-primary fsqp-add-product-btn">
                            ${fsqp_admin.strings.add_rule}
                        </button>
                    </div>
                </div>
            `;
            $container.append(html);
        });
    }
    
    function addProductFromSearch() {
        const $item = $(this).closest('.fsqp-product-item');
        const productId = $item.data('product-id');
        const productName = $item.find('h4').text();
        
        // Check if product already exists in rules
        const existingProduct = $(`.fsqp-product-select option[value="${productId}"]`).length;
        if (existingProduct > 0) {
            alert('This product is already added to the rules.');
            return;
        }
        
        // Add new rule with this product
        addRule();
        
        // Set the product in the last added rule
        const $lastRow = $('.fsqp-rule-row').last();
        const $select = $lastRow.find('.fsqp-product-select');
        
        $select.append(`<option value="${productId}" selected>${productName}</option>`);
        
        closeModal();
    }
    
    function validateRule($row) {
        const minQty = parseInt($row.find('input[name*="[min_quantity]"]').val()) || 0;
        const maxQty = parseInt($row.find('input[name*="[max_quantity]"]').val()) || 0;
        const discountValue = parseFloat($row.find('input[name*="[discount_value]"]').val()) || 0;
        
        let hasError = false;
        
        // Clear previous errors
        $row.removeClass('has-error');
        $row.find('.fsqp-validation-error').remove();
        
        // Validate min quantity
        if (minQty < 1) {
            addValidationError($row.find('input[name*="[min_quantity]"]'), 'Minimum quantity must be at least 1');
            hasError = true;
        }
        
        // Validate max quantity
        if (maxQty > 0 && maxQty < minQty) {
            addValidationError($row.find('input[name*="[max_quantity]"]'), 'Maximum quantity must be greater than minimum');
            hasError = true;
        }
        
        // Validate discount value
        if (discountValue <= 0) {
            addValidationError($row.find('input[name*="[discount_value]"]'), 'Discount value must be greater than 0');
            hasError = true;
        }
        
        if (hasError) {
            $row.addClass('has-error');
        }
        
        return !hasError;
    }
    
    function addValidationError($field, message) {
        $field.after(`<span class="fsqp-validation-error">${message}</span>`);
    }
    
    function validateForm(e) {
        let hasErrors = false;
        
        $('.fsqp-rule-row').each(function() {
            if (!validateRule($(this))) {
                hasErrors = true;
            }
        });
        
        // Check for overlapping ranges
        const ranges = [];
        $('.fsqp-rule-row').each(function() {
            const productId = $(this).find('.fsqp-product-select').val();
            const minQty = parseInt($(this).find('input[name*="[min_quantity]"]').val()) || 0;
            const maxQty = parseInt($(this).find('input[name*="[max_quantity]"]').val()) || 0;
            
            if (productId) {
                ranges.push({
                    productId: productId,
                    minQty: minQty,
                    maxQty: maxQty,
                    row: $(this)
                });
            }
        });
        
        // Check for overlaps within same product
        for (let i = 0; i < ranges.length; i++) {
            for (let j = i + 1; j < ranges.length; j++) {
                if (ranges[i].productId === ranges[j].productId) {
                    if (rangesOverlap(ranges[i], ranges[j])) {
                        addValidationError(ranges[i].row.find('input[name*="[min_quantity]"]'), 'Quantity ranges overlap');
                        addValidationError(ranges[j].row.find('input[name*="[min_quantity]"]'), 'Quantity ranges overlap');
                        ranges[i].row.addClass('has-error');
                        ranges[j].row.addClass('has-error');
                        hasErrors = true;
                    }
                }
            }
        }
        
        if (hasErrors) {
            e.preventDefault();
            showMessage('Please fix the validation errors before saving.', 'error');
            return false;
        }
        
        return true;
    }
    
    function rangesOverlap(range1, range2) {
        const max1 = range1.maxQty === 0 ? Number.MAX_SAFE_INTEGER : range1.maxQty;
        const max2 = range2.maxQty === 0 ? Number.MAX_SAFE_INTEGER : range2.maxQty;
        
        return range1.minQty <= max2 && range2.minQty <= max1;
    }
    
    function updatePreview() {
        const $tbody = $('.fsqp-preview-tbody');
        $tbody.empty();
        
        const rules = [];
        $('.fsqp-rule-row').each(function() {
            const $row = $(this);
            const productName = $row.find('.fsqp-product-select option:selected').text();
            const minQty = parseInt($row.find('input[name*="[min_quantity]"]').val()) || 0;
            const maxQty = parseInt($row.find('input[name*="[max_quantity]"]').val()) || 0;
            const discountType = parseInt($row.find('select[name*="[discount_type]"]').val()) || 1;
            const discountValue = parseFloat($row.find('input[name*="[discount_value]"]').val()) || 0;
            
            if (productName && productName !== 'Select a product...' && minQty > 0 && discountValue > 0) {
                rules.push({
                    productName: productName,
                    minQty: minQty,
                    maxQty: maxQty,
                    discountType: discountType,
                    discountValue: discountValue
                });
            }
        });
        
        if (rules.length === 0) {
            $tbody.html('<tr><td colspan="3" class="fsqp-preview-empty">Add quantity rules to see preview</td></tr>');
            return;
        }
        
        rules.forEach(function(rule) {
            const quantityRange = rule.maxQty > 0 ? `${rule.minQty} - ${rule.maxQty}` : `${rule.minQty}+`;
            const discountText = rule.discountType === 1 ? `${rule.discountValue}%` : `$${rule.discountValue}`;
            
            const html = `
                <tr>
                    <td>${quantityRange}</td>
                    <td>${discountText}</td>
                    <td>
                        <strong>${rule.productName}</strong><br>
                        <small>Calculated price will be shown here</small>
                    </td>
                </tr>
            `;
            $tbody.append(html);
        });
    }
    
    function updateNoRulesMessage() {
        const hasRules = $('.fsqp-rule-row').length > 0;
        $('.fsqp-no-rules').toggle(!hasRules);
    }
    
    function showMessage(message, type) {
        const $message = $(`<div class="fsqp-message ${type}">${message}</div>`);
        $('.fsqp-campaign-form').prepend($message);
        
        setTimeout(function() {
            $message.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }
    
})(jQuery);
