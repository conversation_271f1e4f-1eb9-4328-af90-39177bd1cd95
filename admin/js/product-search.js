/**
 * FlashSale Core - Product Search Library
 * Shared functionality for product search across core and addons
 */

(function($) {
    'use strict';

    // Global namespace for product search
    window.FSProductSearch = {
        
        /**
         * Initialize product search for a container
         * @param {Object} options Configuration options
         */
        init: function(options) {
            const defaults = {
                searchInputId: '#fs-product-search',
                searchButtonId: '#fs-search-btn',
                resultsContainerId: '#fs-search-results',
                categoryFilterId: '#fs-category-filter',
                searchAction: 'fs_search_products',
                nonce: '',
                debounceDelay: 500,
                minQueryLength: 2,
                callbacks: {
                    onProductAdd: null,
                    onSearchStart: null,
                    onSearchComplete: null,
                    onResultsDisplay: null,
                    onShowResults: null,
                    onHideResults: null
                },
                templates: {
                    resultItem: this.defaultResultTemplate,
                    noResults: '<p>No products found.</p>',
                    loading: '<p>Searching...</p>'
                }
            };

            const settings = $.extend(true, {}, defaults, options);
            this.settings = settings;
            this.searchTimeout = null;
            
            this.bindEvents();
        },

        /**
         * Bind event handlers
         */
        bindEvents: function() {
            const self = this;
            const settings = this.settings;

            // Search input with debounce
            $(document).on('input', settings.searchInputId, function() {
                clearTimeout(self.searchTimeout);
                const query = $(this).val().trim();

                if (query.length >= settings.minQueryLength) {
                    self.searchTimeout = setTimeout(() => {
                        self.searchProducts(query);
                    }, settings.debounceDelay);
                } else {
                    self.hideResults();
                    self.removeLoadingState();
                }
            });

            // Search button click
            $(document).on('click', settings.searchButtonId, function(e) {
                e.preventDefault();
                const query = $(settings.searchInputId).val().trim();
                if (query) {
                    self.searchProducts(query);
                }
            });

            // Enter key in search input
            $(document).on('keypress', settings.searchInputId, function(e) {
                if (e.which === 13) {
                    e.preventDefault();
                    const query = $(this).val().trim();
                    if (query) {
                        self.searchProducts(query);
                    }
                }
            });

            // Category filter change
            if (settings.categoryFilterId) {
                $(document).on('change', settings.categoryFilterId, function() {
                    const query = $(settings.searchInputId).val().trim();
                    if (query) {
                        self.searchProducts(query);
                    }
                });
            }

            // Add product button click
            $(document).on('click', '.fs-add-product-btn', function(e) {
                e.preventDefault();
                self.handleProductAdd($(this));
            });
        },

        /**
         * Search products via AJAX
         */
        searchProducts: function(query) {
            const settings = this.settings;
            const categoryId = settings.categoryFilterId ? $(settings.categoryFilterId).val() : '';

            // Trigger search start callback
            if (settings.callbacks.onSearchStart) {
                settings.callbacks.onSearchStart(query);
            }

            this.showLoadingState();
            this.hideResults();

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: settings.searchAction,
                    query: query,
                    category_id: categoryId,
                    nonce: settings.nonce
                },
                success: (response) => {
                    this.removeLoadingState();
                    if (response.success) {
                        this.displayResults(response.data);
                    } else {
                        this.displayResults([]);
                    }
                },
                error: () => {
                    this.removeLoadingState();
                    this.displayResults([]);
                },
                complete: () => {
                    // Trigger search complete callback
                    if (settings.callbacks.onSearchComplete) {
                        settings.callbacks.onSearchComplete(query);
                    }
                }
            });
        },

        /**
         * Display search results
         */
        displayResults: function(products) {
            const settings = this.settings;
            const $results = $(settings.resultsContainerId);

            $results.empty();

            if (products.length === 0) {
                $results.html(settings.templates.noResults);
            } else {
                products.forEach(product => {
                    const $item = $(settings.templates.resultItem(product));
                    $results.append($item);
                });
            }

            this.showResults();

            // Trigger results display callback
            if (settings.callbacks.onResultsDisplay) {
                settings.callbacks.onResultsDisplay(products);
            }
        },

        /**
         * Default result item template
         */
        defaultResultTemplate: function(product) {
            const typeInfo = product.type === 'variation' ? ' <span class="fs-variation-badge">Variation</span>' : '';
            const parentInfo = product.parent_id ? ` <span class="fs-parent-id">(Parent: ${product.parent_id})</span>` : '';
            
            return `
                <div class="fs-search-result-item" data-product-id="${product.id}" data-product-type="${product.type || 'simple'}">
                    <div class="fs-product-info">
                        <strong>${product.name}${typeInfo}</strong>
                        <div class="fs-product-meta">
                            <span>SKU: ${product.sku || '-'}</span>
                            <span>ID: ${product.id}${parentInfo}</span>
                            <span>Price: ${product.price}</span>
                        </div>
                        <div class="fs-add-progress" style="display: none;">
                            <div class="fs-progress-bar">
                                <div class="fs-progress-fill"></div>
                            </div>
                            <span class="fs-progress-text">Adding...</span>
                        </div>
                    </div>
                    <button type="button" class="fs-btn fs-btn-primary fs-btn-small fs-add-product-btn">
                        Add
                    </button>
                </div>
            `;
        },

        /**
         * Handle product add button click
         */
        handleProductAdd: function($button) {
            const settings = this.settings;
            const $item = $button.closest('.fs-search-result-item');
            const productId = $item.data('product-id');

            if (settings.callbacks.onProductAdd) {
                settings.callbacks.onProductAdd(productId, $item, $button);
            }
        },

        /**
         * Show loading state
         */
        showLoadingState: function() {
            const settings = this.settings;
            $(settings.searchInputId).closest('.fs-search-input-group').addClass('loading');
        },

        /**
         * Remove loading state
         */
        removeLoadingState: function() {
            const settings = this.settings;
            $(settings.searchInputId).closest('.fs-search-input-group').removeClass('loading');
        },

        /**
         * Show results container
         */
        showResults: function() {
            const settings = this.settings;

            if (settings.callbacks.onShowResults) {
                settings.callbacks.onShowResults();
            } else {
                $(settings.resultsContainerId).show();
            }
        },

        /**
         * Hide results container
         */
        hideResults: function() {
            const settings = this.settings;

            if (settings.callbacks.onHideResults) {
                settings.callbacks.onHideResults();
            } else {
                $(settings.resultsContainerId).hide();
            }
        },

        /**
         * Clear search
         */
        clearSearch: function() {
            const settings = this.settings;
            $(settings.searchInputId).val('');
            this.hideResults();
            this.removeLoadingState();
        },

        /**
         * Get product data from result item
         */
        getProductData: function($item) {
            return {
                id: $item.data('product-id'),
                name: $item.find('strong').text(),
                meta: $item.find('.fs-product-meta').html(),
                price: this.extractPrice($item.find('.fs-product-meta').text())
            };
        },

        /**
         * Extract price from meta text
         */
        extractPrice: function(metaText) {
            const priceMatch = metaText.match(/Price: ([0-9.,]+)/);
            return priceMatch ? priceMatch[1] : '-';
        },

        /**
         * Check if product already exists in table
         */
        isProductExists: function(productId, tableSelector) {
            return $(`${tableSelector} tr[data-product-id="${productId}"]`).length > 0;
        },

        /**
         * Show notification
         */
        showNotification: function(message, type) {
            // Use FlashSale admin notification if available
            if (window.FlashSaleAdmin && window.FlashSaleAdmin.showNotification) {
                window.FlashSaleAdmin.showNotification(message, type);
            } else {
                // Fallback to alert
                alert(message);
            }
        }
    };

})(jQuery);
