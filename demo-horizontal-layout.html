<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quantity Promotion - Horizontal Layout Demo</title>
    <style>
        /* Basic WordPress Admin Styles */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
            background: #f1f1f1;
            margin: 0;
            padding: 20px;
        }
        
        .fs-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .fs-table th,
        .fs-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .fs-table th {
            background: #f9f9f9;
            font-weight: 600;
            color: #555;
        }
        
        .fs-checkbox {
            margin: 0;
        }
        
        .fs-product-thumb {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 4px;
        }
        
        .fs-product-thumb-placeholder {
            width: 50px;
            height: 50px;
            background: #f0f0f0;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
        }
        
        .fs-product-name {
            font-size: 14px;
            margin: 0 0 4px 0;
        }
        
        .fs-product-meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .fs-product-meta span {
            margin-right: 12px;
        }
        
        .fs-btn {
            padding: 6px 12px;
            border: 1px solid #007cba;
            background: #007cba;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            text-decoration: none;
            display: inline-block;
        }
        
        .fs-btn-secondary {
            background: #f0f0f0;
            color: #333;
            border-color: #ddd;
        }
        
        .fs-btn-danger {
            background: #dc3232;
            border-color: #dc3232;
        }
        
        .fs-btn-small {
            padding: 4px 8px;
            font-size: 11px;
        }
        
        .dashicons {
            font-family: dashicons;
            font-size: 16px;
            line-height: 1;
        }
        
        .dashicons-products:before { content: "🛍️"; }
        .dashicons-plus-alt:before { content: "+"; }
        .dashicons-trash:before { content: "🗑️"; }
    </style>
    <link rel="stylesheet" href="admin/css/quantity-promotion-horizontal.css">
</head>
<body>
    <h1>Quantity Promotion - New Horizontal Layout</h1>
    
    <div class="fs-products-container">
        <div class="fs-products-table-wrapper">
            <table class="fs-table fs-products-table">
                <thead>
                    <tr>
                        <th width="40px" class="fs-checkbox-column">
                            <input type="checkbox" class="fs-checkbox">
                        </th>
                        <th class="fs-product-info-column">Product Information & Quantity Ranges</th>
                        <th width="100px" class="fs-price-column">Regular Price</th>
                        <th width="80px" class="fs-actions-column">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="fs-product-row">
                        <td class="fs-checkbox-column">
                            <input type="checkbox" class="fs-checkbox">
                        </td>
                        <td class="fs-product-info-cell">
                            <div class="fs-product-info-enhanced">
                                <div class="fs-product-image">
                                    <div class="fs-product-thumb-placeholder">
                                        <span class="dashicons dashicons-products"></span>
                                    </div>
                                </div>
                                <div class="fs-product-details">
                                    <strong class="fs-product-name">Beanie with Logo</strong>
                                    <div class="fs-product-meta">
                                        <span>SKU: woo-beanie-logo</span>
                                        <span>ID: 38</span>
                                        <span>In Stock</span>
                                    </div>
                                    
                                    <!-- Quantity Ranges moved here -->
                                    <div class="fsqp-quantity-ranges">
                                        <div class="fsqp-range-item-horizontal">
                                            <div class="fsqp-range-controls-horizontal">
                                                <div class="fsqp-range-field-inline">
                                                    <label>Qty</label>
                                                    <input type="number" value="1" class="fs-input-tiny">
                                                    <span class="fsqp-range-separator">-</span>
                                                    <input type="number" value="5" class="fs-input-tiny">
                                                </div>
                                                <div class="fsqp-range-field-inline">
                                                    <label>Discount</label>
                                                    <div class="fs-discount-controls-inline">
                                                        <div class="fs-discount-type-toggle-small">
                                                            <button type="button" class="fs-discount-type-btn-small active">%</button>
                                                            <button type="button" class="fs-discount-type-btn-small">$</button>
                                                        </div>
                                                        <input type="number" value="5" class="fs-input-tiny">
                                                    </div>
                                                </div>
                                                <div class="fsqp-range-field-inline">
                                                    <label>Max Disc.</label>
                                                    <input type="number" value="0" class="fs-input-tiny">
                                                </div>
                                                <div class="fsqp-range-actions-inline">
                                                    <button type="button" class="fs-btn fs-btn-danger fs-btn-tiny">
                                                        <span class="dashicons dashicons-trash"></span>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="fsqp-range-item-horizontal">
                                            <div class="fsqp-range-controls-horizontal">
                                                <div class="fsqp-range-field-inline">
                                                    <label>Qty</label>
                                                    <input type="number" value="6" class="fs-input-tiny">
                                                    <span class="fsqp-range-separator">-</span>
                                                    <input type="number" placeholder="∞" class="fs-input-tiny">
                                                </div>
                                                <div class="fsqp-range-field-inline">
                                                    <label>Discount</label>
                                                    <div class="fs-discount-controls-inline">
                                                        <div class="fs-discount-type-toggle-small">
                                                            <button type="button" class="fs-discount-type-btn-small active">%</button>
                                                            <button type="button" class="fs-discount-type-btn-small">$</button>
                                                        </div>
                                                        <input type="number" value="10" class="fs-input-tiny">
                                                    </div>
                                                </div>
                                                <div class="fsqp-range-field-inline">
                                                    <label>Max Disc.</label>
                                                    <input type="number" value="0" class="fs-input-tiny">
                                                </div>
                                                <div class="fsqp-range-actions-inline">
                                                    <button type="button" class="fs-btn fs-btn-danger fs-btn-tiny">
                                                        <span class="dashicons dashicons-trash"></span>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="fsqp-add-range-inline">
                                            <button type="button" class="fs-btn fs-btn-secondary fs-btn-small">
                                                <span class="dashicons dashicons-plus-alt"></span>
                                                Add Range
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="fs-price-cell">
                            <span class="fs-regular-price">20.00</span>
                        </td>
                        <td class="fs-actions-cell">
                            <button type="button" class="fs-btn fs-btn-danger fs-btn-small">
                                <span class="dashicons dashicons-trash"></span>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <h2>Comparison</h2>
    <p><strong>Old Layout:</strong> Quantity ranges were in a separate wide column, making the table very wide and hard to read.</p>
    <p><strong>New Layout:</strong> Quantity ranges are now compact and placed under product information, making better use of space.</p>
    
    <h3>Benefits:</h3>
    <ul>
        <li>✅ More compact and organized layout</li>
        <li>✅ Better use of horizontal space</li>
        <li>✅ Easier to scan product information</li>
        <li>✅ Quantity ranges are logically grouped with product info</li>
        <li>✅ Responsive design for mobile devices</li>
    </ul>
</body>
</html>
