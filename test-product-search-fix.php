<?php
/**
 * Test script for product search functionality fix
 * Add this to wp-config.php or run in WordPress admin context
 */

function test_product_search_fix() {
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }
    
    echo "<h1>Product Search Functionality Test</h1>";
    echo "<p><strong>Goal:</strong> Verify that shared product search library works correctly with quantity promotion plugin</p>";
    
    // Step 1: Check HTML structure
    echo "<h2>Step 1: HTML Structure Check</h2>";
    
    $expected_elements = [
        '#fsqp-product-search' => 'Search input field',
        '#fsqp-search-btn' => 'Search button',
        '#fsqp-search-results' => 'Search results container',
        '.fs-search-results-list' => 'Search results list',
        '#fsqp-category-filter' => 'Category filter'
    ];
    
    echo "<p><strong>Expected HTML elements for quantity promotion:</strong></p>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>Selector</th><th>Description</th><th>Status</th>";
    echo "</tr>";
    
    foreach ($expected_elements as $selector => $description) {
        echo "<tr>";
        echo "<td><code>$selector</code></td>";
        echo "<td>$description</td>";
        echo "<td style='color: green;'>✅ Expected</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Step 2: Check JavaScript library loading
    echo "<h2>Step 2: JavaScript Library Loading</h2>";
    
    $js_files = [
        'fs-admin-script' => 'Core admin script',
        'fs-product-search' => 'Shared product search library',
        'fs-bulk-actions' => 'Shared bulk actions library'
    ];
    
    echo "<p><strong>Required JavaScript files:</strong></p>";
    echo "<ul>";
    foreach ($js_files as $handle => $description) {
        echo "<li><strong>$handle</strong>: $description</li>";
    }
    echo "</ul>";
    
    // Step 3: Configuration check
    echo "<h2>Step 3: Configuration Check</h2>";
    
    echo "<p><strong>Quantity Promotion Search Configuration:</strong></p>";
    echo "<div style='background: #f9f9f9; padding: 15px; border-left: 4px solid #0073aa;'>";
    echo "<pre>";
    echo "FSProductSearch.init({
    searchInputId: '#fsqp-product-search',
    searchButtonId: '#fsqp-search-btn',
    resultsContainerId: '.fs-search-results-list',
    categoryFilterId: '#fsqp-category-filter',
    searchAction: 'fsqp_search_products',
    nonce: '" . wp_create_nonce('fsqp_search_products') . "',
    callbacks: {
        onProductAdd: handleQuantityPromotionProductAdd,
        onShowResults: function() { $('#fsqp-search-results').show(); },
        onHideResults: function() { $('#fsqp-search-results').hide(); }
    }
});";
    echo "</pre>";
    echo "</div>";
    
    // Step 4: Problem and solution
    echo "<h2>Step 4: Problem & Solution</h2>";
    
    echo "<div style='background: #fee; border: 1px solid #f00; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3 style='color: #d00; margin-top: 0;'>❌ Problem Found</h3>";
    echo "<p><strong>Issue:</strong> Shared library couldn't find <code>.fsqp-search-results-list</code> class</p>";
    echo "<p><strong>Root Cause:</strong> Library was looking for results container but needed custom show/hide logic</p>";
    echo "</div>";
    
    echo "<div style='background: #efe; border: 1px solid #0a0; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3 style='color: #0a0; margin-top: 0;'>✅ Solution Applied</h3>";
    echo "<p><strong>Fix 1:</strong> Changed <code>resultsContainerId</code> to <code>'.fs-search-results-list'</code></p>";
    echo "<p><strong>Fix 2:</strong> Added custom <code>onShowResults</code> and <code>onHideResults</code> callbacks</p>";
    echo "<p><strong>Fix 3:</strong> Updated shared library to support custom show/hide logic</p>";
    echo "</div>";
    
    // Step 5: Flow explanation
    echo "<h2>Step 5: Search Flow</h2>";
    
    echo "<p><strong>How product search works now:</strong></p>";
    echo "<ol>";
    echo "<li><strong>User types in search input</strong> → <code>#fsqp-product-search</code></li>";
    echo "<li><strong>Shared library debounces input</strong> → 500ms delay</li>";
    echo "<li><strong>AJAX request sent</strong> → <code>fsqp_search_products</code> action</li>";
    echo "<li><strong>Results received</strong> → Populate <code>.fs-search-results-list</code></li>";
    echo "<li><strong>Container shown</strong> → <code>$('#fsqp-search-results').show()</code></li>";
    echo "<li><strong>User clicks Add</strong> → <code>handleQuantityPromotionProductAdd()</code></li>";
    echo "</ol>";
    
    // Step 6: Testing instructions
    echo "<h2>Step 6: Testing Instructions</h2>";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #856404; margin-top: 0;'>🧪 Manual Testing Steps</h3>";
    echo "<ol>";
    echo "<li><strong>Go to Quantity Promotion campaign form</strong></li>";
    echo "<li><strong>Open browser developer tools</strong> (F12)</li>";
    echo "<li><strong>Check Console tab</strong> for JavaScript errors</li>";
    echo "<li><strong>Type in product search field</strong></li>";
    echo "<li><strong>Verify search results appear</strong> in <code>#fsqp-search-results</code></li>";
    echo "<li><strong>Click Add button</strong> on a product</li>";
    echo "<li><strong>Verify product is added</strong> to campaign table</li>";
    echo "</ol>";
    echo "</div>";
    
    // Step 7: Debugging tips
    echo "<h2>Step 7: Debugging Tips</h2>";
    
    echo "<p><strong>If search still doesn't work, check:</strong></p>";
    echo "<ul>";
    echo "<li><strong>JavaScript Console:</strong> Look for errors related to <code>FSProductSearch</code></li>";
    echo "<li><strong>Network Tab:</strong> Check if AJAX requests to <code>fsqp_search_products</code> are sent</li>";
    echo "<li><strong>HTML Structure:</strong> Verify all required elements exist</li>";
    echo "<li><strong>CSS Display:</strong> Check if <code>#fsqp-search-results</code> has <code>display: none</code></li>";
    echo "</ul>";
    
    echo "<p><strong>Debug JavaScript in browser console:</strong></p>";
    echo "<div style='background: #f9f9f9; padding: 10px; border: 1px solid #ddd;'>";
    echo "<pre>";
    echo "// Check if library is loaded
console.log(window.FSProductSearch);

// Check if elements exist
console.log($('#fsqp-product-search').length);
console.log($('.fs-search-results-list').length);

// Test search manually
FSProductSearch.searchProducts('test');";
    echo "</pre>";
    echo "</div>";
    
    // Step 8: Summary
    echo "<h2>Step 8: Summary</h2>";
    
    echo "<div style='background: #e8f5e8; border: 1px solid #27ae60; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #27ae60; margin-top: 0;'>🎯 Changes Made</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>Fixed resultsContainerId</strong>: Now targets <code>.fs-search-results-list</code></li>";
    echo "<li>✅ <strong>Added custom callbacks</strong>: <code>onShowResults</code> and <code>onHideResults</code></li>";
    echo "<li>✅ <strong>Updated shared library</strong>: Supports custom show/hide logic</li>";
    echo "<li>✅ <strong>Maintained compatibility</strong>: Works with existing HTML structure</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #e3f2fd; border: 1px solid #1976d2; padding: 15px; border-radius: 5px; margin-top: 15px;'>";
    echo "<h3 style='color: #1976d2; margin-top: 0;'>🚀 Benefits</h3>";
    echo "<ul>";
    echo "<li><strong>Reusable Code:</strong> Same search library for all plugins</li>";
    echo "<li><strong>Flexible Configuration:</strong> Custom callbacks for different UI structures</li>";
    echo "<li><strong>Consistent Behavior:</strong> Same search experience across plugins</li>";
    echo "<li><strong>Easy Maintenance:</strong> Update once, affects all plugins</li>";
    echo "</ul>";
    echo "</div>";
    
    // Step 9: Next steps
    echo "<h2>Step 9: Next Steps</h2>";
    
    echo "<p><strong>After confirming search works:</strong></p>";
    echo "<ol>";
    echo "<li>Test with different product types</li>";
    echo "<li>Test category filtering</li>";
    echo "<li>Test product conflict checking</li>";
    echo "<li>Test bulk delete functionality</li>";
    echo "<li>Verify no JavaScript errors in console</li>";
    echo "</ol>";
}

// Add admin page
add_action('admin_menu', function() {
    if (current_user_can('manage_options')) {
        add_submenu_page(
            null,
            'Test Product Search Fix',
            'Test Product Search Fix',
            'manage_options',
            'test-product-search-fix',
            'test_product_search_fix'
        );
    }
});

// Add quick access
add_action('admin_notices', function() {
    if (current_user_can('manage_options') && 
        (strpos($_SERVER['REQUEST_URI'], 'quantity-promotion') !== false || 
         strpos($_SERVER['REQUEST_URI'], 'campaign') !== false)) {
        echo '<div class="notice notice-warning"><p>';
        echo '<strong>Product Search Fix:</strong> ';
        echo '<a href="?page=test-product-search-fix" target="_blank" class="button button-small">Test Search Fix</a>';
        echo '</p></div>';
    }
});
