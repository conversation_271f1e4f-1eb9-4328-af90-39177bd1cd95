# Priority và Product Conflicts Implementation

## Tổng quan

Đã thực hiện đầy đủ 3 yêu cầu:

1. **Priority Logic**: Chương trình flashsale có priority cao hơn sẽ được ưu tiên (5 > 4)
2. **Product Conflicts**: Validation để ngăn sản phẩm trùng lặp giữa các chương trình active
3. **Single Progress Bar**: Thanh tiến trình chỉ hiển thị 1 cái theo priority cao nhất

## Chi tiết thực hiện

### 1. Priority Logic

#### Database Schema
- Trường `priority` đã tồn tại trong bảng `fs_campaigns` (int(11) DEFAULT 0)
- Index `status_priority` để tối ưu query

#### Backend Logic
**File: `public/class-fs-public.php`**
- Cập nhật `get_best_promotion()` để ưu tiên campaign có priority cao hơn
- Logic: Priority cao hơn thắng, nếu cùng priority thì chọn giá tốt hơn

```php
// Priority logic: Higher priority number wins (5 > 4)
if ($campaign_priority > $best_priority) {
    $best_promotion = $promotion;
    $best_priority = $campaign_priority;
    $best_price = $discounted_price;
} 
// If same priority, choose better price
elseif ($campaign_priority == $best_priority && $discounted_price < $best_price) {
    $best_promotion = $promotion;
    $best_price = $discounted_price;
}
```

**File: `includes/class-fs-flash-sale-manager.php`**
- Cập nhật `get_campaigns()` để sắp xếp theo `ORDER BY priority DESC, id DESC`

**File: `public/class-ait-dev-public.php`**
- Cập nhật `isProductFS()` để chọn campaign có priority cao nhất
- Cập nhật `isProductFSVariable()` để chọn campaign có priority cao nhất
- Hàm `override_price()` đã sử dụng priority logic từ trước

#### Frontend Form
**Files: `admin/views/campaign-new.php`, `admin/views/campaign-edit.php`**
- Thêm dropdown priority từ 0-10 (mặc định 5)
- Help text: "Higher numbers = higher priority"

### 2. Product Conflicts Validation

#### Backend Validation
**File: `includes/class-fs-flash-sale-manager.php`**

**Thêm các phương thức mới:**
- `ajax_save_campaign()` - AJAX handler cho save campaign
- `ajax_update_campaign()` - AJAX handler cho update campaign  
- `ajax_check_product_conflicts()` - AJAX handler kiểm tra conflicts
- `ajax_get_product_info()` - AJAX handler lấy thông tin sản phẩm
- `check_product_conflicts()` - Logic kiểm tra conflicts
- `validate_campaign_data()` - Validation tổng quát
- `sanitize_campaign_data()` - Sanitize dữ liệu campaign
- `sanitize_products_data()` - Sanitize dữ liệu products

**Logic kiểm tra conflicts:**
```sql
SELECT p.product_id, c.name as campaign_name, c.priority
FROM {$products_table} p
INNER JOIN {$campaigns_table} c ON p.promotion_id = c.id
WHERE p.product_id IN ({$product_ids})
AND c.status = 1
AND (c.end_date IS NULL OR c.end_date > NOW())
AND c.id != {$exclude_campaign_id}
```

#### Frontend Validation
**File: `admin/views/campaign-types/flash-sale.php`**

**Cập nhật JavaScript:**
- Khi click "Add Product", gọi AJAX kiểm tra conflicts
- Hiển thị warning nếu có conflicts với thông tin campaign và priority
- Cho phép user quyết định có tiếp tục hay không

```javascript
// Check for conflicts with other campaigns
$.ajax({
    url: ajaxurl,
    type: 'POST',
    data: {
        action: 'fs_check_product_conflicts',
        product_id: productId,
        exclude_campaign_id: $('#campaign_id').val() || 0,
        nonce: nonce
    },
    success: function(response) {
        if (response.data.has_conflicts) {
            // Show warning with campaign names and priorities
            // Let user decide to continue or not
        }
    }
});
```

## Cấu trúc Files đã thay đổi

### 3. Single Progress Bar

#### Hệ thống mới (FS_Public)
**File: `public/class-fs-public.php`**
- Cập nhật `display_promotion_info()` để chỉ hiển thị campaign có priority cao nhất
- Sử dụng `get_best_promotion()` thay vì hiển thị tất cả campaigns

```php
// Get the best promotion (highest priority) for this product
$best_promotion = $this->get_best_promotion($promotions, $product);

if ($best_promotion) {
    // Only render the highest priority campaign to avoid multiple progress bars
    $this->render_promotion_info($best_promotion, $product);
}
```

#### Hệ thống cũ (Legacy)
**File: `public/partials/single-product/flash-sale-bar.php`**
- Cập nhật template để chỉ hiển thị progress bar đầu tiên
- Sử dụng `reset($product)` để lấy product đầu tiên thay vì loop

```php
// Only show progress bar for the first product to avoid duplicates
// Since isProductFSVariable already returns highest priority campaign,
// we just need the first product's progress bar
$p = reset($product); // Get first product
```

## Cấu trúc Files đã thay đổi

### Files chính đã cập nhật:
1. `includes/class-fs-flash-sale-manager.php` - Thêm AJAX handlers và validation
2. `public/class-fs-public.php` - Cập nhật logic priority và single progress bar cho hệ thống mới
3. `public/class-ait-dev-public.php` - Cập nhật logic priority cho hệ thống cũ
4. `public/partials/single-product/flash-sale-bar.php` - Cập nhật template để chỉ hiển thị 1 progress bar
5. `admin/views/campaign-types/flash-sale.php` - Thêm validation frontend
6. `admin/views/campaign-new.php` - Đã có trường priority
7. `admin/views/campaign-edit.php` - Đã có trường priority

### Files mới:
1. `test-priority-and-conflicts.php` - Test script
2. `PRIORITY_AND_CONFLICTS_IMPLEMENTATION.md` - Tài liệu này

## Testing

### Chạy test tự động:
```
/wp-content/plugins/flashsale-core-v2/test-priority-and-conflicts.php
```

### Test thủ công:
1. **Test Priority:**
   - Tạo 2 campaigns với priority khác nhau (4 và 5)
   - Thêm cùng 1 sản phẩm vào cả 2 campaigns
   - Kiểm tra frontend xem campaign priority 5 có được áp dụng không

2. **Test Product Conflicts:**
   - Tạo campaign A với sản phẩm X
   - Tạo campaign B, thử thêm sản phẩm X
   - Kiểm tra có warning hiện ra không
   - Kiểm tra backend validation có chặn không

## Lưu ý quan trọng

### Priority Logic:
- **Higher number = Higher priority** (5 > 4 > 3...)
- Nếu cùng priority, chọn campaign có giá tốt hơn
- Chỉ áp dụng cho campaigns đang active và trong thời gian hiệu lực

### Product Conflicts:
- Chỉ kiểm tra với campaigns đang active (`status = 1`)
- Chỉ kiểm tra với campaigns chưa hết hạn (`end_date > NOW()`)
- Cho phép edit campaign hiện tại mà không bị conflict với chính nó
- Frontend warning nhưng vẫn cho phép user quyết định

### Performance:
- Sử dụng database indexes để tối ưu query
- Cache campaigns trong memory khi có thể
- Validation chỉ chạy khi cần thiết

## Tương thích

- Tương thích với hệ thống cũ (legacy classes)
- Không ảnh hưởng đến campaigns đã tồn tại
- Database migration tự động khi cần

## Troubleshooting

### Nếu priority không hoạt động:
1. Kiểm tra database có trường `priority` không
2. Kiểm tra campaigns có giá trị priority đúng không
3. Clear cache nếu có

### Nếu validation không hoạt động:
1. Kiểm tra AJAX endpoints đã được register chưa
2. Kiểm tra nonce security
3. Kiểm tra JavaScript console có lỗi không
