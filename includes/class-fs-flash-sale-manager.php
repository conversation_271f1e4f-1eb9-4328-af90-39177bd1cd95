<?php
/**
 * Flash Sale Manager (inspired by AITFS_Campaign)
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class FS_Flash_Sale_Manager {
    
    private static $table_campaigns = 'fs_campaigns';
    private static $table_products = 'fs_campaign_products';
    private static $table_gifts = 'fs_product_gifts';
    private static $table_gifts_offline = 'fs_product_gifts_offline';
    private static $pageLimit = 20;
    
    /**
     * Initialize flash sale manager
     */
    public function init() {
        add_action('wp_ajax_fs_search_products', [$this, 'ajax_search_products']);
        add_action('wp_ajax_fs_save_campaign', [$this, 'ajax_save_campaign']);
        add_action('wp_ajax_fs_update_campaign', [$this, 'ajax_update_campaign']);
        add_action('wp_ajax_fs_delete_campaign', [$this, 'ajax_delete_campaign']);
        add_action('wp_ajax_fs_check_product_conflicts', [$this, 'ajax_check_product_conflicts']);
        add_action('wp_ajax_fs_get_product_info', [$this, 'ajax_get_product_info']);
    }
    
    /**
     * Get table name with prefix
     */
    private static function get_table($table) {
        global $wpdb;
        return $wpdb->prefix . $table;
    }
    
    /**
     * Get all campaigns with filters (inspired by getPromotionAll)
     */
    public static function get_campaigns($filters = [], $paged = 1) {
        global $wpdb;
        
        $table = self::get_table(self::$table_campaigns);
        $data = [];
        
        $where = ['1=1'];
        $where_values = [];
        
        // Filter by status
        if (isset($filters['filter-status']) && ($filters['filter-status'] == 0 || $filters['filter-status'] == 1)) {
            $where[] = 'campaign_status = %d';
            $where_values[] = $filters['filter-status'];
        }
        
        // Filter by type (always flash-sale for this plugin)
        if (isset($filters['filter-type']) && $filters['filter-type']) {
            $where[] = 'promotion_type = %s';
            $where_values[] = $filters['filter-type'];
        }
        
        $where_clause = implode(' AND ', $where);
        $offset = ($paged - 1) * self::$pageLimit;
        
        // Get total count
        $count_query = "SELECT COUNT(*) FROM {$table} WHERE {$where_clause}";
        if (!empty($where_values)) {
            $count_query = $wpdb->prepare($count_query, $where_values);
        }
        $total_count = $wpdb->get_var($count_query);
        
        // Get campaigns - Order by priority DESC (higher priority first), then by id DESC
        $query = "SELECT * FROM {$table} WHERE {$where_clause} ORDER BY priority DESC, id DESC LIMIT %d OFFSET %d";
        $query_values = array_merge($where_values, [self::$pageLimit, $offset]);
        $campaigns = $wpdb->get_results($wpdb->prepare($query, $query_values));
        
        // Add products to each campaign
        if ($campaigns) {
            foreach ($campaigns as &$campaign) {
                $campaign->products = self::get_campaign_products($campaign->id);
            }
        }
        
        return [
            'data' => $campaigns,
            'pagination' => self::handle_pagination($paged, $total_count, self::$pageLimit)
        ];
    }
    
    /**
     * Get single campaign (inspired by getCampaign)
     */
    public static function get_campaign($filters) {
        global $wpdb;
        
        $table = self::get_table(self::$table_campaigns);
        $where = ['1=1'];
        $where_values = [];
        
        if (isset($filters['promotion_id']) && $filters['promotion_id']) {
            $where[] = 'id = %d';
            $where_values[] = $filters['promotion_id'];
        }
        
        if (isset($filters['is_launching']) && $filters['is_launching']) {
            $where[] = 'start_date <= %s';
            $where[] = 'end_date > %s';
            $where_values[] = $filters['is_launching'];
            $where_values[] = $filters['is_launching'];
        }
        
        $where_clause = implode(' AND ', $where);
        $query = "SELECT * FROM {$table} WHERE {$where_clause}";
        
        if (!empty($where_values)) {
            $campaign = $wpdb->get_row($wpdb->prepare($query, $where_values));
        } else {
            $campaign = $wpdb->get_row($query);
        }
        
        if ($campaign) {
            $limit = isset($filters['per_page']) ? intval($filters['per_page']) : 1000;
            $paged = isset($filters['page']) ? $filters['page'] : 1;
            $offset = ($paged - 1) * $limit;
            
            // Get campaign products
            $products_table = self::get_table(self::$table_products);
            $products_query = "SELECT * FROM {$products_table} WHERE promotion_id = %d ORDER BY product_id ASC LIMIT %d OFFSET %d";
            $campaign->products = $wpdb->get_results($wpdb->prepare($products_query, $campaign->id, $limit, $offset));
            
            // Get total products count for pagination
            $count_query = "SELECT COUNT(*) FROM {$products_table} WHERE promotion_id = %d";
            $total_products = $wpdb->get_var($wpdb->prepare($count_query, $campaign->id));
            $campaign->pagination = self::handle_pagination($paged, $total_products, $limit);
        }
        
        return $campaign;
    }
    
    /**
     * Get campaign products
     */
    public static function get_campaign_products($campaign_id) {
        global $wpdb;
        
        $table = self::get_table(self::$table_products);
        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$table} WHERE promotion_id = %d ORDER BY product_id ASC",
            $campaign_id
        ));
    }
    
    /**
     * Add new campaign (inspired by addCampaign)
     */
    public static function add_campaign($campaign_data, $products) {
        global $wpdb;
        
        $table = self::get_table(self::$table_campaigns);
        $products_table = self::get_table(self::$table_products);
        
        $wpdb->query('START TRANSACTION');
        
        try {
            $campaign_id = $wpdb->insert($table, $campaign_data);
            
            if ($campaign_id) {
                foreach ($products as $key => &$p) {
                    $p['promotion_id'] = $campaign_id;
                }
                
                $result = $wpdb->insert_multi($products_table, $products);
                
                if ($result) {
                    $wpdb->query('COMMIT');
                    return $campaign_id;
                } else {
                    $wpdb->query('ROLLBACK');
                    return false;
                }
            }
            
            $wpdb->query('ROLLBACK');
            return false;
            
        } catch (Exception $e) {
            $wpdb->query('ROLLBACK');
            return false;
        }
    }
    
    /**
     * Update campaign (inspired by updateCampaign)
     */
    public static function update_campaign($campaign_data, $products) {
        global $wpdb;

        $table = self::get_table(self::$table_campaigns);
        $products_table = self::get_table(self::$table_products);

        $campaign_id = $campaign_data['id'];

        // Check if campaign exists
        $exist = self::get_campaign(['promotion_id' => $campaign_id]);
        if (!$exist) {
            return false;
        }

        $wpdb->query('START TRANSACTION');

        try {
            // Update campaign
            $campaign_result = $wpdb->update($table, $campaign_data, ['id' => $campaign_id]);

            if ($campaign_result !== false) {
                // Clear all existing products for this campaign first
                $wpdb->delete($products_table, ['promotion_id' => $campaign_id]);

                // Insert all products fresh
                if (!empty($products)) {
                    foreach ($products as $product_data) {
                        $product_data['promotion_id'] = $campaign_id;
                        $product_data['created_at'] = current_time('mysql');

                        $result = $wpdb->insert($products_table, $product_data);
                        if ($result === false) {
                            throw new Exception('Failed to insert product: ' . $wpdb->last_error);
                        }
                    }
                }

                $wpdb->query('COMMIT');
                return $campaign_id;
            }

            $wpdb->query('ROLLBACK');
            return false;

        } catch (Exception $e) {
            $wpdb->query('ROLLBACK');
            error_log('Campaign update failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Handle pagination (inspired by AIT_HandlePagination)
     */
    private static function handle_pagination($paged, $total_count, $per_page) {
        $total_pages = ceil($total_count / $per_page);
        
        return [
            'current_page' => $paged,
            'total_pages' => $total_pages,
            'total_items' => $total_count,
            'per_page' => $per_page,
            'has_prev' => $paged > 1,
            'has_next' => $paged < $total_pages
        ];
    }
    
    /**
     * AJAX: Search products
     */
    public function ajax_search_products() {
        check_ajax_referer('fs_search_products', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $query = sanitize_text_field($_POST['query']);
        $category_id = intval($_POST['category_id']);

        $args = [
            'post_type' => ['product', 'product_variation'],
            'post_status' => 'publish',
            'posts_per_page' => 20,
            'meta_query' => [
                [
                    'key' => '_stock_status',
                    'value' => 'instock'
                ]
            ]
        ];

        if (!empty($query)) {
            $args['s'] = $query;
        }

        if ($category_id > 0) {
            $args['tax_query'] = [
                [
                    'taxonomy' => 'product_cat',
                    'field' => 'term_id',
                    'terms' => $category_id
                ]
            ];
        }

        $products = get_posts($args);
        $results = [];

        foreach ($products as $product) {
            $wc_product = wc_get_product($product->ID);
            if (!$wc_product) continue;

            $parent_id = $wc_product->get_parent_id();
            $is_variation = $parent_id > 0;

            // Enhanced name for variations
            $display_name = $wc_product->get_name();
            if ($is_variation) {
                $variation_attributes = $this->format_variation_attributes($wc_product);
                if ($variation_attributes) {
                    $display_name .= ' - ' . $variation_attributes;
                }
            }

            // Get total sold quantity for variable products (sum of all variations)
            $total_sold = 0;
            if ($wc_product->is_type('variable')) {
                $variations = $wc_product->get_children();
                foreach ($variations as $variation_id) {
                    $variation = wc_get_product($variation_id);
                    if ($variation) {
                        $total_sold += intval($variation->get_total_sales());
                    }
                }
            } else {
                $total_sold = intval($wc_product->get_total_sales());
            }

            $results[] = [
                'id' => $wc_product->get_id(),
                'name' => $display_name,
                'sku' => $wc_product->get_sku(),
                'price' => wc_price($wc_product->get_regular_price()),
                'regular_price' => $wc_product->get_regular_price(),
                'type' => $wc_product->get_type(),
                'stock_status' => $wc_product->get_stock_status(),
                'is_variation' => $is_variation,
                'parent_id' => $parent_id,
                'total_sold' => $total_sold
            ];
        }

        wp_send_json_success($results);
    }

    /**
     * AJAX: Check product conflicts
     */
    public function ajax_check_product_conflicts() {
        check_ajax_referer('fs_check_product_conflicts', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $product_id = intval($_POST['product_id']);
        $exclude_campaign_id = intval($_POST['exclude_campaign_id'] ?? 0);

        if (!$product_id) {
            wp_send_json_error(['message' => __('Product ID is required', 'flashsale-core')]);
            return;
        }

        // Create a fake products array to use existing validation
        $products = [['product_id' => $product_id]];
        $conflict_result = $this->check_product_conflicts($products, $exclude_campaign_id ?: null);

        if (!$conflict_result['valid']) {
            // Parse conflicts from message
            global $wpdb;
            $products_table = self::get_table(self::$table_products);
            $campaigns_table = self::get_table(self::$table_campaigns);

            $exclude_clause = '';
            if ($exclude_campaign_id) {
                $exclude_clause = $wpdb->prepare(' AND c.id != %d', $exclude_campaign_id);
            }

            $query = "
                SELECT p.product_id, c.name as campaign_name, c.priority
                FROM {$products_table} p
                INNER JOIN {$campaigns_table} c ON p.promotion_id = c.id
                WHERE p.product_id = %d
                AND c.status = 1
                AND (c.end_date IS NULL OR c.end_date > NOW())
                {$exclude_clause}
            ";

            $conflicts = $wpdb->get_results($wpdb->prepare($query, $product_id));

            wp_send_json_success([
                'has_conflicts' => true,
                'conflicts' => $conflicts
            ]);
        } else {
            wp_send_json_success([
                'has_conflicts' => false,
                'conflicts' => []
            ]);
        }
    }

    /**
     * AJAX: Get product info
     */
    public function ajax_get_product_info() {
        check_ajax_referer('fs_get_product_info', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $product_id = intval($_POST['product_id']);

        if (!$product_id) {
            wp_send_json_error(['message' => __('Product ID is required', 'flashsale-core')]);
            return;
        }

        $product = wc_get_product($product_id);

        if (!$product) {
            wp_send_json_error(['message' => __('Product not found', 'flashsale-core')]);
            return;
        }

        $image_id = get_post_thumbnail_id($product->get_id());
        $image_url = $image_id ? wp_get_attachment_image_src($image_id, 'thumbnail')[0] : '';

        $product_info = [
            'id' => $product->get_id(),
            'name' => $product->get_name(),
            'sku' => $product->get_sku(),
            'price' => wc_price($product->get_regular_price()),
            'price_raw' => $product->get_regular_price(),
            'in_stock' => $product->get_stock_status() === 'instock',
            'image_url' => $image_url
        ];

        wp_send_json_success($product_info);
    }

    /**
     * AJAX: Save new campaign
     */
    public function ajax_save_campaign() {
        check_ajax_referer('fs_save_campaign', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $campaign_data = $this->sanitize_campaign_data($_POST);
        $products = $this->sanitize_products_data($_POST['products'] ?? []);

        // Validate campaign data
        $validation_result = $this->validate_campaign_data($campaign_data, $products);
        if (!$validation_result['valid']) {
            wp_send_json_error(['message' => $validation_result['message']]);
            return;
        }

        // Check for product conflicts
        $conflict_result = $this->check_product_conflicts($products);
        if (!$conflict_result['valid']) {
            wp_send_json_error(['message' => $conflict_result['message']]);
            return;
        }

        $campaign_id = self::add_campaign($campaign_data, $products);

        if ($campaign_id) {
            wp_send_json_success([
                'message' => __('Campaign saved successfully!', 'flashsale-core'),
                'campaign_id' => $campaign_id
            ]);
        } else {
            wp_send_json_error(['message' => __('Failed to save campaign', 'flashsale-core')]);
        }
    }

    /**
     * AJAX: Update existing campaign
     */
    public function ajax_update_campaign() {
        check_ajax_referer('fs_update_campaign', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $campaign_data = $this->sanitize_campaign_data($_POST);
        $products = $this->sanitize_products_data($_POST['products'] ?? []);

        if (!isset($campaign_data['id']) || !$campaign_data['id']) {
            wp_send_json_error(['message' => __('Campaign ID is required', 'flashsale-core')]);
            return;
        }

        // Validate campaign data
        $validation_result = $this->validate_campaign_data($campaign_data, $products);
        if (!$validation_result['valid']) {
            wp_send_json_error(['message' => $validation_result['message']]);
            return;
        }

        // Check for product conflicts (exclude current campaign)
        $conflict_result = $this->check_product_conflicts($products, $campaign_data['id']);
        if (!$conflict_result['valid']) {
            wp_send_json_error(['message' => $conflict_result['message']]);
            return;
        }

        $result = self::update_campaign($campaign_data, $products);

        if ($result) {
            wp_send_json_success([
                'message' => __('Campaign updated successfully!', 'flashsale-core')
            ]);
        } else {
            wp_send_json_error(['message' => __('Failed to update campaign', 'flashsale-core')]);
        }
    }

    /**
     * AJAX: Delete campaign
     */
    public function ajax_delete_campaign() {
        check_ajax_referer('fs_delete_campaign', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $campaign_id = intval($_POST['campaign_id']);

        if (!$campaign_id) {
            wp_send_json_error(['message' => __('Campaign ID is required', 'flashsale-core')]);
            return;
        }

        $result = self::delete_campaign($campaign_id);

        if ($result) {
            wp_send_json_success(['message' => __('Campaign deleted successfully!', 'flashsale-core')]);
        } else {
            wp_send_json_error(['message' => __('Failed to delete campaign', 'flashsale-core')]);
        }
    }

    /**
     * Sanitize campaign data
     */
    private function sanitize_campaign_data($post_data) {
        return [
            'id' => isset($post_data['campaign_id']) ? intval($post_data['campaign_id']) : null,
            'name' => sanitize_text_field($post_data['campaign_name'] ?? ''),
            'description' => sanitize_textarea_field($post_data['campaign_description'] ?? ''),
            'type' => sanitize_text_field($post_data['campaign_type'] ?? 'flash-sale'),
            'priority' => intval($post_data['campaign_priority'] ?? 0),
            'status' => intval($post_data['campaign_status'] ?? 1),
            'start_date' => sanitize_text_field($post_data['start_date'] ?? ''),
            'end_date' => sanitize_text_field($post_data['end_date'] ?? ''),
            'global_settings' => wp_json_encode($post_data['global_settings'] ?? [])
        ];
    }

    /**
     * Sanitize products data
     */
    private function sanitize_products_data($products_data) {
        $sanitized = [];

        if (!is_array($products_data)) {
            return $sanitized;
        }

        foreach ($products_data as $product) {
            $sanitized[] = [
                'product_id' => intval($product['product_id'] ?? 0),
                'regular_price' => floatval($product['regular_price'] ?? 0),
                'discount_type' => intval($product['discount_type'] ?? 2),
                'discount_value' => floatval($product['discount_value'] ?? 0),
                'max_discount_amount' => floatval($product['max_discount_amount'] ?? 0),
                'sold' => intval($product['sold'] ?? 0),
                'qty_max' => intval($product['qty_max'] ?? 0),
                'percent_sold_max' => intval($product['percent_sold_max'] ?? 0),
                'start_date' => sanitize_text_field($product['start_date'] ?? ''),
                'end_date' => sanitize_text_field($product['end_date'] ?? ''),
                'sku' => sanitize_text_field($product['sku'] ?? '')
            ];
        }

        return $sanitized;
    }

    /**
     * Validate campaign data
     */
    private function validate_campaign_data($campaign_data, $products) {
        // Check required fields
        if (empty($campaign_data['name'])) {
            return [
                'valid' => false,
                'message' => __('Campaign name is required', 'flashsale-core')
            ];
        }

        if (empty($products)) {
            return [
                'valid' => false,
                'message' => __('At least one product is required', 'flashsale-core')
            ];
        }

        // Validate date format
        if (!empty($campaign_data['start_date']) && !$this->is_valid_datetime($campaign_data['start_date'])) {
            return [
                'valid' => false,
                'message' => __('Invalid start date format', 'flashsale-core')
            ];
        }

        if (!empty($campaign_data['end_date']) && !$this->is_valid_datetime($campaign_data['end_date'])) {
            return [
                'valid' => false,
                'message' => __('Invalid end date format', 'flashsale-core')
            ];
        }

        // Validate products
        foreach ($products as $product) {
            if (empty($product['product_id'])) {
                return [
                    'valid' => false,
                    'message' => __('Product ID is required for all products', 'flashsale-core')
                ];
            }

            if ($product['discount_value'] <= 0) {
                return [
                    'valid' => false,
                    'message' => __('Discount value must be greater than 0', 'flashsale-core')
                ];
            }
        }

        return ['valid' => true];
    }

    /**
     * Check for product conflicts with other active campaigns
     */
    private function check_product_conflicts($products, $exclude_campaign_id = null) {
        global $wpdb;

        $products_table = self::get_table(self::$table_products);
        $campaigns_table = self::get_table(self::$table_campaigns);

        $product_ids = array_column($products, 'product_id');
        $product_ids_str = implode(',', array_map('intval', $product_ids));

        $exclude_clause = '';
        if ($exclude_campaign_id) {
            $exclude_clause = $wpdb->prepare(' AND c.id != %d', $exclude_campaign_id);
        }

        // Check for products in other active campaigns
        $query = "
            SELECT p.product_id, c.name as campaign_name, c.priority
            FROM {$products_table} p
            INNER JOIN {$campaigns_table} c ON p.promotion_id = c.id
            WHERE p.product_id IN ({$product_ids_str})
            AND c.status = 1
            AND (c.end_date IS NULL OR c.end_date > NOW())
            {$exclude_clause}
        ";

        $conflicts = $wpdb->get_results($query);

        if (!empty($conflicts)) {
            $conflict_messages = [];
            foreach ($conflicts as $conflict) {
                $product = wc_get_product($conflict->product_id);
                $product_name = $product ? $product->get_name() : "Product ID {$conflict->product_id}";
                $conflict_messages[] = sprintf(
                    __('Product "%s" is already in campaign "%s" (Priority: %d)', 'flashsale-core'),
                    $product_name,
                    $conflict->campaign_name,
                    $conflict->priority
                );
            }

            return [
                'valid' => false,
                'message' => implode('<br>', $conflict_messages)
            ];
        }

        return ['valid' => true];
    }

    /**
     * Check if datetime string is valid
     */
    private function is_valid_datetime($datetime_string) {
        $datetime = DateTime::createFromFormat('Y-m-d H:i:s', $datetime_string);
        return $datetime && $datetime->format('Y-m-d H:i:s') === $datetime_string;
    }

    /**
     * Delete campaign
     */
    public static function delete_campaign($campaign_id) {
        global $wpdb;

        $campaigns_table = self::get_table(self::$table_campaigns);
        $products_table = self::get_table(self::$table_products);

        $wpdb->query('START TRANSACTION');

        try {
            // Delete campaign products first
            $wpdb->delete($products_table, ['promotion_id' => $campaign_id]);

            // Delete campaign
            $result = $wpdb->delete($campaigns_table, ['id' => $campaign_id]);

            if ($result) {
                $wpdb->query('COMMIT');
                return true;
            } else {
                $wpdb->query('ROLLBACK');
                return false;
            }
        } catch (Exception $e) {
            $wpdb->query('ROLLBACK');
            return false;
        }
    }

    /**
     * Format variation attributes for display
     */
    private function format_variation_attributes($variation_product) {
        $attributes = [];
        $variation_attributes = $variation_product->get_variation_attributes();

        foreach ($variation_attributes as $name => $value) {
            $taxonomy = str_replace('attribute_', '', $name);

            if (taxonomy_exists($taxonomy)) {
                $term = get_term_by('slug', $value, $taxonomy);
                $attribute_label = wc_attribute_label($taxonomy);
                $value_label = $term ? $term->name : $value;
            } else {
                $attribute_label = str_replace('pa_', '', $taxonomy);
                $value_label = $value;
            }

            $attributes[] = $attribute_label . ': ' . $value_label;
        }

        return implode(', ', $attributes);
    }
}
