<?php
/**
 * Fired during plugin activation
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class FS_Activator {
    
    /**
     * Plugin activation
     */
    public static function activate() {
        // Check WordPress version
        if (version_compare(get_bloginfo('version'), '5.0', '<')) {
            deactivate_plugins(plugin_basename(__FILE__));
            wp_die(__('CanhcamPromotion Core requires WordPress 5.0 or higher.', 'canhcampromotion-core'));
        }

        // Check PHP version
        if (version_compare(PHP_VERSION, '7.4', '<')) {
            deactivate_plugins(plugin_basename(__FILE__));
            wp_die(__('CanhcamPromotion Core requires PHP 7.4 or higher.', 'canhcampromotion-core'));
        }

        // Check WooCommerce
        if (!class_exists('WooCommerce')) {
            deactivate_plugins(plugin_basename(__FILE__));
            wp_die(__('CanhcamPromotion Core requires WooCommerce to be installed and active.', 'canhcampromotion-core'));
        }
        
        // Create database tables
        self::create_database_tables();
        
        // Set default options
        self::set_default_options();

        // Clean up demo addons (if any exist)
        self::cleanup_demo_addons();

        // Create default addons (now does nothing)
        self::create_default_addons();
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Set activation flag
        update_option('fs_activation_time', current_time('mysql'));
        update_option('fs_version', FLASHSALE_CORE_VERSION);
        
        do_action('flashsale_core_activated');
    }
    
    /**
     * Create database tables
     */
    private static function create_database_tables() {
        require_once FLASHSALE_CORE_PLUGIN_DIR . 'includes/class-fs-database.php';
        
        $database = new FS_Database();
        $database->create_tables();
    }
    
    /**
     * Set default options
     */
    private static function set_default_options() {
        $default_options = [
            'fs_enable_legacy_support' => 1,
            'fs_enable_debug' => 0,
            'fs_cache_campaigns' => 1,
            'fs_cache_duration' => 3600, // 1 hour
            'fs_enable_stats' => 1,
            'fs_enable_api' => 1
        ];
        
        foreach ($default_options as $option => $value) {
            if (get_option($option) === false) {
                update_option($option, $value);
            }
        }
    }

    /**
     * Clean up demo addons from database
     */
    private static function cleanup_demo_addons() {
        global $wpdb;

        $table = FS_Database::get_table_name('addons');

        // List of demo addon slugs that should be removed
        $demo_addon_slugs = [
            'product-promotion',
            'category-promotion',
            'quantity-promotion',
            'order-promotion',
            'combo-promotion',
            'shipping-promotion',
            'upsell-promotion',
            'gift-promotion'
        ];

        // Remove demo addons
        foreach ($demo_addon_slugs as $slug) {
            $wpdb->delete($table, ['addon_slug' => $slug]);
        }
    }

    /**
     * Create default addons - REMOVED
     * Addons should be separate plugins, not demo data
     */
    private static function create_default_addons() {
        // No longer create demo addons
        // Addons are separate WordPress plugins that register themselves
        // when they are installed and activated

        // This method is kept for backward compatibility but does nothing
        return;
    }
}
