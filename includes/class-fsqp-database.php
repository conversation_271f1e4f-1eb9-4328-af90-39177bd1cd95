<?php
/**
 * Database management for Quantity Promotion
 * Uses FlashSale Core database structure instead of creating new tables
 *
 * @package FlashSale_Quantity_Promotion
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class FSQP_Database {

    /**
     * Constructor
     */
    public function __construct() {
        // No need to create new tables, use FlashSale Core structure
    }

    /**
     * Create database tables (not needed - using FlashSale Core tables)
     */
    public function create_tables() {
        // FlashSale Core already has the tables we need:
        // - fs_campaigns (with type='quantity-promotion')
        // - fs_campaign_products (stores quantity rules as products with special fields)
        // - We'll use global_settings to store quantity-specific configuration

        // Only create logs table for tracking
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        $logs_table = $wpdb->prefix . 'fsqp_promotion_logs';

        $logs_sql = "CREATE TABLE $logs_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            order_id bigint(20) unsigned NOT NULL,
            product_id bigint(20) unsigned NOT NULL,
            campaign_id bigint(20) unsigned NOT NULL,
            quantity int(11) NOT NULL,
            original_price decimal(10,2) NOT NULL,
            discounted_price decimal(10,2) NOT NULL,
            discount_amount decimal(10,2) NOT NULL,
            discount_type tinyint(1) NOT NULL,
            min_quantity int(11) NOT NULL,
            max_quantity int(11) NOT NULL DEFAULT 0,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY order_id (order_id),
            KEY product_id (product_id),
            KEY campaign_id (campaign_id),
            KEY created_at (created_at)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($logs_sql);
    }
    
    /**
     * Maybe upgrade database
     */
    public function maybe_upgrade() {
        $current_version = get_option('fsqp_db_version', '0.0.0');
        
        if (version_compare($current_version, FSQP_VERSION, '<')) {
            $this->create_tables();
            update_option('fsqp_db_version', FSQP_VERSION);
        }
    }
    
    /**
     * Get quantity rules for product using FlashSale Core structure
     */
    public function get_product_quantity_rules($product_id, $active_only = true) {
        global $wpdb;

        $products_table = $wpdb->prefix . 'fs_campaign_products';
        $campaigns_table = $wpdb->prefix . 'fs_campaigns';

        $where_clause = "p.product_id = %d AND c.type = 'quantity-promotion'";
        $params = [$product_id];

        if ($active_only) {
            $where_clause .= " AND c.status = 1 AND (c.end_date IS NULL OR c.end_date > NOW())";
        }

        $sql = "
            SELECT
                p.id,
                p.promotion_id as campaign_id,
                p.product_id,
                p.discount_type,
                p.discount_value,
                p.max_discount_amount,
                p.qty_max as min_quantity,
                p.percent_sold_max as max_quantity,
                c.name as campaign_name,
                c.priority as campaign_priority,
                c.global_settings
            FROM $products_table p
            INNER JOIN $campaigns_table c ON p.promotion_id = c.id
            WHERE $where_clause
            ORDER BY c.priority DESC, p.id ASC
        ";

        $results = $wpdb->get_results($wpdb->prepare($sql, $params));

        // Parse quantity rules from global_settings if needed
        foreach ($results as $result) {
            $global_settings = json_decode($result->global_settings, true);
            if (isset($global_settings['quantity_rules'])) {
                // If quantity rules are stored in global_settings, use those
                $quantity_rules = $global_settings['quantity_rules'];
                foreach ($quantity_rules as $rule) {
                    if ($rule['product_id'] == $product_id) {
                        $result->min_quantity = $rule['min_quantity'];
                        $result->max_quantity = $rule['max_quantity'];
                        break;
                    }
                }
            }
        }

        return $results;
    }

    /**
     * Get quantity rules for campaign
     */
    public function get_campaign_quantity_rules($campaign_id, $active_only = true) {
        global $wpdb;

        $products_table = $wpdb->prefix . 'fs_campaign_products';
        $campaigns_table = $wpdb->prefix . 'fs_campaigns';

        $where_clause = "p.promotion_id = %d AND c.type = 'quantity-promotion'";
        $params = [$campaign_id];

        if ($active_only) {
            $where_clause .= " AND c.status = 1";
        }

        $sql = "
            SELECT
                p.id,
                p.promotion_id as campaign_id,
                p.product_id,
                p.discount_type,
                p.discount_value,
                p.max_discount_amount,
                p.qty_max as min_quantity,
                p.percent_sold_max as max_quantity,
                p.regular_price,
                p.sku,
                c.name as campaign_name,
                c.priority as campaign_priority,
                c.global_settings
            FROM $products_table p
            INNER JOIN $campaigns_table c ON p.promotion_id = c.id
            WHERE $where_clause
            ORDER BY p.product_id ASC, p.id ASC
        ";

        $results = $wpdb->get_results($wpdb->prepare($sql, $params));

        // Add product names
        foreach ($results as $result) {
            $product = wc_get_product($result->product_id);
            if ($product) {
                $result->product_name = $product->get_name();
                if (!$result->sku) {
                    $result->sku = $product->get_sku();
                }
                if (!$result->regular_price) {
                    $result->regular_price = $product->get_regular_price();
                }
            } else {
                $result->product_name = 'Product #' . $result->product_id;
            }
        }

        return $results;
    }

    /**
     * Get applicable rule for quantity
     */
    public function get_applicable_rule($product_id, $quantity) {
        $rules = $this->get_product_quantity_rules($product_id);
        
        foreach ($rules as $rule) {
            if ($quantity >= $rule->min_quantity && 
                ($rule->max_quantity == 0 || $quantity <= $rule->max_quantity)) {
                return $rule;
            }
        }
        
        return null;
    }
    
    /**
     * Save quantity rule using FlashSale Core structure
     */
    public function save_quantity_rule($data) {
        global $wpdb;

        error_log("FSQP_Database: save_quantity_rule called with data: " . print_r($data, true));

        $products_table = $wpdb->prefix . 'fs_campaign_products';

        // Map quantity rule data to fs_campaign_products structure
        $product_data = [
            'promotion_id' => intval($data['campaign_id']),
            'product_id' => intval($data['product_id']),
            'regular_price' => floatval($data['regular_price'] ?? 0),
            'discount_type' => intval($data['discount_type']),
            'discount_value' => floatval($data['discount_value']),
            'max_discount_amount' => floatval($data['max_discount_amount'] ?? 0),
            'qty_max' => intval($data['min_quantity']), // Store min_quantity in qty_max field
            'percent_sold_max' => intval($data['max_quantity']), // Store max_quantity in percent_sold_max field
            'sku' => sanitize_text_field($data['sku'] ?? ''),
            'created_at' => current_time('mysql')
        ];

        error_log("FSQP_Database: Mapped product_data: " . print_r($product_data, true));

        if (isset($data['id']) && $data['id'] > 0) {
            // Update existing rule
            unset($product_data['created_at']); // Don't update created_at
            error_log("FSQP_Database: Updating existing rule with ID: " . $data['id']);
            $result = $wpdb->update(
                $products_table,
                $product_data,
                ['id' => intval($data['id'])]
            );

            if ($result === false) {
                error_log("FSQP_Database: Update failed - " . $wpdb->last_error);
            } else {
                error_log("FSQP_Database: Update successful, affected rows: $result");
            }

            return $result !== false ? intval($data['id']) : false;
        } else {
            // Insert new rule
            error_log("FSQP_Database: Inserting new rule");
            $result = $wpdb->insert($products_table, $product_data);

            if ($result === false) {
                error_log("FSQP_Database: Insert failed - " . $wpdb->last_error);
                return false;
            } else {
                $insert_id = $wpdb->insert_id;
                error_log("FSQP_Database: Insert successful, new ID: $insert_id");
                return $insert_id;
            }
        }
    }
    
    /**
     * Delete quantity rule using FlashSale Core structure
     */
    public function delete_quantity_rule($rule_id) {
        global $wpdb;

        $products_table = $wpdb->prefix . 'fs_campaign_products';

        return $wpdb->delete($products_table, ['id' => intval($rule_id)]);
    }

    /**
     * Log promotion usage
     */
    public function log_promotion_usage($data) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'fsqp_promotion_logs';

        $log_data = [
            'order_id' => intval($data['order_id']),
            'product_id' => intval($data['product_id']),
            'campaign_id' => intval($data['campaign_id']),
            'quantity' => intval($data['quantity']),
            'original_price' => floatval($data['original_price']),
            'discounted_price' => floatval($data['discounted_price']),
            'discount_amount' => floatval($data['discount_amount']),
            'discount_type' => intval($data['discount_type']),
            'min_quantity' => intval($data['min_quantity']),
            'max_quantity' => intval($data['max_quantity'])
        ];

        return $wpdb->insert($table_name, $log_data);
    }
    
    /**
     * Get promotion statistics
     */
    public function get_promotion_stats($campaign_id = null, $date_from = null, $date_to = null) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'fsqp_promotion_logs';
        $where_conditions = [];
        $params = [];
        
        if ($campaign_id) {
            $where_conditions[] = "r.campaign_id = %d";
            $params[] = $campaign_id;
        }
        
        if ($date_from) {
            $where_conditions[] = "l.created_at >= %s";
            $params[] = $date_from;
        }
        
        if ($date_to) {
            $where_conditions[] = "l.created_at <= %s";
            $params[] = $date_to;
        }
        
        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
        
        $sql = "
            SELECT 
                COUNT(*) as total_uses,
                SUM(l.discount_amount) as total_discount,
                AVG(l.discount_amount) as avg_discount,
                SUM(l.quantity) as total_quantity
            FROM $table_name l
            INNER JOIN {$wpdb->prefix}fsqp_quantity_rules r ON l.rule_id = r.id
            $where_clause
        ";
        
        if (!empty($params)) {
            return $wpdb->get_row($wpdb->prepare($sql, $params));
        } else {
            return $wpdb->get_row($sql);
        }
    }
}
