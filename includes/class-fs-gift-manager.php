<?php
/**
 * Gift Manager for FlashSale Core
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class FS_Gift_Manager {
    
    /**
     * Gift types
     */
    private $gift_types = [];
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_gift_types();
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', [$this, 'register_gift_types']);
        add_action('woocommerce_add_to_cart', [$this, 'check_gift_eligibility'], 10, 6);
        add_action('woocommerce_cart_updated', [$this, 'update_cart_gifts']);
    }
    
    /**
     * Initialize default gift types
     */
    private function init_gift_types() {
        $this->gift_types = [
            'product' => [
                'label' => __('Product Gift', 'flashsale-core'),
                'description' => __('Give a WooCommerce product as gift', 'flashsale-core'),
                'handler' => [$this, 'handle_product_gift']
            ],
            'offline' => [
                'label' => __('Offline Gift', 'flashsale-core'),
                'description' => __('Physical gift not managed in WooCommerce', 'flashsale-core'),
                'handler' => [$this, 'handle_offline_gift']
            ],
            'coupon' => [
                'label' => __('Coupon Gift', 'flashsale-core'),
                'description' => __('Give a coupon code as gift', 'flashsale-core'),
                'handler' => [$this, 'handle_coupon_gift']
            ],
            'points' => [
                'label' => __('Points Gift', 'flashsale-core'),
                'description' => __('Give loyalty points as gift', 'flashsale-core'),
                'handler' => [$this, 'handle_points_gift']
            ]
        ];
    }
    
    /**
     * Register gift types
     */
    public function register_gift_types() {
        $this->gift_types = apply_filters('fs_gift_types', $this->gift_types);
    }
    
    /**
     * Register a new gift type
     */
    public function register_gift_type($type, $config) {
        $this->gift_types[$type] = $config;
    }
    
    /**
     * Get all gift types
     */
    public function get_gift_types() {
        return $this->gift_types;
    }
    
    /**
     * Get gift type
     */
    public function get_gift_type($type) {
        return $this->gift_types[$type] ?? null;
    }
    
    /**
     * Check gift eligibility when item added to cart
     */
    public function check_gift_eligibility($cart_item_key, $product_id, $quantity, $variation_id, $variation, $cart_item_data) {
        $campaigns = FS()->campaigns()->get_active_campaigns();
        
        foreach ($campaigns as $campaign) {
            $this->process_campaign_gifts($campaign, $product_id, $quantity);
        }
    }
    
    /**
     * Process campaign gifts
     */
    private function process_campaign_gifts($campaign, $product_id, $quantity) {
        $gifts = FS()->campaigns()->get_campaign_gifts($campaign->id);
        
        foreach ($gifts as $gift) {
            if ($this->is_gift_eligible($gift, $product_id, $quantity)) {
                $this->add_gift($gift);
            }
        }
    }
    
    /**
     * Check if gift is eligible
     */
    private function is_gift_eligible($gift, $product_id, $quantity) {
        $gift_config = $gift->gift_config;
        
        // Check if gift is for this product
        if (isset($gift_config['target_products'])) {
            if (!in_array($product_id, $gift_config['target_products'])) {
                return false;
            }
        }
        
        // Check quantity requirements
        if (isset($gift_config['min_quantity'])) {
            if ($quantity < $gift_config['min_quantity']) {
                return false;
            }
        }
        
        // Check if gift already added
        if ($this->is_gift_in_cart($gift)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Add gift to cart or process
     */
    public function add_gift($gift) {
        $gift_type = $this->get_gift_type($gift->gift_type);
        
        if ($gift_type && isset($gift_type['handler'])) {
            return call_user_func($gift_type['handler'], $gift);
        }
        
        return false;
    }
    
    /**
     * Handle product gift
     */
    public function handle_product_gift($gift) {
        $gift_config = $gift->gift_config;
        $gift_product_id = $gift->gift_id;
        
        if (!$gift_product_id) {
            return false;
        }
        
        $product = wc_get_product($gift_product_id);
        if (!$product) {
            return false;
        }
        
        // Add to cart with special flag
        $cart_item_data = [
            'fs_gift' => true,
            'fs_gift_id' => $gift->id,
            'fs_campaign_id' => $gift->campaign_id,
            'fs_original_price' => $product->get_price()
        ];
        
        // Set price to 0 for gifts
        add_filter('woocommerce_add_cart_item_data', function($cart_item_data_filter) use ($cart_item_data) {
            return array_merge($cart_item_data_filter, $cart_item_data);
        });
        
        add_filter('woocommerce_add_cart_item', [$this, 'set_gift_price'], 10, 1);
        
        $cart_item_key = WC()->cart->add_to_cart($gift_product_id, 1, 0, [], $cart_item_data);
        
        remove_filter('woocommerce_add_cart_item', [$this, 'set_gift_price'], 10);
        
        return $cart_item_key !== false;
    }
    
    /**
     * Set gift price to 0
     */
    public function set_gift_price($cart_item) {
        if (isset($cart_item['fs_gift']) && $cart_item['fs_gift']) {
            $cart_item['data']->set_price(0);
        }
        
        return $cart_item;
    }
    
    /**
     * Handle offline gift
     */
    public function handle_offline_gift($gift) {
        $gift_config = $gift->gift_config;
        
        // Store offline gift information in session or user meta
        $offline_gifts = WC()->session->get('fs_offline_gifts', []);
        $offline_gifts[] = [
            'gift_id' => $gift->id,
            'campaign_id' => $gift->campaign_id,
            'gift_name' => $gift->gift_name,
            'description' => $gift_config['description'] ?? '',
            'image' => $gift_config['image'] ?? '',
            'added_at' => current_time('mysql')
        ];
        
        WC()->session->set('fs_offline_gifts', $offline_gifts);
        
        return true;
    }
    
    /**
     * Handle coupon gift
     */
    public function handle_coupon_gift($gift) {
        $gift_config = $gift->gift_config;
        $coupon_code = $gift_config['coupon_code'] ?? '';
        
        if (!$coupon_code) {
            return false;
        }
        
        // Apply coupon to cart
        if (!WC()->cart->has_discount($coupon_code)) {
            WC()->cart->apply_coupon($coupon_code);
            
            // Store gift coupon information
            $gift_coupons = WC()->session->get('fs_gift_coupons', []);
            $gift_coupons[] = [
                'gift_id' => $gift->id,
                'campaign_id' => $gift->campaign_id,
                'coupon_code' => $coupon_code,
                'added_at' => current_time('mysql')
            ];
            
            WC()->session->set('fs_gift_coupons', $gift_coupons);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Handle points gift
     */
    public function handle_points_gift($gift) {
        $gift_config = $gift->gift_config;
        $points = intval($gift_config['points'] ?? 0);
        
        if ($points <= 0) {
            return false;
        }
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            return false;
        }
        
        // Add points to user (this would integrate with a points system)
        $current_points = get_user_meta($user_id, 'fs_loyalty_points', true) ?: 0;
        $new_points = $current_points + $points;
        
        update_user_meta($user_id, 'fs_loyalty_points', $new_points);
        
        // Log the points transaction
        $this->log_points_transaction($user_id, $points, $gift);
        
        return true;
    }
    
    /**
     * Log points transaction
     */
    private function log_points_transaction($user_id, $points, $gift) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'fs_points_transactions';
        
        // Create table if it doesn't exist
        $wpdb->query("CREATE TABLE IF NOT EXISTS $table (
            id int(11) NOT NULL AUTO_INCREMENT,
            user_id int(11) NOT NULL,
            points int(11) NOT NULL,
            transaction_type varchar(50) NOT NULL,
            gift_id int(11),
            campaign_id int(11),
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        )");
        
        $wpdb->insert($table, [
            'user_id' => $user_id,
            'points' => $points,
            'transaction_type' => 'gift',
            'gift_id' => $gift->id,
            'campaign_id' => $gift->campaign_id,
            'created_at' => current_time('mysql')
        ]);
    }
    
    /**
     * Check if gift is already in cart
     */
    private function is_gift_in_cart($gift) {
        foreach (WC()->cart->get_cart() as $cart_item) {
            if (isset($cart_item['fs_gift_id']) && $cart_item['fs_gift_id'] == $gift->id) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Update cart gifts when cart changes
     */
    public function update_cart_gifts() {
        // Remove gifts that are no longer eligible
        $this->remove_ineligible_gifts();
        
        // Add new eligible gifts
        $this->add_eligible_gifts();
    }
    
    /**
     * Remove ineligible gifts from cart
     */
    private function remove_ineligible_gifts() {
        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            if (isset($cart_item['fs_gift']) && $cart_item['fs_gift']) {
                $gift_id = $cart_item['fs_gift_id'];
                
                // Check if gift is still eligible
                if (!$this->is_gift_still_eligible($gift_id)) {
                    WC()->cart->remove_cart_item($cart_item_key);
                }
            }
        }
    }
    
    /**
     * Add eligible gifts to cart
     */
    private function add_eligible_gifts() {
        $campaigns = FS()->campaigns()->get_active_campaigns();
        
        foreach ($campaigns as $campaign) {
            $gifts = FS()->campaigns()->get_campaign_gifts($campaign->id);
            
            foreach ($gifts as $gift) {
                if ($this->should_add_gift($gift)) {
                    $this->add_gift($gift);
                }
            }
        }
    }
    
    /**
     * Check if gift is still eligible
     */
    private function is_gift_still_eligible($gift_id) {
        // Implementation to check if gift conditions are still met
        return true; // Simplified for now
    }
    
    /**
     * Check if gift should be added
     */
    private function should_add_gift($gift) {
        // Check if gift is not already in cart and conditions are met
        return !$this->is_gift_in_cart($gift) && $this->check_gift_conditions($gift);
    }
    
    /**
     * Check gift conditions
     */
    private function check_gift_conditions($gift) {
        // Implementation to check gift conditions
        return true; // Simplified for now
    }
    
    /**
     * Get user's offline gifts
     */
    public function get_offline_gifts() {
        return WC()->session->get('fs_offline_gifts', []);
    }
    
    /**
     * Get user's gift coupons
     */
    public function get_gift_coupons() {
        return WC()->session->get('fs_gift_coupons', []);
    }
    
    /**
     * Clear all gifts from session
     */
    public function clear_session_gifts() {
        WC()->session->__unset('fs_offline_gifts');
        WC()->session->__unset('fs_gift_coupons');
    }
}
