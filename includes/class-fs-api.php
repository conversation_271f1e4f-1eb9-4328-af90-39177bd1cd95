<?php
/**
 * API functionality for FlashSale Core
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class FS_API {
    
    /**
     * API namespace
     */
    const NAMESPACE = 'flashsale/v1';
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('rest_api_init', [$this, 'register_routes']);
    }
    
    /**
     * Register REST API routes
     */
    public function register_routes() {
        // Campaigns endpoints
        register_rest_route(self::NAMESPACE, '/campaigns', [
            [
                'methods' => WP_REST_Server::READABLE,
                'callback' => [$this, 'get_campaigns'],
                'permission_callback' => [$this, 'check_permissions']
            ],
            [
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => [$this, 'create_campaign'],
                'permission_callback' => [$this, 'check_admin_permissions']
            ]
        ]);
        
        register_rest_route(self::NAMESPACE, '/campaigns/(?P<id>\d+)', [
            [
                'methods' => WP_REST_Server::READABLE,
                'callback' => [$this, 'get_campaign'],
                'permission_callback' => [$this, 'check_permissions']
            ],
            [
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => [$this, 'update_campaign'],
                'permission_callback' => [$this, 'check_admin_permissions']
            ],
            [
                'methods' => WP_REST_Server::DELETABLE,
                'callback' => [$this, 'delete_campaign'],
                'permission_callback' => [$this, 'check_admin_permissions']
            ]
        ]);
        
        // Addons endpoints
        register_rest_route(self::NAMESPACE, '/addons', [
            'methods' => WP_REST_Server::READABLE,
            'callback' => [$this, 'get_addons'],
            'permission_callback' => [$this, 'check_admin_permissions']
        ]);
        
        register_rest_route(self::NAMESPACE, '/addons/(?P<slug>[a-zA-Z0-9-]+)/toggle', [
            'methods' => WP_REST_Server::CREATABLE,
            'callback' => [$this, 'toggle_addon'],
            'permission_callback' => [$this, 'check_admin_permissions']
        ]);
        
        // Product promotions endpoint
        register_rest_route(self::NAMESPACE, '/products/(?P<id>\d+)/promotions', [
            'methods' => WP_REST_Server::READABLE,
            'callback' => [$this, 'get_product_promotions'],
            'permission_callback' => [$this, 'check_permissions']
        ]);
        
        // Stats endpoint
        register_rest_route(self::NAMESPACE, '/stats', [
            'methods' => WP_REST_Server::READABLE,
            'callback' => [$this, 'get_stats'],
            'permission_callback' => [$this, 'check_admin_permissions']
        ]);
    }
    
    /**
     * Check basic permissions
     */
    public function check_permissions() {
        return true; // Public access for reading
    }
    
    /**
     * Check admin permissions
     */
    public function check_admin_permissions() {
        return current_user_can('manage_options');
    }
    
    /**
     * Get campaigns
     */
    public function get_campaigns($request) {
        $campaigns = FS()->campaigns()->get_active_campaigns();
        
        $data = [];
        foreach ($campaigns as $campaign) {
            $data[] = $this->prepare_campaign_response($campaign);
        }
        
        return rest_ensure_response($data);
    }
    
    /**
     * Get single campaign
     */
    public function get_campaign($request) {
        $campaign_id = $request->get_param('id');
        $campaign = FS()->campaigns()->get_campaign($campaign_id);
        
        if (!$campaign) {
            return new WP_Error('campaign_not_found', __('Campaign not found', 'flashsale-core'), ['status' => 404]);
        }
        
        return rest_ensure_response($this->prepare_campaign_response($campaign));
    }
    
    /**
     * Create campaign
     */
    public function create_campaign($request) {
        $data = $request->get_json_params();
        
        $campaign_data = [
            'name' => sanitize_text_field($data['name']),
            'description' => sanitize_textarea_field($data['description']),
            'type' => sanitize_text_field($data['type']),
            'priority' => intval($data['priority']),
            'status' => intval($data['status']),
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'],
            'global_settings' => $data['global_settings'] ?? [],
            'targets' => $data['targets'] ?? [],
            'gifts' => $data['gifts'] ?? [],
            'rules' => $data['rules'] ?? []
        ];
        
        $campaign_id = FS()->campaigns()->create_campaign($campaign_data);
        
        if (!$campaign_id) {
            return new WP_Error('campaign_creation_failed', __('Failed to create campaign', 'flashsale-core'), ['status' => 500]);
        }
        
        $campaign = FS()->campaigns()->get_campaign($campaign_id);
        
        return rest_ensure_response($this->prepare_campaign_response($campaign));
    }
    
    /**
     * Update campaign
     */
    public function update_campaign($request) {
        $campaign_id = $request->get_param('id');
        $data = $request->get_json_params();
        
        $campaign_data = [
            'name' => sanitize_text_field($data['name']),
            'description' => sanitize_textarea_field($data['description']),
            'type' => sanitize_text_field($data['type']),
            'priority' => intval($data['priority']),
            'status' => intval($data['status']),
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'],
            'global_settings' => $data['global_settings'] ?? []
        ];
        
        $result = FS()->campaigns()->update_campaign($campaign_id, $campaign_data);
        
        if (!$result) {
            return new WP_Error('campaign_update_failed', __('Failed to update campaign', 'flashsale-core'), ['status' => 500]);
        }
        
        $campaign = FS()->campaigns()->get_campaign($campaign_id);
        
        return rest_ensure_response($this->prepare_campaign_response($campaign));
    }
    
    /**
     * Delete campaign
     */
    public function delete_campaign($request) {
        $campaign_id = $request->get_param('id');
        
        $result = FS()->campaigns()->delete_campaign($campaign_id);
        
        if (!$result) {
            return new WP_Error('campaign_deletion_failed', __('Failed to delete campaign', 'flashsale-core'), ['status' => 500]);
        }
        
        return rest_ensure_response(['deleted' => true]);
    }
    
    /**
     * Get addons
     */
    public function get_addons($request) {
        $addons = FS()->addons()->get_all_addons();
        
        $data = [];
        foreach ($addons as $addon) {
            $data[] = [
                'id' => $addon->id,
                'name' => $addon->addon_name,
                'slug' => $addon->addon_slug,
                'version' => $addon->addon_version,
                'type' => $addon->addon_type,
                'status' => $addon->status,
                'settings' => $addon->settings,
                'dependencies' => $addon->dependencies,
                'activated_at' => $addon->activated_at,
                'updated_at' => $addon->updated_at
            ];
        }
        
        return rest_ensure_response($data);
    }
    
    /**
     * Toggle addon
     */
    public function toggle_addon($request) {
        $addon_slug = $request->get_param('slug');
        $action = $request->get_param('action'); // 'activate' or 'deactivate'
        
        if ($action === 'activate') {
            $result = FS()->addons()->activate_addon($addon_slug);
        } else {
            $result = FS()->addons()->deactivate_addon($addon_slug);
        }
        
        if (!$result) {
            return new WP_Error('addon_toggle_failed', __('Failed to toggle addon', 'flashsale-core'), ['status' => 500]);
        }
        
        return rest_ensure_response(['success' => true, 'action' => $action]);
    }
    
    /**
     * Get product promotions
     */
    public function get_product_promotions($request) {
        $product_id = $request->get_param('id');
        
        $campaigns = FS()->campaigns()->get_active_campaigns();
        $promotions = [];
        
        foreach ($campaigns as $campaign) {
            $targets = FS()->campaigns()->get_campaign_targets($campaign->id);
            
            foreach ($targets as $target) {
                if ($target->target_type === 'product' && $target->target_id == $product_id) {
                    $promotions[] = [
                        'campaign_id' => $campaign->id,
                        'campaign_name' => $campaign->name,
                        'campaign_type' => $campaign->type,
                        'pricing_rules' => $target->pricing_rules,
                        'start_date' => $campaign->start_date,
                        'end_date' => $campaign->end_date
                    ];
                }
            }
        }
        
        return rest_ensure_response($promotions);
    }
    
    /**
     * Get stats
     */
    public function get_stats($request) {
        global $wpdb;
        
        $campaigns_table = FS_Database::get_table_name('campaigns');
        $addons_table = FS_Database::get_table_name('addons');
        $stats_table = FS_Database::get_table_name('campaign_stats');
        
        // Get basic counts
        $total_campaigns = $wpdb->get_var("SELECT COUNT(*) FROM $campaigns_table");
        $active_campaigns = $wpdb->get_var("SELECT COUNT(*) FROM $campaigns_table WHERE status = 1");
        $total_addons = $wpdb->get_var("SELECT COUNT(*) FROM $addons_table");
        $active_addons = $wpdb->get_var("SELECT COUNT(*) FROM $addons_table WHERE status = 1");
        
        // Get revenue stats
        $total_revenue = $wpdb->get_var("SELECT SUM(revenue) FROM $stats_table");
        $total_sales = $wpdb->get_var("SELECT SUM(sold_count) FROM $stats_table");
        
        $stats = [
            'campaigns' => [
                'total' => intval($total_campaigns),
                'active' => intval($active_campaigns)
            ],
            'addons' => [
                'total' => intval($total_addons),
                'active' => intval($active_addons)
            ],
            'sales' => [
                'total_revenue' => floatval($total_revenue),
                'total_sales' => intval($total_sales)
            ]
        ];
        
        return rest_ensure_response($stats);
    }
    
    /**
     * Prepare campaign response
     */
    private function prepare_campaign_response($campaign) {
        return [
            'id' => $campaign->id,
            'name' => $campaign->name,
            'description' => $campaign->description,
            'type' => $campaign->type,
            'priority' => $campaign->priority,
            'status' => $campaign->status,
            'start_date' => $campaign->start_date,
            'end_date' => $campaign->end_date,
            'global_settings' => json_decode($campaign->global_settings, true),
            'targets' => $campaign->targets ?? [],
            'gifts' => $campaign->gifts ?? [],
            'rules' => $campaign->rules ?? [],
            'created_at' => $campaign->created_at,
            'updated_at' => $campaign->updated_at
        ];
    }
}
