<?php
/**
 * Addon Manager for FlashSale Core
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class FS_Addon_Manager {
    
    /**
     * Registered addons
     */
    private $addons = [];
    
    /**
     * Active addons
     */
    private $active_addons = [];
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
        $this->load_addons();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', [$this, 'register_addon_hooks']);
        add_action('flashsale_core_loaded', [$this, 'init_addons']);
    }
    
    /**
     * Register an addon
     */
    public function register_addon($addon_slug, $addon_data) {
        $this->addons[$addon_slug] = $addon_data;
        
        // Save to database
        $this->save_addon_to_db($addon_slug, $addon_data);
        
        do_action('fs_addon_registered', $addon_slug, $addon_data);
    }
    
    /**
     * Save addon to database
     */
    private function save_addon_to_db($addon_slug, $addon_data) {
        global $wpdb;
        
        $table = FS_Database::get_table_name('addons');
        
        $existing = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table WHERE addon_slug = %s",
            $addon_slug
        ));
        
        $data = [
            'addon_name' => $addon_data['name'],
            'addon_slug' => $addon_slug,
            'addon_version' => $addon_data['version'],
            'addon_type' => $addon_data['type'],
            'status' => isset($addon_data['status']) ? $addon_data['status'] : 1,
            'settings' => json_encode($addon_data['settings'] ?? []),
            'dependencies' => json_encode($addon_data['dependencies'] ?? []),
            'updated_at' => current_time('mysql')
        ];
        
        if ($existing) {
            $wpdb->update($table, $data, ['addon_slug' => $addon_slug]);
        } else {
            $data['activated_at'] = current_time('mysql');
            $wpdb->insert($table, $data);
        }
    }
    
    /**
     * Load addons from database
     */
    public function load_addons() {
        global $wpdb;
        
        $table = FS_Database::get_table_name('addons');
        
        $addons = $wpdb->get_results("SELECT * FROM $table WHERE status = 1");
        
        foreach ($addons as $addon) {
            $this->active_addons[$addon->addon_slug] = [
                'id' => $addon->id,
                'name' => $addon->addon_name,
                'version' => $addon->addon_version,
                'type' => $addon->addon_type,
                'settings' => json_decode($addon->settings, true),
                'dependencies' => json_decode($addon->dependencies, true)
            ];
        }
    }
    
    /**
     * Initialize addons
     */
    public function init_addons() {
        foreach ($this->active_addons as $slug => $addon) {
            $this->init_addon($slug, $addon);
        }
    }
    
    /**
     * Initialize single addon
     */
    private function init_addon($slug, $addon) {
        // Check dependencies
        if (!$this->check_addon_dependencies($addon['dependencies'])) {
            return false;
        }
        
        // Load addon hooks
        $this->load_addon_hooks($addon['id']);
        
        do_action('fs_addon_init', $slug, $addon);
        do_action("fs_addon_init_{$slug}", $addon);
        
        return true;
    }
    
    /**
     * Check addon dependencies
     */
    private function check_addon_dependencies($dependencies) {
        if (empty($dependencies)) {
            return true;
        }
        
        foreach ($dependencies as $dependency) {
            if (!$this->is_addon_active($dependency)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Load addon hooks
     */
    private function load_addon_hooks($addon_id) {
        global $wpdb;
        
        $table = FS_Database::get_table_name('addon_hooks');
        
        $hooks = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table WHERE addon_id = %d AND status = 1",
            $addon_id
        ));
        
        foreach ($hooks as $hook) {
            $this->register_hook($hook);
        }
    }
    
    /**
     * Register a hook
     */
    private function register_hook($hook) {
        $callback = [$hook->callback_class, $hook->callback_method];
        
        if ($hook->hook_type === 'action') {
            add_action($hook->hook_name, $callback, $hook->priority);
        } elseif ($hook->hook_type === 'filter') {
            add_filter($hook->hook_name, $callback, $hook->priority);
        }
    }
    
    /**
     * Register addon hook
     */
    public function register_addon_hook($addon_slug, $hook_data) {
        global $wpdb;
        
        $addon = $this->get_addon_by_slug($addon_slug);
        if (!$addon) {
            return false;
        }
        
        $table = FS_Database::get_table_name('addon_hooks');
        
        $hook_data = [
            'addon_id' => $addon['id'],
            'hook_name' => $hook_data['hook_name'],
            'hook_type' => $hook_data['hook_type'],
            'callback_class' => $hook_data['callback_class'],
            'callback_method' => $hook_data['callback_method'],
            'priority' => $hook_data['priority'] ?? 10,
            'status' => $hook_data['status'] ?? 1,
            'created_at' => current_time('mysql')
        ];
        
        return $wpdb->insert($table, $hook_data);
    }
    
    /**
     * Get addon by slug
     */
    public function get_addon_by_slug($slug) {
        return $this->active_addons[$slug] ?? null;
    }
    
    /**
     * Check if addon is active
     */
    public function is_addon_active($slug) {
        return isset($this->active_addons[$slug]);
    }
    
    /**
     * Activate addon
     */
    public function activate_addon($slug) {
        global $wpdb;
        
        $table = FS_Database::get_table_name('addons');
        
        $result = $wpdb->update(
            $table,
            ['status' => 1, 'activated_at' => current_time('mysql')],
            ['addon_slug' => $slug]
        );
        
        if ($result) {
            do_action('fs_addon_activated', $slug);
            return true;
        }
        
        return false;
    }
    
    /**
     * Deactivate addon
     */
    public function deactivate_addon($slug) {
        global $wpdb;
        
        $table = FS_Database::get_table_name('addons');
        
        $result = $wpdb->update(
            $table,
            ['status' => 0],
            ['addon_slug' => $slug]
        );
        
        if ($result) {
            unset($this->active_addons[$slug]);
            do_action('fs_addon_deactivated', $slug);
            return true;
        }
        
        return false;
    }
    
    /**
     * Get all addons
     */
    public function get_all_addons() {
        global $wpdb;
        
        $table = FS_Database::get_table_name('addons');
        
        $addons = $wpdb->get_results("SELECT * FROM $table ORDER BY addon_name");
        
        foreach ($addons as &$addon) {
            $addon->settings = json_decode($addon->settings, true);
            $addon->dependencies = json_decode($addon->dependencies, true);
        }
        
        return $addons;
    }
    
    /**
     * Get active addons
     */
    public function get_active_addons() {
        return $this->active_addons;
    }
    
    /**
     * Register addon hooks for WordPress
     */
    public function register_addon_hooks() {
        // This method will be called on init to register all addon hooks
        do_action('fs_register_addon_hooks');
    }
    
    /**
     * Update addon settings
     */
    public function update_addon_settings($slug, $settings) {
        global $wpdb;
        
        $table = FS_Database::get_table_name('addons');
        
        $result = $wpdb->update(
            $table,
            [
                'settings' => json_encode($settings),
                'updated_at' => current_time('mysql')
            ],
            ['addon_slug' => $slug]
        );
        
        if ($result !== false) {
            if (isset($this->active_addons[$slug])) {
                $this->active_addons[$slug]['settings'] = $settings;
            }
            
            do_action('fs_addon_settings_updated', $slug, $settings);
            return true;
        }
        
        return false;
    }
}
