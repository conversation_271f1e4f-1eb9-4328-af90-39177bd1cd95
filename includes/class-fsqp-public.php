<?php
/**
 * Public functionality for Quantity Promotion
 *
 * @package FlashSale_Quantity_Promotion
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class FSQP_Public {
    
    /**
     * Constructor
     */
    public function __construct() {
        // WooCommerce hooks - Use fees instead of price modification
        add_action('woocommerce_cart_calculate_fees', [$this, 'add_quantity_discount_fee']);
        // add_action('woocommerce_single_product_summary', [$this, 'display_quantity_promotion_info'], 26);

        // Display hooks for cart
        add_filter('woocommerce_cart_item_price', [$this, 'display_cart_item_discount'], 10, 3);
        add_action('woocommerce_review_order_before_order_total', [$this, 'display_discount_details_in_checkout']);
        add_action('woocommerce_cart_totals_before_order_total', [$this, 'display_discount_details_in_cart']);
        
        // Order hooks
        add_action('woocommerce_checkout_create_order_line_item', [$this, 'save_order_item_meta'], 10, 4);
        add_action('woocommerce_order_status_completed', [$this, 'log_promotion_usage']);
        
        // Frontend scripts
        add_action('wp_enqueue_scripts', [$this, 'enqueue_frontend_scripts']);
    }
    
    /**
     * Add quantity discount as fee
     */
    public function add_quantity_discount_fee() {
        if (is_admin() && !defined('DOING_AJAX')) {
            return;
        }

        if (!WC()->cart) {
            return;
        }

        $total_discount = 0;
        $discount_details = [];

        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            $product = $cart_item['data'];
            $quantity = $cart_item['quantity'];
            $product_id = $product->get_id();

            $original_price = $product->get_regular_price();
            $discounted_price = $this->calculate_quantity_discount($product_id, $quantity, $original_price);

            if ($discounted_price < $original_price) {
                $discount_per_item = $original_price - $discounted_price;
                // Apply the fixed discount amount, not multiplied by quantity
                $fixed_discount_amount = $discount_per_item;
                $total_discount += $fixed_discount_amount;

                // Store discount details for display
                $discount_details[] = [
                    'product_name' => $product->get_name(),
                    'quantity' => $quantity,
                    'discount_per_item' => $discount_per_item,
                    'total_discount' => $fixed_discount_amount
                ];

                // Store in cart item for order meta
                WC()->cart->cart_contents[$cart_item_key]['fsqp_original_price'] = $original_price;
                WC()->cart->cart_contents[$cart_item_key]['fsqp_discounted_price'] = $discounted_price;
                WC()->cart->cart_contents[$cart_item_key]['fsqp_discount_amount'] = $discount_per_item;
                WC()->cart->cart_contents[$cart_item_key]['fsqp_fixed_discount'] = $fixed_discount_amount;
            }
        }

        if ($total_discount > 0) {
            // Add negative fee (discount)
            WC()->cart->add_fee(
                __('Quantity Discount', 'flashsale-quantity-promotion'),
                -$total_discount,
                false // Not taxable
            );

            // Store discount details in session for display
            WC()->session->set('fsqp_discount_details', $discount_details);
        }
    }
    
    /**
     * Display cart item discount information
     */
    public function display_cart_item_discount($price_html, $cart_item, $cart_item_key) {
        // Check if this item has quantity discount applied
        if (isset($cart_item['fsqp_fixed_discount']) && $cart_item['fsqp_fixed_discount'] > 0) {
            $fixed_discount = $cart_item['fsqp_fixed_discount'];
            $discount_per_item = $cart_item['fsqp_discount_amount'];
            $quantity = $cart_item['quantity'];

            $discount_html = sprintf(
                '<br><small class="fsqp-discount-info" style="color: #27ae60;">%s %s (%s %s, %d %s)</small>',
                __('Quantity discount:', 'flashsale-quantity-promotion'),
                wc_price($fixed_discount),
                wc_price($discount_per_item),
                __('per item', 'flashsale-quantity-promotion'),
                $quantity,
                __('items', 'flashsale-quantity-promotion')
            );

            return $price_html . $discount_html;
        }

        return $price_html;
    }
    

    
    /**
     * Display quantity promotion info on single product page
     */
    public function display_quantity_promotion_info() {
        global $product;
        
        if (!$product) {
            return;
        }
        
        $product_id = $product->get_id();
        $database = new FSQP_Database();
        $rules = $database->get_product_quantity_rules($product_id);
        
        if (empty($rules)) {
            return;
        }
        
        echo '<div class="fsqp-quantity-promotion-info">';
        echo '<h4>' . __('Quantity Discounts Available', 'flashsale-quantity-promotion') . '</h4>';
        echo '<div class="fsqp-rules-table">';
        echo '<table>';
        echo '<thead>';
        echo '<tr>';
        echo '<th>' . __('Quantity', 'flashsale-quantity-promotion') . '</th>';
        echo '<th>' . __('Discount', 'flashsale-quantity-promotion') . '</th>';
        echo '<th>' . __('Price Each', 'flashsale-quantity-promotion') . '</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        $regular_price = $product->get_regular_price();
        
        foreach ($rules as $rule) {
            $quantity_range = $rule->min_quantity;
            if ($rule->max_quantity > 0) {
                $quantity_range .= ' - ' . $rule->max_quantity;
            } else {
                $quantity_range .= '+';
            }
            
            $discount_text = '';
            if ($rule->discount_type == 1) {
                $discount_text = $rule->discount_value . '%';
            } else {
                $discount_text = wc_price($rule->discount_value);
            }
            
            $discounted_price = $this->calculate_rule_discount($regular_price, $rule);
            
            echo '<tr>';
            echo '<td>' . $quantity_range . '</td>';
            echo '<td>' . $discount_text . '</td>';
            echo '<td>' . wc_price($discounted_price) . '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        echo '</div>';
    }
    
    /**
     * Calculate quantity discount for product
     */
    public function calculate_quantity_discount($product_id, $quantity, $original_price) {
        $database = new FSQP_Database();
        $rule = $database->get_applicable_rule($product_id, $quantity);
        
        if (!$rule) {
            return $original_price;
        }
        
        return $this->calculate_rule_discount($original_price, $rule);
    }
    
    /**
     * Calculate discount based on rule
     */
    public function calculate_rule_discount($original_price, $rule) {
        $discount_amount = 0;
        
        if ($rule->discount_type == 1) {
            // Percentage discount
            $discount_amount = ($original_price * $rule->discount_value) / 100;
        } else {
            // Fixed amount discount
            $discount_amount = $rule->discount_value;
        }
        
        // Apply max discount limit if set
        if ($rule->max_discount_amount > 0 && $discount_amount > $rule->max_discount_amount) {
            $discount_amount = $rule->max_discount_amount;
        }
        
        $discounted_price = $original_price - $discount_amount;
        
        // Ensure price doesn't go below 0
        return max(0, $discounted_price);
    }
    
    /**
     * Get cart item quantity for product
     */
    private function get_cart_item_quantity($product_id) {
        if (!WC()->cart) {
            return 0;
        }
        
        $quantity = 0;
        
        foreach (WC()->cart->get_cart() as $cart_item) {
            if ($cart_item['product_id'] == $product_id || $cart_item['variation_id'] == $product_id) {
                $quantity += $cart_item['quantity'];
            }
        }
        
        return $quantity;
    }
    
    /**
     * Save order item meta
     */
    public function save_order_item_meta($item, $cart_item_key, $values, $order) {
        if (isset($values['fsqp_original_price'])) {
            $item->add_meta_data('_fsqp_original_price', $values['fsqp_original_price']);
            $item->add_meta_data('_fsqp_discounted_price', $values['fsqp_discounted_price']);
            $item->add_meta_data('_fsqp_discount_amount', $values['fsqp_discount_amount']);
            $item->add_meta_data('_fsqp_fixed_discount', $values['fsqp_fixed_discount']);
            $item->add_meta_data('_fsqp_discount_applied', true);
        }
    }
    
    /**
     * Log promotion usage when order is completed
     */
    public function log_promotion_usage($order_id) {
        $order = wc_get_order($order_id);

        if (!$order) {
            return;
        }

        $database = new FSQP_Database();

        foreach ($order->get_items() as $item) {
            $original_price = $item->get_meta('_fsqp_original_price');
            $discounted_price = $item->get_meta('_fsqp_discounted_price');
            $discount_amount = $item->get_meta('_fsqp_discount_amount');
            $fixed_discount = $item->get_meta('_fsqp_fixed_discount');
            $discount_applied = $item->get_meta('_fsqp_discount_applied');

            if ($original_price && $discount_applied && $fixed_discount) {
                $product_id = $item->get_product_id();
                $quantity = $item->get_quantity();

                $rule = $database->get_applicable_rule($product_id, $quantity);

                if ($rule) {
                    $database->log_promotion_usage([
                        'order_id' => $order_id,
                        'product_id' => $product_id,
                        'campaign_id' => $rule->campaign_id,
                        'quantity' => $quantity,
                        'original_price' => $original_price,
                        'discounted_price' => $discounted_price,
                        'discount_amount' => $discount_amount,
                        'fixed_discount' => $fixed_discount,
                        'discount_type' => $rule->discount_type,
                        'min_quantity' => $rule->min_quantity,
                        'max_quantity' => $rule->max_quantity
                    ]);
                }
            }
        }
    }

    /**
     * Display discount details in checkout
     */
    public function display_discount_details_in_checkout() {
        $this->display_discount_details();
    }

    /**
     * Display discount details in cart
     */
    public function display_discount_details_in_cart() {
        $this->display_discount_details();
    }

    /**
     * Display discount details
     */
    private function display_discount_details() {
        if (!WC()->session) {
            return;
        }

        $discount_details = WC()->session->get('fsqp_discount_details');

        if (empty($discount_details)) {
            return;
        }

        echo '<tr class="fsqp-discount-details">';
        echo '<th colspan="2" style="text-align: left; padding: 10px 0; border-top: 1px solid #ddd;">';
        echo '<strong>' . __('Quantity Discount Details:', 'flashsale-quantity-promotion') . '</strong>';
        echo '</th>';
        echo '</tr>';

        foreach ($discount_details as $detail) {
            echo '<tr class="fsqp-discount-item">';
            echo '<td style="padding: 5px 0; color: #666;">';
            echo sprintf(
                '%s (x%d)',
                esc_html($detail['product_name']),
                $detail['quantity']
            );
            echo '</td>';
            echo '<td style="padding: 5px 0; text-align: right; color: #27ae60;">';
            echo sprintf(
                '-%s (%s %s)',
                wc_price($detail['total_discount']),
                wc_price($detail['discount_per_item']),
                __('discount per item', 'flashsale-quantity-promotion')
            );
            echo '</td>';
            echo '</tr>';
        }
    }

    /**
     * Enqueue frontend scripts
     */
    public function enqueue_frontend_scripts() {
        if (is_product()) {
            wp_enqueue_style(
                'fsqp-frontend-style',
                FSQP_PLUGIN_URL . 'public/css/frontend.css',
                [],
                FSQP_VERSION
            );
            
            wp_enqueue_script(
                'fsqp-frontend-script',
                FSQP_PLUGIN_URL . 'public/js/frontend.js',
                ['jquery'],
                FSQP_VERSION,
                true
            );
        }
    }
}
