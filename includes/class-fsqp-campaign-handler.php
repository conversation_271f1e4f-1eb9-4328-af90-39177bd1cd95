<?php
/**
 * Quantity Promotion Campaign Handler
 *
 * @package CanhcamPromotion_Quantity_Promotion
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class FSQP_Campaign_Handler {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Campaign processing hooks
        add_action('fs_process_campaign', [$this, 'process_quantity_promotion'], 10, 2);
        add_action('ccp_process_campaign', [$this, 'process_quantity_promotion'], 10, 2);

        // Campaign saving hooks - IMPORTANT for saving products
        add_action('fs_save_campaign', [$this, 'save_quantity_promotion_data'], 10, 3);
        add_action('ccp_save_campaign', [$this, 'save_quantity_promotion_data'], 10, 3);
        add_action('fs_campaign_created', [$this, 'handle_campaign_created'], 10, 2);
        add_action('fs_campaign_updated', [$this, 'handle_campaign_updated'], 10, 2);

        // Cart hooks for quantity-based discounts
        add_action('woocommerce_before_calculate_totals', [$this, 'apply_quantity_discounts'], 20);
        add_filter('woocommerce_cart_item_price', [$this, 'display_discounted_price'], 10, 3);

        // Product page hooks
        add_action('woocommerce_single_product_summary', [$this, 'display_quantity_pricing_table'], 25);
    }
    
    /**
     * Process quantity promotion campaign
     */
    public function process_quantity_promotion($campaign, $context = 'cart') {
        if ($campaign->type !== 'quantity-promotion') {
            return;
        }
        
        // Get campaign products and rules
        $database = new FSQP_Database();
        $rules = $database->get_product_quantity_rules($campaign->id, false);
        
        if (empty($rules)) {
            return;
        }
        
        // Process based on context
        switch ($context) {
            case 'cart':
                $this->process_cart_quantity_discounts($rules);
                break;
                
            case 'product':
                $this->process_product_quantity_display($rules);
                break;
        }
    }
    
    /**
     * Apply quantity discounts to cart
     */
    public function apply_quantity_discounts($cart) {
        if (is_admin() && !defined('DOING_AJAX')) {
            return;
        }
        
        if (did_action('woocommerce_before_calculate_totals') >= 2) {
            return;
        }
        
        // Get active quantity promotion campaigns
        $active_campaigns = $this->get_active_quantity_campaigns();
        
        if (empty($active_campaigns)) {
            return;
        }
        
        foreach ($cart->get_cart() as $cart_item_key => $cart_item) {
            $product_id = $cart_item['product_id'];
            $quantity = $cart_item['quantity'];
            
            // Find applicable discount for this product and quantity
            $discount = $this->find_applicable_discount($product_id, $quantity, $active_campaigns);
            
            if ($discount) {
                $this->apply_discount_to_cart_item($cart_item, $discount);
            }
        }
    }
    
    /**
     * Get active quantity promotion campaigns
     */
    private function get_active_quantity_campaigns() {
        if (!function_exists('FS') || !FS()->campaigns()) {
            return [];
        }
        
        $campaigns = FS()->campaigns()->get_active_campaigns();
        $quantity_campaigns = [];
        
        foreach ($campaigns as $campaign) {
            if ($campaign->type === 'quantity-promotion') {
                $quantity_campaigns[] = $campaign;
            }
        }
        
        return $quantity_campaigns;
    }
    
    /**
     * Find applicable discount for product and quantity
     */
    private function find_applicable_discount($product_id, $quantity, $campaigns) {
        $database = new FSQP_Database();
        $best_discount = null;
        $best_discount_value = 0;
        
        foreach ($campaigns as $campaign) {
            $rules = $database->get_product_quantity_rules($campaign->id, false);
            
            foreach ($rules as $rule) {
                if ($rule->product_id != $product_id) {
                    continue;
                }
                
                // Check if quantity falls within this rule's range
                if ($quantity >= $rule->min_quantity && 
                    ($rule->max_quantity == 0 || $quantity <= $rule->max_quantity)) {
                    
                    // Calculate discount value for comparison
                    $discount_value = $this->calculate_discount_value($rule, $quantity);
                    
                    if ($discount_value > $best_discount_value) {
                        $best_discount = $rule;
                        $best_discount_value = $discount_value;
                    }
                }
            }
        }
        
        return $best_discount;
    }
    
    /**
     * Calculate discount value for comparison
     */
    private function calculate_discount_value($rule, $quantity) {
        $product = wc_get_product($rule->product_id);
        if (!$product) {
            return 0;
        }
        
        $price = $product->get_price();
        
        if ($rule->discount_type == 1) { // Percentage
            $discount = ($price * $rule->discount_value / 100) * $quantity;
        } else { // Fixed amount
            $discount = $rule->discount_value * $quantity;
        }
        
        // Apply max discount limit if set
        if ($rule->max_discount_amount > 0) {
            $discount = min($discount, $rule->max_discount_amount * $quantity);
        }
        
        return $discount;
    }
    
    /**
     * Apply discount to cart item
     */
    private function apply_discount_to_cart_item($cart_item, $rule) {
        $product = $cart_item['data'];
        $original_price = $product->get_price();
        $quantity = $cart_item['quantity'];
        
        if ($rule->discount_type == 1) { // Percentage
            $discount_amount = $original_price * $rule->discount_value / 100;
        } else { // Fixed amount
            $discount_amount = $rule->discount_value;
        }
        
        // Apply max discount limit if set
        if ($rule->max_discount_amount > 0) {
            $discount_amount = min($discount_amount, $rule->max_discount_amount);
        }
        
        $new_price = max(0, $original_price - $discount_amount);
        $product->set_price($new_price);
        
        // Store discount info for display
        $cart_item['fsqp_discount'] = [
            'original_price' => $original_price,
            'discount_amount' => $discount_amount,
            'rule_id' => $rule->id,
            'discount_type' => $rule->discount_type,
            'discount_value' => $rule->discount_value
        ];
    }
    
    /**
     * Display discounted price in cart
     */
    public function display_discounted_price($price, $cart_item, $cart_item_key) {
        if (isset($cart_item['fsqp_discount'])) {
            $discount_info = $cart_item['fsqp_discount'];
            $original_price = wc_price($discount_info['original_price']);
            $savings = wc_price($discount_info['discount_amount']);
            
            if (get_option('fsqp_show_savings_message', 1)) {
                $price .= '<br><small class="fsqp-savings">' . 
                         sprintf(__('Save %s per item', 'canhcampromotion-quantity-promotion'), $savings) . 
                         '</small>';
            }
        }
        
        return $price;
    }
    
    /**
     * Display quantity pricing table on product page
     */
    public function display_quantity_pricing_table() {
        global $product;
        
        if (!$product) {
            return;
        }
        
        $product_id = $product->get_id();
        $rules = $this->get_product_quantity_rules($product_id);
        
        if (empty($rules)) {
            return;
        }
        
        // Display pricing table
        echo '<div class="fsqp-quantity-pricing-table">';
        echo '<h4>' . __('Quantity Discounts', 'canhcampromotion-quantity-promotion') . '</h4>';
        echo '<table class="fsqp-pricing-table">';
        echo '<thead><tr>';
        echo '<th>' . __('Quantity', 'canhcampromotion-quantity-promotion') . '</th>';
        echo '<th>' . __('Price per item', 'canhcampromotion-quantity-promotion') . '</th>';
        echo '<th>' . __('You save', 'canhcampromotion-quantity-promotion') . '</th>';
        echo '</tr></thead>';
        echo '<tbody>';

        foreach ($rules as $rule) {
            $this->display_pricing_row($rule, $product);
        }

        echo '</tbody></table>';
        echo '</div>';

        // Add inline CSS for the pricing table
        echo '<style>
        .fsqp-quantity-pricing-table {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #f9f9f9;
        }
        .fsqp-quantity-pricing-table h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
        }
        .fsqp-pricing-table {
            width: 100%;
            border-collapse: collapse;
            background: #fff;
            border-radius: 4px;
            overflow: hidden;
        }
        .fsqp-pricing-table th,
        .fsqp-pricing-table td {
            padding: 10px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .fsqp-pricing-table th {
            background: #f6f6f6;
            font-weight: 600;
            color: #333;
        }
        .fsqp-pricing-table tr:last-child td {
            border-bottom: none;
        }
        .fsqp-savings {
            color: #27ae60;
            font-weight: 600;
        }
        </style>';
    }
    
    /**
     * Display pricing table row
     */
    private function display_pricing_row($rule, $product) {
        $original_price = $product->get_price();
        
        if ($rule->discount_type == 1) { // Percentage
            $discount_amount = $original_price * $rule->discount_value / 100;
        } else { // Fixed amount
            $discount_amount = $rule->discount_value;
        }
        
        // Apply max discount limit if set
        if ($rule->max_discount_amount > 0) {
            $discount_amount = min($discount_amount, $rule->max_discount_amount);
        }
        
        $discounted_price = max(0, $original_price - $discount_amount);
        
        $quantity_text = $rule->min_quantity;
        if ($rule->max_quantity > 0) {
            $quantity_text .= ' - ' . $rule->max_quantity;
        } else {
            $quantity_text .= '+';
        }
        
        echo '<tr>';
        echo '<td>' . $quantity_text . '</td>';
        echo '<td>' . wc_price($discounted_price) . '</td>';
        echo '<td>' . wc_price($discount_amount) . '</td>';
        echo '</tr>';
    }
    
    /**
     * Get quantity rules for a product
     */
    private function get_product_quantity_rules($product_id) {
        $database = new FSQP_Database();
        $active_campaigns = $this->get_active_quantity_campaigns();
        $rules = [];
        
        foreach ($active_campaigns as $campaign) {
            $campaign_rules = $database->get_product_quantity_rules($campaign->id, false);
            
            foreach ($campaign_rules as $rule) {
                if ($rule->product_id == $product_id) {
                    $rules[] = $rule;
                }
            }
        }
        
        // Sort by min_quantity
        usort($rules, function($a, $b) {
            return $a->min_quantity - $b->min_quantity;
        });

        return $rules;
    }

    /**
     * Handle saving quantity promotion data when campaign is saved
     */
    public function save_quantity_promotion_data($campaign_id, $campaign_data, $form_data = null) {
        error_log("FSQP: save_quantity_promotion_data called - Campaign ID: $campaign_id");
        error_log("FSQP: Campaign type: " . ($campaign_data['type'] ?? 'not set'));

        // Only handle quantity-promotion campaigns
        if (!isset($campaign_data['type']) || $campaign_data['type'] !== 'quantity-promotion') {
            error_log("FSQP: Not a quantity-promotion campaign, skipping");
            return;
        }

        // IMPORTANT: Don't interfere with flash-sale campaigns
        if ($campaign_data['type'] === 'flash-sale') {
            error_log("FSQP: Flash-sale campaign detected, skipping to avoid conflicts");
            return;
        }

        // Get products data from form submission
        $products_data = [];
        if ($form_data && isset($form_data['products'])) {
            $products_data = $form_data['products'];
            error_log("FSQP: Found products in form_data: " . count($products_data));
        } elseif (isset($_POST['products'])) {
            $products_data = $_POST['products'];
            error_log("FSQP: Found products in _POST: " . count($products_data));
        } else {
            error_log("FSQP: No products data found in form_data or _POST");
        }

        if (!empty($products_data)) {
            error_log("FSQP: Calling save_quantity_rules with " . count($products_data) . " products");
            $this->save_quantity_rules($campaign_id, $products_data);
        } else {
            error_log("FSQP: No products data to save");
        }
    }

    /**
     * Handle campaign created event
     */
    public function handle_campaign_created($campaign_id, $campaign_data) {
        if (isset($campaign_data['type']) && $campaign_data['type'] === 'quantity-promotion') {
            // Additional processing for new quantity promotion campaigns
            do_action('fsqp_campaign_created', $campaign_id, $campaign_data);
        }
    }

    /**
     * Handle campaign updated event
     */
    public function handle_campaign_updated($campaign_id, $campaign_data) {
        if (isset($campaign_data['type']) && $campaign_data['type'] === 'quantity-promotion') {
            // Additional processing for updated quantity promotion campaigns
            do_action('fsqp_campaign_updated', $campaign_id, $campaign_data);
        }
    }

    /**
     * Save quantity rules for campaign
     */
    private function save_quantity_rules($campaign_id, $products_data) {
        global $wpdb;

        error_log("FSQP: save_quantity_rules called for campaign $campaign_id with " . count($products_data) . " products");

        $database = new FSQP_Database();
        $products_table = $wpdb->prefix . 'fs_campaign_products';

        // Delete existing products for this campaign
        $deleted = $wpdb->delete($products_table, ['promotion_id' => $campaign_id]);
        error_log("FSQP: Deleted $deleted existing records for campaign $campaign_id");

        // Save new products and ranges
        $total_saved = 0;
        foreach ($products_data as $product_id => $product_data) {
            error_log("FSQP: Processing product $product_id");
            error_log("FSQP: Product data: " . print_r($product_data, true));

            // Skip if no product_id or ranges
            if (empty($product_id) || !isset($product_data['ranges']) || empty($product_data['ranges'])) {
                error_log("FSQP: Skipping product $product_id - no ranges or empty product_id");
                continue;
            }

            $product = wc_get_product($product_id);
            if (!$product) {
                error_log("FSQP: Product $product_id not found in WooCommerce");
                continue;
            }

            error_log("FSQP: Product $product_id found: " . $product->get_name());

            // Save each quantity range as a separate record
            foreach ($product_data['ranges'] as $range_index => $range_data) {
                error_log("FSQP: Processing range $range_index: " . print_r($range_data, true));

                // Skip empty ranges
                if (empty($range_data['min_quantity']) || empty($range_data['discount_value'])) {
                    error_log("FSQP: Skipping range $range_index - empty min_quantity or discount_value");
                    continue;
                }

                $rule_data = [
                    'campaign_id' => $campaign_id,
                    'product_id' => $product_id,
                    'regular_price' => $product->get_regular_price(),
                    'sku' => $product->get_sku(),
                    'min_quantity' => intval($range_data['min_quantity']),
                    'max_quantity' => intval($range_data['max_quantity'] ?? 0),
                    'discount_type' => intval($range_data['discount_type'] ?? 1),
                    'discount_value' => floatval($range_data['discount_value']),
                    'max_discount_amount' => floatval($range_data['max_discount_amount'] ?? 0)
                ];

                error_log("FSQP: Saving rule data: " . print_r($rule_data, true));

                $result = $database->save_quantity_rule($rule_data);

                // Log for debugging
                if (!$result) {
                    error_log("FSQP: Failed to save quantity rule for product {$product_id}, range {$range_index}");
                    error_log("FSQP: Database error: " . $wpdb->last_error);
                } else {
                    error_log("FSQP: Successfully saved quantity rule for product {$product_id}, range {$range_index}, rule ID: {$result}");
                    $total_saved++;
                }
            }
        }

        error_log("FSQP: Total rules saved: $total_saved");
    }
}
