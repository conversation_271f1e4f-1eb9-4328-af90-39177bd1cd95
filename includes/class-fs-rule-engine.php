<?php
/**
 * Rule Engine for FlashSale Core
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class FS_Rule_Engine {
    
    /**
     * Registered rule types
     */
    private $rule_types = [];
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_default_rules();
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', [$this, 'register_rule_types']);
    }
    
    /**
     * Initialize default rule types
     */
    private function init_default_rules() {
        $this->rule_types = [
            'product_rule' => [
                'label' => __('Product Rule', 'flashsale-core'),
                'description' => __('Rules for specific products', 'flashsale-core'),
                'conditions' => ['product_id', 'product_category', 'product_tag'],
                'actions' => ['discount_percentage', 'discount_fixed', 'free_shipping']
            ],
            'cart_rule' => [
                'label' => __('Cart Rule', 'flashsale-core'),
                'description' => __('Rules based on cart contents', 'flashsale-core'),
                'conditions' => ['cart_total', 'cart_quantity', 'cart_weight'],
                'actions' => ['cart_discount', 'free_shipping', 'add_gift']
            ],
            'user_rule' => [
                'label' => __('User Rule', 'flashsale-core'),
                'description' => __('Rules based on user properties', 'flashsale-core'),
                'conditions' => ['user_role', 'user_email', 'user_registration_date'],
                'actions' => ['discount_percentage', 'discount_fixed', 'exclusive_access']
            ],
            'time_rule' => [
                'label' => __('Time Rule', 'flashsale-core'),
                'description' => __('Time-based rules', 'flashsale-core'),
                'conditions' => ['date_range', 'time_range', 'day_of_week'],
                'actions' => ['activate_campaign', 'deactivate_campaign', 'send_notification']
            ]
        ];
    }
    
    /**
     * Register rule types
     */
    public function register_rule_types() {
        $this->rule_types = apply_filters('fs_rule_types', $this->rule_types);
    }
    
    /**
     * Register a new rule type
     */
    public function register_rule_type($type, $config) {
        $this->rule_types[$type] = $config;
    }
    
    /**
     * Get all rule types
     */
    public function get_rule_types() {
        return $this->rule_types;
    }
    
    /**
     * Get rule type
     */
    public function get_rule_type($type) {
        return $this->rule_types[$type] ?? null;
    }
    
    /**
     * Evaluate rule conditions
     */
    public function evaluate_conditions($conditions, $context = []) {
        if (empty($conditions)) {
            return true;
        }
        
        foreach ($conditions as $condition) {
            if (!$this->evaluate_condition($condition, $context)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Evaluate single condition
     */
    private function evaluate_condition($condition, $context) {
        $type = $condition['type'] ?? '';
        $operator = $condition['operator'] ?? 'equals';
        $value = $condition['value'] ?? '';
        
        switch ($type) {
            case 'product_id':
                return $this->evaluate_product_id($operator, $value, $context);
                
            case 'product_category':
                return $this->evaluate_product_category($operator, $value, $context);
                
            case 'cart_total':
                return $this->evaluate_cart_total($operator, $value, $context);
                
            case 'cart_quantity':
                return $this->evaluate_cart_quantity($operator, $value, $context);
                
            case 'user_role':
                return $this->evaluate_user_role($operator, $value, $context);
                
            case 'date_range':
                return $this->evaluate_date_range($operator, $value, $context);
                
            default:
                return apply_filters('fs_evaluate_condition', false, $condition, $context);
        }
    }
    
    /**
     * Evaluate product ID condition
     */
    private function evaluate_product_id($operator, $value, $context) {
        $product_id = $context['product_id'] ?? 0;
        
        switch ($operator) {
            case 'equals':
                return $product_id == $value;
            case 'not_equals':
                return $product_id != $value;
            case 'in':
                return in_array($product_id, (array) $value);
            case 'not_in':
                return !in_array($product_id, (array) $value);
            default:
                return false;
        }
    }
    
    /**
     * Evaluate product category condition
     */
    private function evaluate_product_category($operator, $value, $context) {
        $product_id = $context['product_id'] ?? 0;
        
        if (!$product_id) {
            return false;
        }
        
        $product_categories = wp_get_post_terms($product_id, 'product_cat', ['fields' => 'ids']);
        
        switch ($operator) {
            case 'in':
                return !empty(array_intersect($product_categories, (array) $value));
            case 'not_in':
                return empty(array_intersect($product_categories, (array) $value));
            default:
                return false;
        }
    }
    
    /**
     * Evaluate cart total condition
     */
    private function evaluate_cart_total($operator, $value, $context) {
        $cart_total = $context['cart_total'] ?? WC()->cart->get_total('edit');
        
        switch ($operator) {
            case 'greater_than':
                return $cart_total > $value;
            case 'greater_than_or_equal':
                return $cart_total >= $value;
            case 'less_than':
                return $cart_total < $value;
            case 'less_than_or_equal':
                return $cart_total <= $value;
            case 'equals':
                return $cart_total == $value;
            default:
                return false;
        }
    }
    
    /**
     * Evaluate cart quantity condition
     */
    private function evaluate_cart_quantity($operator, $value, $context) {
        $cart_quantity = $context['cart_quantity'] ?? WC()->cart->get_cart_contents_count();
        
        switch ($operator) {
            case 'greater_than':
                return $cart_quantity > $value;
            case 'greater_than_or_equal':
                return $cart_quantity >= $value;
            case 'less_than':
                return $cart_quantity < $value;
            case 'less_than_or_equal':
                return $cart_quantity <= $value;
            case 'equals':
                return $cart_quantity == $value;
            default:
                return false;
        }
    }
    
    /**
     * Evaluate user role condition
     */
    private function evaluate_user_role($operator, $value, $context) {
        $user = wp_get_current_user();
        $user_roles = $user->roles ?? [];
        
        switch ($operator) {
            case 'in':
                return !empty(array_intersect($user_roles, (array) $value));
            case 'not_in':
                return empty(array_intersect($user_roles, (array) $value));
            default:
                return false;
        }
    }
    
    /**
     * Evaluate date range condition
     */
    private function evaluate_date_range($operator, $value, $context) {
        $current_time = current_time('timestamp');
        
        if (isset($value['start']) && isset($value['end'])) {
            $start_time = strtotime($value['start']);
            $end_time = strtotime($value['end']);
            
            switch ($operator) {
                case 'between':
                    return $current_time >= $start_time && $current_time <= $end_time;
                case 'not_between':
                    return $current_time < $start_time || $current_time > $end_time;
                default:
                    return false;
            }
        }
        
        return false;
    }
    
    /**
     * Execute rule actions
     */
    public function execute_actions($actions, $context = []) {
        $results = [];
        
        foreach ($actions as $action) {
            $result = $this->execute_action($action, $context);
            $results[] = $result;
        }
        
        return $results;
    }
    
    /**
     * Execute single action
     */
    private function execute_action($action, $context) {
        $type = $action['type'] ?? '';
        $value = $action['value'] ?? '';
        
        switch ($type) {
            case 'discount_percentage':
                return $this->apply_percentage_discount($value, $context);
                
            case 'discount_fixed':
                return $this->apply_fixed_discount($value, $context);
                
            case 'free_shipping':
                return $this->apply_free_shipping($context);
                
            case 'add_gift':
                return $this->add_gift_to_cart($value, $context);
                
            default:
                return apply_filters('fs_execute_action', null, $action, $context);
        }
    }
    
    /**
     * Apply percentage discount
     */
    private function apply_percentage_discount($percentage, $context) {
        // Implementation for percentage discount
        return [
            'type' => 'discount_percentage',
            'value' => $percentage,
            'applied' => true
        ];
    }
    
    /**
     * Apply fixed discount
     */
    private function apply_fixed_discount($amount, $context) {
        // Implementation for fixed discount
        return [
            'type' => 'discount_fixed',
            'value' => $amount,
            'applied' => true
        ];
    }
    
    /**
     * Apply free shipping
     */
    private function apply_free_shipping($context) {
        // Implementation for free shipping
        return [
            'type' => 'free_shipping',
            'applied' => true
        ];
    }
    
    /**
     * Add gift to cart
     */
    private function add_gift_to_cart($gift_id, $context) {
        // Implementation for adding gift to cart
        return [
            'type' => 'add_gift',
            'gift_id' => $gift_id,
            'applied' => true
        ];
    }
}
