<?php
/**
 * Admin functionality for Quantity Promotion
 *
 * @package FlashSale_Quantity_Promotion
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class FSQP_Admin {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_scripts']);
        add_action('wp_ajax_fsqp_save_campaign', [$this, 'ajax_save_campaign']);
        add_action('wp_ajax_fsqp_delete_rule', [$this, 'ajax_delete_rule']);
        add_action('wp_ajax_fsqp_get_product_rules', [$this, 'ajax_get_product_rules']);
        add_action('wp_ajax_fsqp_search_products', [$this, 'ajax_search_products']);
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'flashsale') === false) {
            return;
        }
        
        wp_enqueue_style(
            'fsqp-admin-style',
            FSQP_PLUGIN_URL . 'admin/css/admin.css',
            [],
            FSQP_VERSION
        );

        wp_enqueue_style(
            'fsqp-horizontal-style',
            FSQP_PLUGIN_URL . 'admin/css/quantity-promotion-horizontal.css',
            ['fsqp-admin-style'],
            FSQP_VERSION
        );
        
        wp_enqueue_script(
            'fsqp-admin-script',
            FSQP_PLUGIN_URL . 'admin/js/admin.js',
            ['jquery'],
            FSQP_VERSION,
            true
        );
        
        wp_localize_script('fsqp-admin-script', 'fsqp_admin', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('fsqp_admin_nonce'),
            'strings' => [
                'confirm_delete' => __('Are you sure you want to delete this rule?', 'flashsale-quantity-promotion'),
                'saving' => __('Saving...', 'flashsale-quantity-promotion'),
                'saved' => __('Saved!', 'flashsale-quantity-promotion'),
                'error' => __('Error occurred!', 'flashsale-quantity-promotion'),
                'add_rule' => __('Add Rule', 'flashsale-quantity-promotion'),
                'min_quantity' => __('Min Quantity', 'flashsale-quantity-promotion'),
                'max_quantity' => __('Max Quantity', 'flashsale-quantity-promotion'),
                'discount_type' => __('Discount Type', 'flashsale-quantity-promotion'),
                'discount_value' => __('Discount Value', 'flashsale-quantity-promotion'),
                'percentage' => __('Percentage (%)', 'flashsale-quantity-promotion'),
                'fixed_amount' => __('Fixed Amount', 'flashsale-quantity-promotion'),
                'max_discount' => __('Max Discount Amount', 'flashsale-quantity-promotion'),
                'actions' => __('Actions', 'flashsale-quantity-promotion')
            ]
        ]);
    }
    
    /**
     * AJAX: Save campaign
     */
    public function ajax_save_campaign() {
        check_ajax_referer('fsqp_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Insufficient permissions', 'flashsale-quantity-promotion')]);
        }
        
        try {
            $campaign_data = $this->sanitize_campaign_data($_POST);
            $rules_data = $this->sanitize_rules_data($_POST['products'] ?? []);

            // Validate data
            $validation = $this->validate_campaign_data($campaign_data, $rules_data);
            if (!$validation['valid']) {
                wp_send_json_error(['message' => $validation['message']]);
            }

            // Save campaign using FlashSale Core
            $campaign_id = $this->save_campaign($campaign_data);

            if (!$campaign_id) {
                wp_send_json_error(['message' => __('Failed to save campaign', 'flashsale-quantity-promotion')]);
            }

            // Save quantity rules
            $this->save_quantity_rules($campaign_id, $rules_data);

            wp_send_json_success([
                'message' => __('Campaign saved successfully!', 'flashsale-quantity-promotion'),
                'campaign_id' => $campaign_id
            ]);

        } catch (Exception $e) {
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }
    
    /**
     * AJAX: Delete rule
     */
    public function ajax_delete_rule() {
        check_ajax_referer('fsqp_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Insufficient permissions', 'flashsale-quantity-promotion')]);
        }
        
        $rule_id = intval($_POST['rule_id']);
        
        if (!$rule_id) {
            wp_send_json_error(['message' => __('Invalid rule ID', 'flashsale-quantity-promotion')]);
        }
        
        $database = new FSQP_Database();
        $result = $database->delete_quantity_rule($rule_id);
        
        if ($result) {
            wp_send_json_success(['message' => __('Rule deleted successfully!', 'flashsale-quantity-promotion')]);
        } else {
            wp_send_json_error(['message' => __('Failed to delete rule', 'flashsale-quantity-promotion')]);
        }
    }
    
    /**
     * AJAX: Get product rules
     */
    public function ajax_get_product_rules() {
        check_ajax_referer('fsqp_admin_nonce', 'nonce');

        $product_id = intval($_POST['product_id']);

        if (!$product_id) {
            wp_send_json_error(['message' => __('Invalid product ID', 'flashsale-quantity-promotion')]);
        }

        $database = new FSQP_Database();
        $rules = $database->get_product_quantity_rules($product_id);

        wp_send_json_success(['rules' => $rules]);
    }

    /**
     * AJAX: Search products
     */
    public function ajax_search_products() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'fsqp_search_products')) {
            wp_send_json_error(['message' => __('Security check failed', 'flashsale-quantity-promotion')]);
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Insufficient permissions', 'flashsale-quantity-promotion')]);
        }

        $query = sanitize_text_field($_POST['query'] ?? '');

        if (strlen($query) < 2) {
            wp_send_json_error(['message' => __('Query too short', 'flashsale-quantity-promotion')]);
        }

        $products = $this->search_woocommerce_products($query);

        wp_send_json_success($products);
    }

    /**
     * Search WooCommerce products
     */
    private function search_woocommerce_products($query) {
        $args = [
            'post_type' => 'product',
            'post_status' => 'publish',
            'posts_per_page' => 20,
            'meta_query' => [
                [
                    'key' => '_visibility',
                    'value' => ['hidden', 'search'],
                    'compare' => 'NOT IN'
                ]
            ]
        ];

        // Search by ID if query is numeric
        if (is_numeric($query)) {
            $args['p'] = intval($query);
        } else {
            // Search by title, content, or SKU
            $args['s'] = $query;

            // Also search by SKU
            $args['meta_query'][] = [
                'key' => '_sku',
                'value' => $query,
                'compare' => 'LIKE'
            ];

            $args['meta_query']['relation'] = 'OR';
        }

        $products_query = new WP_Query($args);
        $products = [];

        if ($products_query->have_posts()) {
            while ($products_query->have_posts()) {
                $products_query->the_post();
                $product = wc_get_product(get_the_ID());

                if ($product) {
                    $products[] = [
                        'id' => $product->get_id(),
                        'name' => $product->get_name(),
                        'sku' => $product->get_sku(),
                        'price' => $product->get_regular_price(),
                        'type' => $product->get_type(),
                        'status' => $product->get_status()
                    ];
                }
            }
            wp_reset_postdata();
        }

        return $products;
    }
    
    /**
     * Sanitize campaign data
     */
    private function sanitize_campaign_data($post_data) {
        return [
            'id' => isset($post_data['campaign_id']) ? intval($post_data['campaign_id']) : null,
            'name' => sanitize_text_field($post_data['campaign_name'] ?? ''),
            'description' => sanitize_textarea_field($post_data['campaign_description'] ?? ''),
            'type' => 'quantity-promotion',
            'priority' => intval($post_data['campaign_priority'] ?? 5),
            'status' => intval($post_data['campaign_status'] ?? 1),
            'start_date' => sanitize_text_field($post_data['start_date'] ?? ''),
            'end_date' => sanitize_text_field($post_data['end_date'] ?? ''),
            'global_settings' => wp_json_encode([
                'apply_to_variations' => isset($post_data['apply_to_variations']),
                'combine_with_other_discounts' => isset($post_data['combine_with_other_discounts']),
                'show_savings_message' => isset($post_data['show_savings_message'])
            ])
        ];
    }
    
    /**
     * Sanitize rules data - Updated for new structure with multiple ranges per product
     */
    private function sanitize_rules_data($products_data) {
        $sanitized = [];

        if (!is_array($products_data)) {
            return $sanitized;
        }

        foreach ($products_data as $product_id => $product_data) {
            if (!isset($product_data['ranges']) || !is_array($product_data['ranges'])) {
                continue;
            }

            foreach ($product_data['ranges'] as $range_data) {
                $sanitized[] = [
                    'id' => isset($range_data['id']) ? intval($range_data['id']) : null,
                    'product_id' => intval($product_id),
                    'min_quantity' => intval($range_data['min_quantity'] ?? 1),
                    'max_quantity' => intval($range_data['max_quantity'] ?? 0),
                    'discount_type' => intval($range_data['discount_type'] ?? 1),
                    'discount_value' => floatval($range_data['discount_value'] ?? 0),
                    'max_discount_amount' => floatval($range_data['max_discount_amount'] ?? 0),
                    'priority' => intval($range_data['priority'] ?? 0)
                ];
            }
        }

        return $sanitized;
    }
    
    /**
     * Validate campaign data
     */
    private function validate_campaign_data($campaign_data, $rules_data) {
        if (empty($campaign_data['name'])) {
            return [
                'valid' => false,
                'message' => __('Campaign name is required', 'flashsale-quantity-promotion')
            ];
        }
        
        if (empty($rules_data)) {
            return [
                'valid' => false,
                'message' => __('At least one quantity rule is required', 'flashsale-quantity-promotion')
            ];
        }
        
        // Validate each rule
        foreach ($rules_data as $rule) {
            if (empty($rule['product_id'])) {
                return [
                    'valid' => false,
                    'message' => __('Product is required for all rules', 'flashsale-quantity-promotion')
                ];
            }
            
            if ($rule['min_quantity'] < 1) {
                return [
                    'valid' => false,
                    'message' => __('Minimum quantity must be at least 1', 'flashsale-quantity-promotion')
                ];
            }
            
            if ($rule['max_quantity'] > 0 && $rule['max_quantity'] < $rule['min_quantity']) {
                return [
                    'valid' => false,
                    'message' => __('Maximum quantity must be greater than minimum quantity', 'flashsale-quantity-promotion')
                ];
            }
            
            if ($rule['discount_value'] <= 0) {
                return [
                    'valid' => false,
                    'message' => __('Discount value must be greater than 0', 'flashsale-quantity-promotion')
                ];
            }
        }
        
        return ['valid' => true];
    }
    
    /**
     * Save campaign using FlashSale Core
     */
    private function save_campaign($campaign_data) {
        // Use FlashSale Core campaign manager
        if (class_exists('FS_Campaign_Manager')) {
            $campaign_manager = new FS_Campaign_Manager();

            if (isset($campaign_data['id']) && $campaign_data['id']) {
                // Update existing campaign
                return $campaign_manager->update_campaign($campaign_data['id'], $campaign_data);
            } else {
                // Create new campaign
                return $campaign_manager->create_campaign($campaign_data);
            }
        }

        throw new Exception(__('FlashSale Core not available', 'flashsale-quantity-promotion'));
    }

    /**
     * Save quantity rules using FlashSale Core structure
     */
    private function save_quantity_rules($campaign_id, $rules_data) {
        $database = new FSQP_Database();

        // First, delete existing rules for this campaign
        global $wpdb;
        $products_table = $wpdb->prefix . 'fs_campaign_products';
        $wpdb->delete($products_table, ['promotion_id' => $campaign_id]);

        // Then save new rules
        foreach ($rules_data as $rule) {
            $rule['campaign_id'] = $campaign_id;

            // Get product info for regular_price
            $product = wc_get_product($rule['product_id']);
            if ($product) {
                $rule['regular_price'] = $product->get_regular_price();
                $rule['sku'] = $product->get_sku();
            }

            $database->save_quantity_rule($rule);
        }
    }
}
