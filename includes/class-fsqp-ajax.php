<?php
/**
 * AJAX functionality for Quantity Promotion
 *
 * @package FlashSale_Quantity_Promotion
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class FSQP_Ajax {
    
    /**
     * Constructor
     */
    public function __construct() {
        // Admin AJAX
        add_action('wp_ajax_fsqp_search_products', [$this, 'search_products']);
        add_action('wp_ajax_fsqp_get_product_info', [$this, 'get_product_info']);
        add_action('wp_ajax_fsqp_validate_rules', [$this, 'validate_rules']);
        add_action('wp_ajax_fsqp_check_product_conflicts', [$this, 'check_product_conflicts']);

        // Frontend AJAX
        add_action('wp_ajax_fsqp_calculate_quantity_price', [$this, 'calculate_quantity_price']);
        add_action('wp_ajax_nopriv_fsqp_calculate_quantity_price', [$this, 'calculate_quantity_price']);
        add_action('wp_ajax_fsqp_get_quantity_discounts', [$this, 'get_quantity_discounts']);
        add_action('wp_ajax_nopriv_fsqp_get_quantity_discounts', [$this, 'get_quantity_discounts']);

        // Hook into FlashSale Core AJAX for campaign form
        add_action('wp_ajax_fs_search_products', [$this, 'handle_fs_search_products'], 5);

        // Hook into FlashSale Core AJAX for admin actions
        add_action('wp_ajax_fs_admin_action', [$this, 'handle_fs_admin_action'], 5);
    }
    
    /**
     * AJAX: Search products
     */
    public function search_products() {
        check_ajax_referer('fsqp_search_products', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Insufficient permissions', 'flashsale-quantity-promotion')]);
        }
        
        $query = sanitize_text_field($_POST['query'] ?? '');
        $category_id = intval($_POST['category_id'] ?? 0);
        
        $args = [
            'post_type' => ['product', 'product_variation'],
            'post_status' => 'publish',
            'posts_per_page' => 20,
            'meta_query' => [
                [
                    'key' => '_stock_status',
                    'value' => 'instock'
                ]
            ]
        ];
        
        if (!empty($query)) {
            $args['s'] = $query;
        }
        
        if ($category_id > 0) {
            $args['tax_query'] = [
                [
                    'taxonomy' => 'product_cat',
                    'field' => 'term_id',
                    'terms' => $category_id
                ]
            ];
        }
        
        $products = get_posts($args);
        $results = [];
        
        foreach ($products as $product_post) {
            $product = wc_get_product($product_post->ID);
            if (!$product) continue;
            
            $parent_id = $product->get_parent_id();
            $is_variation = $parent_id > 0;
            
            // Enhanced name for variations
            $display_name = $product->get_name();
            if ($is_variation) {
                $variation_attributes = $this->format_variation_attributes($product);
                if ($variation_attributes) {
                    $display_name .= ' - ' . $variation_attributes;
                }
            }
            
            $results[] = [
                'id' => $product->get_id(),
                'name' => $display_name,
                'sku' => $product->get_sku(),
                'price' => $product->get_regular_price(),
                'price_html' => wc_price($product->get_regular_price()),
                'type' => $product->get_type(),
                'stock_status' => $product->get_stock_status(),
                'is_variation' => $is_variation,
                'parent_id' => $parent_id
            ];
        }
        
        wp_send_json_success($results);
    }
    
    /**
     * AJAX: Get product info
     */
    public function get_product_info() {
        check_ajax_referer('fsqp_get_product_info', 'nonce');
        
        $product_id = intval($_POST['product_id'] ?? 0);
        
        if (!$product_id) {
            wp_send_json_error(['message' => __('Invalid product ID', 'flashsale-quantity-promotion')]);
        }
        
        $product = wc_get_product($product_id);
        
        if (!$product) {
            wp_send_json_error(['message' => __('Product not found', 'flashsale-quantity-promotion')]);
        }
        
        $database = new FSQP_Database();
        $existing_rules = $database->get_product_quantity_rules($product_id);
        
        $image_id = get_post_thumbnail_id($product->get_id());
        $image_url = $image_id ? wp_get_attachment_image_src($image_id, 'thumbnail')[0] : '';
        
        $parent_id = $product->get_parent_id();
        $is_variation = $parent_id > 0;
        
        // Enhanced name for variations
        $display_name = $product->get_name();
        if ($is_variation) {
            $variation_attributes = $this->format_variation_attributes($product);
            if ($variation_attributes) {
                $display_name .= ' - ' . $variation_attributes;
            }
        }

        $product_info = [
            'id' => $product->get_id(),
            'name' => $display_name,
            'sku' => $product->get_sku(),
            'price' => wc_price($product->get_regular_price()),
            'price_raw' => $product->get_regular_price(),
            'type' => $product->get_type(),
            'in_stock' => $product->is_in_stock(),
            'image_url' => $image_url,
            'existing_rules' => $existing_rules,
            'is_variation' => $is_variation,
            'parent_id' => $parent_id
        ];

        wp_send_json_success($product_info);
    }
    
    /**
     * AJAX: Validate rules
     */
    public function validate_rules() {
        check_ajax_referer('fsqp_validate_rules', 'nonce');
        
        $rules = $_POST['rules'] ?? [];
        $errors = [];
        
        foreach ($rules as $index => $rule) {
            $min_qty = intval($rule['min_quantity'] ?? 0);
            $max_qty = intval($rule['max_quantity'] ?? 0);
            $discount_value = floatval($rule['discount_value'] ?? 0);
            
            if ($min_qty < 1) {
                $errors[] = sprintf(
                    __('Rule %d: Minimum quantity must be at least 1', 'flashsale-quantity-promotion'),
                    $index + 1
                );
            }
            
            if ($max_qty > 0 && $max_qty < $min_qty) {
                $errors[] = sprintf(
                    __('Rule %d: Maximum quantity must be greater than minimum quantity', 'flashsale-quantity-promotion'),
                    $index + 1
                );
            }
            
            if ($discount_value <= 0) {
                $errors[] = sprintf(
                    __('Rule %d: Discount value must be greater than 0', 'flashsale-quantity-promotion'),
                    $index + 1
                );
            }
            
            // Check for overlapping ranges
            foreach ($rules as $other_index => $other_rule) {
                if ($index === $other_index) continue;
                
                $other_min = intval($other_rule['min_quantity'] ?? 0);
                $other_max = intval($other_rule['max_quantity'] ?? 0);
                
                if ($this->ranges_overlap($min_qty, $max_qty, $other_min, $other_max)) {
                    $errors[] = sprintf(
                        __('Rule %d overlaps with Rule %d', 'flashsale-quantity-promotion'),
                        $index + 1,
                        $other_index + 1
                    );
                }
            }
        }
        
        if (!empty($errors)) {
            wp_send_json_error(['message' => implode('<br>', $errors)]);
        }
        
        wp_send_json_success(['message' => __('Rules are valid', 'flashsale-quantity-promotion')]);
    }

    /**
     * AJAX: Check product conflicts
     */
    public function check_product_conflicts() {
        check_ajax_referer('fsqp_check_product_conflicts', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Insufficient permissions', 'flashsale-quantity-promotion')]);
        }

        $product_id = intval($_POST['product_id'] ?? 0);
        $exclude_campaign_id = intval($_POST['exclude_campaign_id'] ?? 0);

        if (!$product_id) {
            wp_send_json_error(['message' => __('Product ID is required', 'flashsale-quantity-promotion')]);
        }

        // Check if product exists
        $product = wc_get_product($product_id);
        if (!$product) {
            wp_send_json_error(['message' => __('Product not found', 'flashsale-quantity-promotion')]);
        }

        // Check for conflicts with existing campaigns
        $conflicts = $this->get_product_conflicts($product_id, $exclude_campaign_id);

        if (!empty($conflicts)) {
            wp_send_json_success([
                'has_conflicts' => true,
                'conflicts' => $conflicts,
                'message' => __('Product has conflicts with existing campaigns', 'flashsale-quantity-promotion')
            ]);
        } else {
            wp_send_json_success([
                'has_conflicts' => false,
                'message' => __('No conflicts found', 'flashsale-quantity-promotion')
            ]);
        }
    }

    /**
     * AJAX: Calculate quantity price (Frontend)
     */
    public function calculate_quantity_price() {
        $product_id = intval($_POST['product_id'] ?? 0);
        $quantity = intval($_POST['quantity'] ?? 1);
        
        if (!$product_id || $quantity < 1) {
            wp_send_json_error(['message' => __('Invalid parameters', 'flashsale-quantity-promotion')]);
        }
        
        $product = wc_get_product($product_id);
        
        if (!$product) {
            wp_send_json_error(['message' => __('Product not found', 'flashsale-quantity-promotion')]);
        }
        
        $original_price = $product->get_regular_price();
        $public = new FSQP_Public();
        $discounted_price = $public->calculate_quantity_discount($product_id, $quantity, $original_price);
        
        $response = [
            'original_price' => $original_price,
            'discounted_price' => $discounted_price,
            'discount_amount' => $original_price - $discounted_price,
            'total_original' => $original_price * $quantity,
            'total_discounted' => $discounted_price * $quantity,
            'total_savings' => ($original_price - $discounted_price) * $quantity,
            'price_html' => wc_price($discounted_price),
            'total_html' => wc_price($discounted_price * $quantity)
        ];
        
        wp_send_json_success($response);
    }
    
    /**
     * AJAX: Get quantity discounts (Frontend)
     */
    public function get_quantity_discounts() {
        $product_id = intval($_POST['product_id'] ?? 0);
        
        if (!$product_id) {
            wp_send_json_error(['message' => __('Invalid product ID', 'flashsale-quantity-promotion')]);
        }
        
        $database = new FSQP_Database();
        $rules = $database->get_product_quantity_rules($product_id);
        
        $product = wc_get_product($product_id);
        $original_price = $product ? $product->get_regular_price() : 0;
        
        $discounts = [];
        
        foreach ($rules as $rule) {
            $public = new FSQP_Public();
            $discounted_price = $public->calculate_rule_discount($original_price, $rule);
            
            $discounts[] = [
                'min_quantity' => $rule->min_quantity,
                'max_quantity' => $rule->max_quantity,
                'discount_type' => $rule->discount_type,
                'discount_value' => $rule->discount_value,
                'original_price' => $original_price,
                'discounted_price' => $discounted_price,
                'savings' => $original_price - $discounted_price,
                'savings_percent' => $original_price > 0 ? (($original_price - $discounted_price) / $original_price) * 100 : 0
            ];
        }
        
        wp_send_json_success(['discounts' => $discounts]);
    }
    
    /**
     * Handle FlashSale Core search products AJAX
     * This ensures compatibility with the core search functionality
     */
    public function handle_fs_search_products() {
        // Check if this is for quantity promotion campaign
        $campaign_type = $_POST['campaign_type'] ?? '';

        if ($campaign_type !== 'quantity-promotion') {
            return; // Let FlashSale Core handle it
        }

        // Use our search products method
        $this->search_products();
    }

    /**
     * Handle FlashSale Core admin actions AJAX
     * This ensures compatibility with the core admin functionality
     */
    public function handle_fs_admin_action() {
        // Check if this is for quantity promotion campaign
        $fs_action = $_POST['fs_action'] ?? '';

        if ($fs_action === 'remove_campaign_product') {
            $this->remove_campaign_product();
        }
    }

    /**
     * AJAX: Remove product from campaign
     */
    public function remove_campaign_product() {
        check_ajax_referer('fs_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Insufficient permissions', 'canhcampromotion-quantity-promotion')]);
        }

        $campaign_id = intval($_POST['campaign_id'] ?? 0);
        $product_id = intval($_POST['product_id'] ?? 0);

        if (!$campaign_id || !$product_id) {
            wp_send_json_error(['message' => __('Invalid campaign or product ID', 'canhcampromotion-quantity-promotion')]);
        }

        // Use FlashSale Core's campaign manager if available
        if (function_exists('FS') && FS()->campaigns()) {
            $result = FS()->campaigns()->remove_campaign_product($campaign_id, $product_id);
        } else {
            // Fallback to direct database operation
            global $wpdb;
            $products_table = $wpdb->prefix . 'fs_campaign_products';
            $result = $wpdb->delete($products_table, [
                'promotion_id' => $campaign_id,
                'product_id' => $product_id
            ]);
        }

        if ($result) {
            wp_send_json_success(['message' => __('Product removed from campaign successfully', 'canhcampromotion-quantity-promotion')]);
        } else {
            wp_send_json_error(['message' => __('Failed to remove product from campaign', 'canhcampromotion-quantity-promotion')]);
        }
    }

    /**
     * Check if two quantity ranges overlap
     */
    private function ranges_overlap($min1, $max1, $min2, $max2) {
        // Convert 0 max to infinity for comparison
        $max1 = $max1 == 0 ? PHP_INT_MAX : $max1;
        $max2 = $max2 == 0 ? PHP_INT_MAX : $max2;

        return $min1 <= $max2 && $min2 <= $max1;
    }

    /**
     * Get product conflicts with existing campaigns
     */
    private function get_product_conflicts($product_id, $exclude_campaign_id = 0) {
        global $wpdb;

        // Get the campaigns table name from FlashSale Core
        $campaigns_table = $wpdb->prefix . 'fs_campaigns';
        $products_table = $wpdb->prefix . 'fs_campaign_products';

        // Check if tables exist
        if ($wpdb->get_var("SHOW TABLES LIKE '$campaigns_table'") !== $campaigns_table) {
            return []; // No conflicts if core tables don't exist
        }

        $sql = "
            SELECT c.id, c.name, c.priority, c.type, c.status
            FROM $campaigns_table c
            INNER JOIN $products_table cp ON c.id = cp.campaign_id
            WHERE cp.product_id = %d
            AND c.status = 1
            AND c.id != %d
            ORDER BY c.priority DESC, c.created_at DESC
        ";

        $results = $wpdb->get_results(
            $wpdb->prepare($sql, $product_id, $exclude_campaign_id)
        );

        $conflicts = [];
        foreach ($results as $result) {
            $conflicts[] = [
                'campaign_id' => $result->id,
                'campaign_name' => $result->name,
                'campaign_type' => $result->type,
                'priority' => $result->priority,
                'status' => $result->status
            ];
        }

        return $conflicts;
    }

    /**
     * Format variation attributes for display
     */
    private function format_variation_attributes($variation_product) {
        $attributes = [];
        $variation_attributes = $variation_product->get_variation_attributes();
        
        foreach ($variation_attributes as $name => $value) {
            $taxonomy = str_replace('attribute_', '', $name);
            
            if (taxonomy_exists($taxonomy)) {
                $term = get_term_by('slug', $value, $taxonomy);
                $attribute_label = wc_attribute_label($taxonomy);
                $value_label = $term ? $term->name : $value;
            } else {
                $attribute_label = str_replace('pa_', '', $taxonomy);
                $value_label = $value;
            }
            
            $attributes[] = $attribute_label . ': ' . $value_label;
        }
        
        return implode(', ', $attributes);
    }
}
