<?php
/**
 * Legacy support for backward compatibility
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class FS_Legacy {
    
    /**
     * Constructor
     */
    public function __construct() {
        if (get_option('fs_enable_legacy_support', 1)) {
            $this->init_legacy_support();
        }
    }
    
    /**
     * Initialize legacy support
     */
    private function init_legacy_support() {
        // Legacy class aliases
        $this->create_legacy_aliases();

        // Legacy function wrappers
        $this->create_legacy_functions();

        // Legacy hooks
        $this->init_legacy_hooks();

        // Initialize legacy admin if needed (but disable admin menu to avoid duplicates)
        $this->init_legacy_admin();
    }
    
    /**
     * Create legacy class aliases
     */
    private function create_legacy_aliases() {
        if (!class_exists('AITFS_Campaign')) {
            class_alias('FS_Campaign_Manager', 'AITFS_Campaign');
        }
        
        if (!class_exists('AITFS_Product')) {
            class_alias('FS_Legacy_Product', 'AITFS_Product');
        }
        
        if (!class_exists('AITFS_APP')) {
            class_alias('FS_Legacy_App', 'AITFS_APP');
        }
    }
    
    /**
     * Create legacy functions
     */
    private function create_legacy_functions() {
        // Legacy debug function
        if (!function_exists('debug')) {
            function debug($data, $die = true) {
                fs_debug($data, $die);
            }
        }
        
        // Legacy helper functions
        if (!function_exists('AIT_Hanlde_Result_DB')) {
            function AIT_Hanlde_Result_DB($result, $db) {
                return $result;
            }
        }
        
        if (!function_exists('AIT_HandlePagination')) {
            function AIT_HandlePagination($page, $total, $per_page) {
                return [
                    'current_page' => $page,
                    'total_items' => $total,
                    'per_page' => $per_page,
                    'total_pages' => ceil($total / $per_page)
                ];
            }
        }
        
        if (!function_exists('AIT_Get_Product_Array')) {
            function AIT_Get_Product_Array($product_id) {
                $product = wc_get_product($product_id);
                if (!$product) {
                    return false;
                }
                
                return [
                    'ID' => $product->get_id(),
                    'name' => $product->get_name(),
                    'sku' => $product->get_sku(),
                    'price' => $product->get_price(),
                    'regular_price' => $product->get_regular_price(),
                    'sale_price' => $product->get_sale_price(),
                    'type' => $product->get_type()
                ];
            }
        }
        
        if (!function_exists('AIT_Get_Product_Gift_Array')) {
            function AIT_Get_Product_Gift_Array($product_id) {
                return AIT_Get_Product_Array($product_id);
            }
        }
        
        if (!function_exists('AIT_Load_view')) {
            function AIT_Load_view($template, $data = [], $echo = true, $return = false, $path = '') {
                if (empty($path)) {
                    $path = FLASHSALE_CORE_PLUGIN_DIR;
                }
                
                $template_file = $path . 'public/partials/' . $template;
                
                if (file_exists($template_file)) {
                    extract($data);
                    
                    if ($return) {
                        ob_start();
                        include $template_file;
                        return ob_get_clean();
                    } else {
                        include $template_file;
                    }
                }
                
                return '';
            }
        }
        
        if (!function_exists('AIT_currentUrl')) {
            function AIT_currentUrl() {
                return (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . 
                       "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
            }
        }
    }
    
    /**
     * Initialize legacy hooks
     */
    private function init_legacy_hooks() {
        // Map old hooks to new ones
        add_action('aitfs_campaign_created', function($campaign_id, $data) {
            do_action('fs_campaign_created', $campaign_id, $data);
        }, 10, 2);
        
        add_action('aitfs_campaign_updated', function($campaign_id, $data) {
            do_action('fs_campaign_updated', $campaign_id, $data);
        }, 10, 2);
        
        add_action('aitfs_campaign_deleted', function($campaign_id) {
            do_action('fs_campaign_deleted', $campaign_id);
        });
    }

    /**
     * Initialize legacy admin without duplicate menu
     */
    private function init_legacy_admin() {
        // Legacy admin is now disabled by default in class-ait-dev-admin.php
        // It will only load if fs_enable_legacy_admin option is set to 1
        // This prevents duplicate menus while maintaining backward compatibility
    }
}

/**
 * Legacy App class
 */
class FS_Legacy_App {
    public static $db;
    public static $pageLimit = 20;
    
    public function __construct() {
        if (!self::$db) {
            // Initialize legacy database connection
            require_once FLASHSALE_CORE_PLUGIN_DIR . 'helpers/database.php';
            self::$db = new MysqliDb();
        }
    }
}

/**
 * Legacy Product class
 */
class FS_Legacy_Product extends FS_Legacy_App {
    static $table = 'aitfs_promotion_product';
    
    static function getFlashSaleInfoOfProduct($filters) {
        // Convert to new system
        $campaign_manager = FS()->campaigns();
        
        if (isset($filters['product_id'])) {
            $campaigns = $campaign_manager->get_active_campaigns();
            
            foreach ($campaigns as $campaign) {
                $targets = $campaign_manager->get_campaign_targets($campaign->id);
                
                foreach ($targets as $target) {
                    if ($target->target_type === 'product' && $target->target_id == $filters['product_id']) {
                        return $target->pricing_rules;
                    }
                }
            }
        }
        
        return null;
    }
    
    static function getProduct($filters) {
        // Legacy method - convert to new system
        return self::getFlashSaleInfoOfProduct($filters);
    }
    
    static function update($id, $data) {
        // Legacy method - implement if needed
        return true;
    }
    
    static function searchWooProducts($filters) {
        global $wpdb;
        
        $product_table = $wpdb->prefix . "posts";
        
        $query = "SELECT ID, post_title FROM $product_table 
                  WHERE post_type = 'product' 
                  AND post_status = 'publish' 
                  AND post_parent = 0";
        
        if (isset($filters['search'])) {
            $search = sanitize_text_field($filters['search']);
            $query .= $wpdb->prepare(" AND post_title LIKE %s", "%$search%");
        }
        
        $query .= " LIMIT 1000";
        
        $products = $wpdb->get_results($query);
        $result = [];
        
        foreach ($products as $product) {
            $product_data = AIT_Get_Product_Array($product->ID);
            if ($product_data) {
                $result[] = $product_data;
            }
        }
        
        return [
            'data' => $result,
            'pagination' => AIT_HandlePagination(1, count($result), 1000)
        ];
    }
}
