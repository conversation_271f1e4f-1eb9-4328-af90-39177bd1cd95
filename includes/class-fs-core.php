<?php
/**
 * The core plugin class
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class FS_Core {
    
    /**
     * The single instance of the class
     */
    protected static $_instance = null;
    
    /**
     * Plugin version
     */
    public $version = FLASHSALE_CORE_VERSION;
    
    /**
     * Campaign Manager instance
     */
    public $campaign_manager;
    
    /**
     * Rule Engine instance
     */
    public $rule_engine;
    
    /**
     * Gift Manager instance
     */
    public $gift_manager;
    
    /**
     * Addon Manager instance
     */
    public $addon_manager;
    
    /**
     * Database instance
     */
    public $database;
    
    /**
     * Main FS_Core Instance
     */
    public static function get_instance() {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }
    
    /**
     * Constructor
     */
    public function __construct() {
        // Prevent duplicate construction
        if (self::$_instance !== null) {
            return self::$_instance;
        }
        
        $this->init_hooks();
        $this->includes();
        $this->init();
    }
    
    /**
     * Hook into actions and filters
     */
    private function init_hooks() {
        add_action('init', array($this, 'init'), 0);
        add_action('wp_loaded', array($this, 'wp_loaded'));
    }
    
    /**
     * Include required core files
     */
    public function includes() {
        // Database
        include_once FLASHSALE_CORE_PLUGIN_DIR . 'includes/class-fs-database.php';
        
        // Core managers
        include_once FLASHSALE_CORE_PLUGIN_DIR . 'includes/class-fs-campaign-manager.php';
        include_once FLASHSALE_CORE_PLUGIN_DIR . 'includes/class-fs-flash-sale-manager.php';
        include_once FLASHSALE_CORE_PLUGIN_DIR . 'includes/class-fs-rule-engine.php';
        include_once FLASHSALE_CORE_PLUGIN_DIR . 'includes/class-fs-gift-manager.php';
        include_once FLASHSALE_CORE_PLUGIN_DIR . 'includes/class-fs-addon-manager.php';
        
        // API
        include_once FLASHSALE_CORE_PLUGIN_DIR . 'includes/class-fs-api.php';
        
        // Admin
        if (is_admin()) {
            include_once FLASHSALE_CORE_PLUGIN_DIR . 'admin/class-fs-admin.php';

            // Include test files in development/debug mode
            if (defined('WP_DEBUG') && WP_DEBUG) {
                include_once FLASHSALE_CORE_PLUGIN_DIR . 'tests/test-cache-optimization.php';
            }
        }
        
        // Public
        if (!is_admin() || defined('DOING_AJAX')) {
            include_once FLASHSALE_CORE_PLUGIN_DIR . 'public/class-fs-public.php';
        }
        
        // Legacy support (but not legacy admin)
        // include_once FLASHSALE_CORE_PLUGIN_DIR . 'includes/class-fs-legacy.php';
    }
    
    /**
     * Init FlashSale Core when WordPress Initialises
     */
    public function init() {
        // Before init action
        do_action('before_flashsale_core_init');
        
        // Set up localisation
        $this->load_plugin_textdomain();
        
        // Initialize database
        $this->database = new FS_Database();
        $this->database->init();
        
        // Initialize managers
        $this->campaign_manager = new FS_Campaign_Manager();
        $this->flash_sale_manager = new FS_Flash_Sale_Manager();
        $this->flash_sale_manager->init();
        $this->rule_engine = new FS_Rule_Engine();
        $this->gift_manager = new FS_Gift_Manager();
        $this->addon_manager = new FS_Addon_Manager();
        
        // Initialize API
        new FS_API();
        
        // Initialize admin
        if (is_admin()) {
            new FS_Admin();
        }
        
        // Initialize public
        if (!is_admin() || defined('DOING_AJAX')) {
            new FS_Public();
        }
        
        // Initialize legacy support (data compatibility only, no admin)
        // new FS_Legacy();
        
        // Init action
        do_action('flashsale_core_init');
    }
    
    /**
     * When WP has loaded all plugins, trigger the loaded hooks
     */
    public function wp_loaded() {
        do_action('flashsale_core_loaded');
        do_action('canhcampromotion_core_loaded');
    }
    
    /**
     * Load Localisation files
     */
    public function load_plugin_textdomain() {
        $locale = determine_locale();
        $locale = apply_filters('plugin_locale', $locale, 'canhcampromotion-core');

        unload_textdomain('canhcampromotion-core');
        load_textdomain('canhcampromotion-core', WP_LANG_DIR . '/canhcampromotion-core/canhcampromotion-core-' . $locale . '.mo');
        load_plugin_textdomain('canhcampromotion-core', false, plugin_basename(dirname(FLASHSALE_CORE_PLUGIN_FILE)) . '/languages');

        // Also load legacy textdomain for backward compatibility
        unload_textdomain('flashsale-core');
        load_textdomain('flashsale-core', WP_LANG_DIR . '/flashsale-core/flashsale-core-' . $locale . '.mo');
        load_plugin_textdomain('flashsale-core', false, plugin_basename(dirname(FLASHSALE_CORE_PLUGIN_FILE)) . '/languages');
    }
    
    /**
     * Get the plugin url
     */
    public function plugin_url() {
        return untrailingslashit(plugins_url('/', FLASHSALE_CORE_PLUGIN_FILE));
    }
    
    /**
     * Get the plugin path
     */
    public function plugin_path() {
        return untrailingslashit(plugin_dir_path(FLASHSALE_CORE_PLUGIN_FILE));
    }
    
    /**
     * Get Ajax URL
     */
    public function ajax_url() {
        return admin_url('admin-ajax.php', 'relative');
    }
    
    /**
     * Get campaign manager
     */
    public function campaigns() {
        return $this->campaign_manager;
    }
    
    /**
     * Get rule engine
     */
    public function rules() {
        return $this->rule_engine;
    }
    
    /**
     * Get gift manager
     */
    public function gifts() {
        return $this->gift_manager;
    }
    
    /**
     * Get addon manager
     */
    public function addons() {
        return $this->addon_manager;
    }
    
    /**
     * Get database instance
     */
    public function db() {
        return $this->database;
    }
}

/**
 * Main instance of FlashSale Core
 */
function FS() {
    return FS_Core::get_instance();
}
