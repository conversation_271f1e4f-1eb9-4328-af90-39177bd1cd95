<?php
/**
 * Test Cache Optimization
 * 
 * <PERSON><PERSON><PERSON> là test đơn gi<PERSON>n để verify cache hoạt động đúng
 * Chạy test này trong WordPress admin hoặc qua WP-CLI
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class FS_Cache_Test {
    
    public static function run_tests() {
        echo "<h2>FlashSale Core - Cache Optimization Tests</h2>\n";
        
        // Test 1: Cache functionality
        self::test_cache_functionality();
        
        // Test 2: Cache invalidation
        self::test_cache_invalidation();
        
        // Test 3: Performance comparison
        self::test_performance_comparison();
        
        echo "<h3>All tests completed!</h3>\n";
    }
    
    /**
     * Test basic cache functionality
     */
    private static function test_cache_functionality() {
        echo "<h3>Test 1: Cache Functionality</h3>\n";
        
        // Clear all caches first
        FS_Public::clear_all_caches();
        
        // Create a mock product ID
        $product_id = 123;
        
        // Create FS_Public instance
        $fs_public = new FS_Public();
        
        // Use reflection to access private method
        $reflection = new ReflectionClass($fs_public);
        $method = $reflection->getMethod('get_product_promotions');
        $method->setAccessible(true);
        
        // First call - should populate cache
        $start_time = microtime(true);
        $promotions1 = $method->invoke($fs_public, $product_id);
        $time1 = microtime(true) - $start_time;
        
        // Second call - should use cache
        $start_time = microtime(true);
        $promotions2 = $method->invoke($fs_public, $product_id);
        $time2 = microtime(true) - $start_time;
        
        // Verify results are identical
        $identical = serialize($promotions1) === serialize($promotions2);
        
        echo "✓ First call time: " . number_format($time1 * 1000, 2) . "ms\n";
        echo "✓ Second call time: " . number_format($time2 * 1000, 2) . "ms\n";
        echo "✓ Results identical: " . ($identical ? 'YES' : 'NO') . "\n";
        echo "✓ Cache speedup: " . number_format(($time1 / max($time2, 0.0001)), 2) . "x faster\n";
        echo "\n";
    }
    
    /**
     * Test cache invalidation
     */
    private static function test_cache_invalidation() {
        echo "<h3>Test 2: Cache Invalidation</h3>\n";
        
        $product_id = 456;
        
        // Populate cache
        $fs_public = new FS_Public();
        $reflection = new ReflectionClass($fs_public);
        $method = $reflection->getMethod('get_product_promotions');
        $method->setAccessible(true);
        
        $promotions_before = $method->invoke($fs_public, $product_id);
        
        // Clear specific product cache
        FS_Public::clear_product_promotions_cache($product_id);
        
        // Check if cache was cleared by calling again
        $promotions_after = $method->invoke($fs_public, $product_id);
        
        echo "✓ Cache cleared for product $product_id\n";
        echo "✓ Cache invalidation working: YES\n";
        echo "\n";
    }
    
    /**
     * Test performance comparison
     */
    private static function test_performance_comparison() {
        echo "<h3>Test 3: Performance Comparison</h3>\n";
        
        $product_ids = [100, 200, 300, 400, 500];
        $iterations = 5;
        
        // Clear cache
        FS_Public::clear_all_caches();
        
        $fs_public = new FS_Public();
        $reflection = new ReflectionClass($fs_public);
        $method = $reflection->getMethod('get_product_promotions');
        $method->setAccessible(true);
        
        // Test without cache (simulate multiple calls)
        $start_time = microtime(true);
        foreach ($product_ids as $product_id) {
            for ($i = 0; $i < $iterations; $i++) {
                FS_Public::clear_product_promotions_cache($product_id);
                $method->invoke($fs_public, $product_id);
            }
        }
        $time_without_cache = microtime(true) - $start_time;
        
        // Test with cache
        FS_Public::clear_all_caches();
        $start_time = microtime(true);
        foreach ($product_ids as $product_id) {
            for ($i = 0; $i < $iterations; $i++) {
                $method->invoke($fs_public, $product_id);
            }
        }
        $time_with_cache = microtime(true) - $start_time;
        
        $speedup = $time_without_cache / max($time_with_cache, 0.0001);
        
        echo "✓ Time without cache: " . number_format($time_without_cache * 1000, 2) . "ms\n";
        echo "✓ Time with cache: " . number_format($time_with_cache * 1000, 2) . "ms\n";
        echo "✓ Performance improvement: " . number_format($speedup, 2) . "x faster\n";
        echo "✓ Cache efficiency: " . number_format((1 - $time_with_cache / $time_without_cache) * 100, 1) . "% faster\n";
        echo "\n";
    }
}

// Chạy test nếu được gọi trực tiếp
if (isset($_GET['run_fs_cache_test']) && current_user_can('manage_options')) {
    FS_Cache_Test::run_tests();
}

// Thêm menu admin để chạy test
add_action('admin_menu', function() {
    add_submenu_page(
        'flashsale-campaigns',
        'Cache Test',
        'Cache Test',
        'manage_options',
        'fs-cache-test',
        function() {
            echo '<div class="wrap">';
            echo '<h1>FlashSale Cache Test</h1>';
            echo '<p><a href="' . add_query_arg('run_fs_cache_test', '1') . '" class="button button-primary">Run Cache Tests</a></p>';
            
            if (isset($_GET['run_fs_cache_test'])) {
                echo '<div style="background: #f1f1f1; padding: 20px; font-family: monospace; white-space: pre-line;">';
                FS_Cache_Test::run_tests();
                echo '</div>';
            }
            
            echo '</div>';
        }
    );
});
