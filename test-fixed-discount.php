<?php
/**
 * Test script for fixed discount amount (not multiplied by quantity)
 * Add this to wp-config.php or run in WordPress admin context
 */

function test_fixed_discount_functionality() {
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }
    
    echo "<h1>Fixed Discount Amount Test</h1>";
    echo "<p><strong>Goal:</strong> Verify that discount is applied as fixed amount, not multiplied by quantity</p>";
    
    // Step 1: Test discount calculation logic
    echo "<h2>Step 1: Discount Calculation Logic</h2>";
    
    if (class_exists('FSQP_Public')) {
        $fsqp_public = new FSQP_Public();
        
        // Test scenarios
        $test_scenarios = [
            [
                'product_id' => 38,
                'quantity' => 1,
                'original_price' => 20.00,
                'expected_discount_per_item' => 2.00,
                'expected_total_discount' => 2.00 // Fixed amount, not multiplied
            ],
            [
                'product_id' => 38,
                'quantity' => 5,
                'original_price' => 20.00,
                'expected_discount_per_item' => 5.00,
                'expected_total_discount' => 5.00 // Fixed amount, not multiplied
            ],
            [
                'product_id' => 38,
                'quantity' => 10,
                'original_price' => 20.00,
                'expected_discount_per_item' => 10.00,
                'expected_total_discount' => 10.00 // Fixed amount, not multiplied
            ]
        ];
        
        foreach ($test_scenarios as $index => $scenario) {
            echo "<h3>Scenario " . ($index + 1) . "</h3>";
            echo "<p>Product ID: {$scenario['product_id']}, Quantity: {$scenario['quantity']}, Price: $" . number_format($scenario['original_price'], 2) . "</p>";
            
            $discounted_price = $fsqp_public->calculate_quantity_discount(
                $scenario['product_id'], 
                $scenario['quantity'], 
                $scenario['original_price']
            );
            
            if ($discounted_price < $scenario['original_price']) {
                $actual_discount_per_item = $scenario['original_price'] - $discounted_price;
                $actual_total_discount = $actual_discount_per_item; // Fixed amount
                
                echo "<p style='color: green;'>✅ Discount applied:</p>";
                echo "<p>&nbsp;&nbsp;- Discount per item: $" . number_format($actual_discount_per_item, 2) . "</p>";
                echo "<p>&nbsp;&nbsp;- Total discount (fixed): $" . number_format($actual_total_discount, 2) . "</p>";
                
                // Verify it's not multiplied by quantity
                $old_way_total = $actual_discount_per_item * $scenario['quantity'];
                if ($actual_total_discount !== $old_way_total && $scenario['quantity'] > 1) {
                    echo "<p style='color: green;'>✅ Confirmed: Discount is NOT multiplied by quantity</p>";
                    echo "<p>&nbsp;&nbsp;- Old way (multiplied): $" . number_format($old_way_total, 2) . "</p>";
                    echo "<p>&nbsp;&nbsp;- New way (fixed): $" . number_format($actual_total_discount, 2) . "</p>";
                } else {
                    echo "<p style='color: blue;'>ℹ️ Single item or same amount</p>";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ No discount applied (may be normal if no rules exist)</p>";
            }
            
            echo "<hr>";
        }
    } else {
        echo "<p style='color: red;'>❌ FSQP_Public class not found</p>";
        return;
    }
    
    // Step 2: Test cart simulation
    echo "<h2>Step 2: Cart Simulation Test</h2>";
    
    // Simulate cart items with different quantities
    $mock_cart_items = [
        [
            'product_id' => 38,
            'product_name' => 'Test Product A',
            'quantity' => 3,
            'original_price' => 20.00,
            'discounted_price' => 18.00 // $2 discount per item
        ],
        [
            'product_id' => 39,
            'product_name' => 'Test Product B',
            'quantity' => 7,
            'original_price' => 15.00,
            'discounted_price' => 12.00 // $3 discount per item
        ]
    ];
    
    $total_cart_discount = 0;
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>Product</th><th>Quantity</th><th>Original Price</th><th>Discount/Item</th><th>Old Way (×Qty)</th><th>New Way (Fixed)</th>";
    echo "</tr>";
    
    foreach ($mock_cart_items as $item) {
        $discount_per_item = $item['original_price'] - $item['discounted_price'];
        $old_way_total = $discount_per_item * $item['quantity'];
        $new_way_total = $discount_per_item; // Fixed amount
        
        $total_cart_discount += $new_way_total;
        
        echo "<tr>";
        echo "<td>{$item['product_name']}</td>";
        echo "<td>{$item['quantity']}</td>";
        echo "<td>$" . number_format($item['original_price'], 2) . "</td>";
        echo "<td>$" . number_format($discount_per_item, 2) . "</td>";
        echo "<td style='color: #999; text-decoration: line-through;'>$" . number_format($old_way_total, 2) . "</td>";
        echo "<td style='color: #27ae60; font-weight: bold;'>$" . number_format($new_way_total, 2) . "</td>";
        echo "</tr>";
    }
    
    echo "<tr style='background: #e8f5e8; font-weight: bold;'>";
    echo "<td colspan='5'>Total Cart Discount:</td>";
    echo "<td style='color: #27ae60;'>$" . number_format($total_cart_discount, 2) . "</td>";
    echo "</tr>";
    echo "</table>";
    
    // Step 3: Test display methods
    echo "<h2>Step 3: Display Methods Test</h2>";
    
    // Test cart item display
    $mock_cart_item = [
        'fsqp_fixed_discount' => 5.00,
        'fsqp_discount_amount' => 5.00,
        'quantity' => 3
    ];
    
    echo "<h3>Cart Item Display Test</h3>";
    echo "<p>Mock cart item data:</p>";
    echo "<ul>";
    echo "<li>Fixed discount: $" . number_format($mock_cart_item['fsqp_fixed_discount'], 2) . "</li>";
    echo "<li>Discount per item: $" . number_format($mock_cart_item['fsqp_discount_amount'], 2) . "</li>";
    echo "<li>Quantity: {$mock_cart_item['quantity']}</li>";
    echo "</ul>";
    
    $original_price_html = wc_price(20.00);
    $result_html = $fsqp_public->display_cart_item_discount($original_price_html, $mock_cart_item, 'test_key');
    
    echo "<p><strong>Display result:</strong></p>";
    echo "<div style='border: 1px solid #ddd; padding: 10px; background: #f9f9f9;'>";
    echo $result_html;
    echo "</div>";
    
    // Step 4: Test discount details
    echo "<h2>Step 4: Discount Details Test</h2>";
    
    $mock_discount_details = [
        [
            'product_name' => 'Test Product A',
            'quantity' => 3,
            'discount_per_item' => 5.00,
            'total_discount' => 5.00 // Fixed amount
        ],
        [
            'product_name' => 'Test Product B',
            'quantity' => 7,
            'discount_per_item' => 3.00,
            'total_discount' => 3.00 // Fixed amount
        ]
    ];
    
    echo "<p>Mock discount details:</p>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>Product</th><th>Quantity</th><th>Discount/Item</th><th>Total Discount</th>";
    echo "</tr>";
    
    foreach ($mock_discount_details as $detail) {
        echo "<tr>";
        echo "<td>{$detail['product_name']}</td>";
        echo "<td>{$detail['quantity']}</td>";
        echo "<td>$" . number_format($detail['discount_per_item'], 2) . "</td>";
        echo "<td style='color: #27ae60; font-weight: bold;'>$" . number_format($detail['total_discount'], 2) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Step 5: Summary
    echo "<h2>Step 5: Summary</h2>";
    
    echo "<div style='background: #e8f5e8; border: 1px solid #27ae60; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #27ae60; margin-top: 0;'>✅ Fixed Discount System</h3>";
    echo "<p><strong>How it works now:</strong></p>";
    echo "<ul>";
    echo "<li>Discount amount is calculated per item based on quantity rules</li>";
    echo "<li>Total discount = discount per item (NOT multiplied by quantity)</li>";
    echo "<li>Customer pays: (Original price × Quantity) - Fixed discount amount</li>";
    echo "<li>Fee shows as single discount line item in cart</li>";
    echo "</ul>";
    
    echo "<p><strong>Example:</strong></p>";
    echo "<ul>";
    echo "<li>Product: $20 each</li>";
    echo "<li>Quantity: 5 items</li>";
    echo "<li>Rule: 5+ items get $3 discount per item</li>";
    echo "<li>Subtotal: $20 × 5 = $100</li>";
    echo "<li>Discount: -$3 (fixed amount, not $3 × 5 = $15)</li>";
    echo "<li>Total: $100 - $3 = $97</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-top: 15px;'>";
    echo "<h3 style='color: #856404; margin-top: 0;'>⚠️ Important Notes</h3>";
    echo "<ul>";
    echo "<li>This is different from percentage discounts which naturally scale with quantity</li>";
    echo "<li>Fixed amount discounts provide consistent savings regardless of quantity</li>";
    echo "<li>Make sure your quantity rules are configured with appropriate discount amounts</li>";
    echo "<li>Test thoroughly with different quantities to ensure expected behavior</li>";
    echo "</ul>";
    echo "</div>";
}

// Add admin page
add_action('admin_menu', function() {
    if (current_user_can('manage_options')) {
        add_submenu_page(
            null,
            'Test Fixed Discount',
            'Test Fixed Discount',
            'manage_options',
            'test-fixed-discount',
            'test_fixed_discount_functionality'
        );
    }
});

// Add quick access
add_action('admin_notices', function() {
    if (current_user_can('manage_options') && 
        (strpos($_SERVER['REQUEST_URI'], 'flashsale') !== false || 
         strpos($_SERVER['REQUEST_URI'], 'campaign') !== false)) {
        echo '<div class="notice notice-info"><p>';
        echo '<strong>Fixed Discount Test:</strong> ';
        echo '<a href="?page=test-fixed-discount" target="_blank" class="button button-small">Test Fixed Discount</a>';
        echo '</p></div>';
    }
});
