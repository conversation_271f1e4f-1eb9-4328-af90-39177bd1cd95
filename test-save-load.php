<?php
/**
 * Test script to verify save and load functionality
 * 
 * This script should be run in WordPress admin context to test the save/load functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    echo "This script should be run in WordPress context\n";
    exit;
}

/**
 * Test saving campaign data
 */
function test_save_campaign() {
    echo "Testing Campaign Save Functionality:\n";
    echo "====================================\n";
    
    // Simulate campaign data
    $campaign_data = [
        'name' => 'Test Quantity Promotion',
        'type' => 'quantity-promotion',
        'status' => 1,
        'priority' => 10,
        'start_date' => date('Y-m-d H:i:s'),
        'end_date' => date('Y-m-d H:i:s', strtotime('+30 days'))
    ];
    
    // Simulate products data
    $products_data = [
        '38' => [ // Beanie with Logo
            'product_id' => 38,
            'ranges' => [
                [
                    'min_quantity' => 1,
                    'max_quantity' => 5,
                    'discount_type' => 1,
                    'discount_value' => 5,
                    'max_discount_amount' => 0
                ],
                [
                    'min_quantity' => 6,
                    'max_quantity' => 0,
                    'discount_type' => 1,
                    'discount_value' => 10,
                    'max_discount_amount' => 0
                ]
            ]
        ]
    ];
    
    // Test if classes exist
    if (!class_exists('FSQP_Campaign_Handler')) {
        echo "✗ FSQP_Campaign_Handler class not found\n";
        return false;
    }
    
    if (!class_exists('FSQP_Database')) {
        echo "✗ FSQP_Database class not found\n";
        return false;
    }
    
    echo "✓ Required classes found\n";
    
    // Test database connection
    global $wpdb;
    $campaigns_table = $wpdb->prefix . 'fs_campaigns';
    $products_table = $wpdb->prefix . 'fs_campaign_products';
    
    if ($wpdb->get_var("SHOW TABLES LIKE '$campaigns_table'") !== $campaigns_table) {
        echo "✗ Campaigns table not found: $campaigns_table\n";
        return false;
    }
    
    if ($wpdb->get_var("SHOW TABLES LIKE '$products_table'") !== $products_table) {
        echo "✗ Products table not found: $products_table\n";
        return false;
    }
    
    echo "✓ Database tables found\n";
    
    // Test campaign handler
    $handler = new FSQP_Campaign_Handler();
    
    // Simulate save hook trigger
    $_POST['products'] = $products_data;
    
    try {
        // This would normally be triggered by the save hook
        $handler->save_quantity_promotion_data(999, $campaign_data, $_POST);
        echo "✓ Save method executed without errors\n";
    } catch (Exception $e) {
        echo "✗ Save method failed: " . $e->getMessage() . "\n";
        return false;
    }
    
    return true;
}

/**
 * Test loading campaign data
 */
function test_load_campaign() {
    echo "\nTesting Campaign Load Functionality:\n";
    echo "====================================\n";
    
    if (!class_exists('FSQP_Database')) {
        echo "✗ FSQP_Database class not found\n";
        return false;
    }
    
    $database = new FSQP_Database();
    
    // Test loading rules for a campaign
    try {
        $rules = $database->get_campaign_quantity_rules(999, false);
        echo "✓ Load method executed without errors\n";
        echo "Found " . count($rules) . " rules\n";
        
        if (!empty($rules)) {
            echo "Sample rule data:\n";
            $rule = $rules[0];
            echo "  - Product ID: " . $rule->product_id . "\n";
            echo "  - Min Qty: " . $rule->min_quantity . "\n";
            echo "  - Max Qty: " . $rule->max_quantity . "\n";
            echo "  - Discount: " . $rule->discount_value . "% \n";
        }
        
    } catch (Exception $e) {
        echo "✗ Load method failed: " . $e->getMessage() . "\n";
        return false;
    }
    
    return true;
}

/**
 * Test hooks registration
 */
function test_hooks() {
    echo "\nTesting Hooks Registration:\n";
    echo "===========================\n";
    
    global $wp_filter;
    
    $required_hooks = [
        'fs_save_campaign',
        'ccp_save_campaign'
    ];
    
    foreach ($required_hooks as $hook) {
        if (isset($wp_filter[$hook])) {
            echo "✓ Hook '$hook' is registered\n";
            
            // Check if our handler is attached
            $callbacks = $wp_filter[$hook]->callbacks;
            $found_handler = false;
            
            foreach ($callbacks as $priority => $functions) {
                foreach ($functions as $function) {
                    if (is_array($function['function']) && 
                        $function['function'][0] instanceof FSQP_Campaign_Handler) {
                        $found_handler = true;
                        echo "  - FSQP handler found at priority $priority\n";
                        break 2;
                    }
                }
            }
            
            if (!$found_handler) {
                echo "  ✗ FSQP handler not found for this hook\n";
            }
        } else {
            echo "✗ Hook '$hook' is not registered\n";
        }
    }
}

/**
 * Test form data structure
 */
function test_form_data() {
    echo "\nTesting Form Data Structure:\n";
    echo "============================\n";
    
    // Simulate form submission data
    $form_data = [
        'campaign_id' => 999,
        'name' => 'Test Campaign',
        'type' => 'quantity-promotion',
        'products' => [
            '38' => [
                'product_id' => 38,
                'ranges' => [
                    [
                        'min_quantity' => 1,
                        'max_quantity' => 5,
                        'discount_type' => 1,
                        'discount_value' => 5,
                        'max_discount_amount' => 0
                    ]
                ]
            ]
        ]
    ];
    
    echo "✓ Form data structure looks correct\n";
    echo "Products count: " . count($form_data['products']) . "\n";
    echo "Ranges for product 38: " . count($form_data['products']['38']['ranges']) . "\n";
    
    return true;
}

// Run tests if WordPress is loaded
if (defined('ABSPATH')) {
    echo "FlashSale Quantity Promotion - Save/Load Test\n";
    echo "=============================================\n\n";
    
    test_hooks();
    test_form_data();
    test_save_campaign();
    test_load_campaign();
    
    echo "\nTest completed!\n";
} else {
    echo "This script should be run in WordPress context\n";
    echo "You can include it in a WordPress admin page or run via WP-CLI\n";
}
