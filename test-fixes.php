<?php
/**
 * Test file to verify the fixes for:
 * 1. Date range clear functionality 
 * 2. Variable product progress bar display
 * 
 * @package FlashSaleCore
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test Fix 1: Date Range Clear Functionality
 */
function test_date_range_clear_functionality() {
    echo "<h2>Test 1: Date Range Clear Functionality</h2>";
    
    // Check if the date range picker has clear functionality
    $flash_sale_file = 'admin/views/campaign-types/flash-sale.php';
    
    if (file_exists($flash_sale_file)) {
        $content = file_get_contents($flash_sale_file);
        
        // Check for clear button
        $has_clear_button = strpos($content, 'fs-datetime-clear') !== false;
        $has_clear_handler = strpos($content, 'fs-datetime-clear\').on(\'click\'') !== false;
        
        echo "<p><strong>Clear Button Present:</strong> " . ($has_clear_button ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Clear Handler Present:</strong> " . ($has_clear_handler ? '✅ Yes' : '❌ No') . "</p>";
        
        if ($has_clear_button && $has_clear_handler) {
            echo "<p style='color: green;'>✅ Date range clear functionality is properly implemented!</p>";
        } else {
            echo "<p style='color: red;'>❌ Date range clear functionality needs attention.</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Flash sale file not found.</p>";
    }
}

/**
 * Test Fix 2: Variable Product Progress Bar Display
 */
function test_variable_product_progress_bar() {
    echo "<h2>Test 2: Variable Product Progress Bar Display</h2>";
    
    // Check if progress bars are no longer hidden for variable products
    $flash_sale_bar_file = 'public/partials/single-product/flash-sale-bar.php';
    
    if (file_exists($flash_sale_bar_file)) {
        $content = file_get_contents($flash_sale_bar_file);
        
        // Check if the hidden style has been removed
        $has_hidden_style = strpos($content, 'style="display: none;"') !== false;
        $has_data_key = strpos($content, 'data-key="<?php echo $p[\'product_id\']; ?>"') !== false;
        
        echo "<p><strong>Hidden Style Removed:</strong> " . (!$has_hidden_style ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Data Key Present:</strong> " . ($has_data_key ? '✅ Yes' : '❌ No') . "</p>";
        
        if (!$has_hidden_style && $has_data_key) {
            echo "<p style='color: green;'>✅ Variable product progress bars are now visible!</p>";
        } else {
            echo "<p style='color: red;'>❌ Variable product progress bars still have issues.</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Flash sale bar file not found.</p>";
    }
    
    // Check JavaScript functionality
    $public_js_file = 'public/js/public.js';
    
    if (file_exists($public_js_file)) {
        $js_content = file_get_contents($public_js_file);
        
        $has_variation_init = strpos($js_content, 'initVariableProductBars') !== false;
        $has_variation_update = strpos($js_content, 'updateVariationProgress') !== false;
        $has_variation_hide = strpos($js_content, 'hideAllVariationProgress') !== false;
        
        echo "<p><strong>Variable Product JS Init:</strong> " . ($has_variation_init ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Variation Update Function:</strong> " . ($has_variation_update ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Variation Hide Function:</strong> " . ($has_variation_hide ? '✅ Yes' : '❌ No') . "</p>";
        
        if ($has_variation_init && $has_variation_update && $has_variation_hide) {
            echo "<p style='color: green;'>✅ JavaScript for variable products is properly implemented!</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ JavaScript for variable products needs attention.</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Public JS file not found.</p>";
    }
    
    // Check CSS styles
    $public_css_file = 'public/css/public.css';
    
    if (file_exists($public_css_file)) {
        $css_content = file_get_contents($public_css_file);
        
        $has_sold_feal_styles = strpos($css_content, '.sold-feal[data-key]') !== false;
        $has_countdown_styles = strpos($css_content, '.countdown-fs[data-key]') !== false;
        $has_process_bar_styles = strpos($css_content, '.process-bar') !== false;
        
        echo "<p><strong>Sold Feal Styles:</strong> " . ($has_sold_feal_styles ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Countdown Styles:</strong> " . ($has_countdown_styles ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Process Bar Styles:</strong> " . ($has_process_bar_styles ? '✅ Yes' : '❌ No') . "</p>";
        
        if ($has_sold_feal_styles && $has_countdown_styles && $has_process_bar_styles) {
            echo "<p style='color: green;'>✅ CSS for variable products is properly implemented!</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ CSS for variable products needs attention.</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Public CSS file not found.</p>";
    }
}

/**
 * Test Fix 3: Admin CSS for DateTime Modal
 */
function test_admin_css_datetime_modal() {
    echo "<h2>Test 3: Admin CSS for DateTime Modal</h2>";
    
    $admin_css_file = 'admin/css/admin.css';
    
    if (file_exists($admin_css_file)) {
        $css_content = file_get_contents($admin_css_file);
        
        $has_modal_styles = strpos($css_content, '.fs-datetime-modal') !== false;
        $has_clear_styles = strpos($css_content, '.fs-datetime-clear') !== false;
        $has_animations = strpos($css_content, '@keyframes fadeIn') !== false;
        
        echo "<p><strong>Modal Styles:</strong> " . ($has_modal_styles ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Clear Button Styles:</strong> " . ($has_clear_styles ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Animation Styles:</strong> " . ($has_animations ? '✅ Yes' : '❌ No') . "</p>";
        
        if ($has_modal_styles && $has_clear_styles && $has_animations) {
            echo "<p style='color: green;'>✅ Admin CSS for DateTime modal is properly implemented!</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Admin CSS for DateTime modal needs attention.</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Admin CSS file not found.</p>";
    }
}

/**
 * Run all tests
 */
function run_all_tests() {
    echo "<h1>FlashSale Core - Fixes Verification</h1>";
    echo "<p>Testing the fixes for date range clear functionality and variable product progress bars.</p>";
    echo "<hr>";
    
    test_date_range_clear_functionality();
    echo "<hr>";
    
    test_variable_product_progress_bar();
    echo "<hr>";
    
    test_admin_css_datetime_modal();
    echo "<hr>";
    
    echo "<h2>Summary</h2>";
    echo "<p>All tests completed. Please review the results above to ensure all fixes are working correctly.</p>";
    echo "<p><strong>Expected behaviors:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Date range picker should have a red 'Clear' button that clears the selected dates</li>";
    echo "<li>✅ Progress bars should be visible for variable products when a variation is selected</li>";
    echo "<li>✅ Progress bars should be hidden by default and shown only for the selected variation</li>";
    echo "<li>✅ Admin interface should have improved styling for the datetime modal</li>";
    echo "</ul>";
}

// Run tests if this file is accessed directly
if (isset($_GET['run_tests']) && $_GET['run_tests'] == '1') {
    run_all_tests();
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>FlashSale Core - Fixes Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        h2 { color: #666; border-bottom: 1px solid #eee; padding-bottom: 5px; }
        p { margin: 8px 0; }
        ul { margin: 10px 0; padding-left: 20px; }
        hr { margin: 20px 0; border: none; border-top: 1px solid #eee; }
        .test-button { 
            background: #007cba; 
            color: white; 
            padding: 10px 20px; 
            text-decoration: none; 
            border-radius: 4px; 
            display: inline-block; 
            margin: 10px 0;
        }
        .test-button:hover { background: #005a87; }
    </style>
</head>
<body>
    <?php if (!isset($_GET['run_tests'])): ?>
        <h1>FlashSale Core - Fixes Test</h1>
        <p>Click the button below to run tests for the implemented fixes:</p>
        <a href="?run_tests=1" class="test-button">Run Tests</a>
        
        <h2>What will be tested:</h2>
        <ol>
            <li><strong>Date Range Clear Functionality:</strong> Check if the date range picker has a working clear button</li>
            <li><strong>Variable Product Progress Bars:</strong> Verify progress bars display correctly for variable products</li>
            <li><strong>Admin Interface Improvements:</strong> Check styling and UX improvements</li>
        </ol>
    <?php else: ?>
        <?php run_all_tests(); ?>
        <p><a href="?" class="test-button">Run Tests Again</a></p>
    <?php endif; ?>
</body>
</html> 