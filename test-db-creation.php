<?php
/**
 * Test script to verify database table creation
 * Run this from WordPress admin or via WP-CLI
 */

// Ensure this is run in WordPress context
if (!defined('ABSPATH')) {
    // If running via WP-CLI or direct access, load WordPress
    require_once dirname(__FILE__) . '/../../../wp-config.php';
}

// Load the database class
require_once dirname(__FILE__) . '/includes/class-fs-database.php';

echo "Testing FlashSale Core Database Creation...\n";

try {
    // Create database instance
    $database = new FS_Database();
    
    echo "Creating tables...\n";
    $results = $database->force_create_tables();
    
    echo "Table creation results:\n";
    print_r($results);
    
    // Check if tables exist
    global $wpdb;
    
    $tables_to_check = [
        'fs_campaigns',
        'fs_campaign_products',
        'fs_product_gifts',
        'fs_product_gifts_offline',
        'fs_addons'
    ];
    
    echo "\nChecking table existence:\n";
    foreach ($tables_to_check as $table) {
        $full_table_name = $wpdb->prefix . $table;
        $exists = $wpdb->get_var("SHOW TABLES LIKE '$full_table_name'") == $full_table_name;
        echo "- $full_table_name: " . ($exists ? "EXISTS" : "NOT FOUND") . "\n";
    }
    
    echo "\nDatabase version: " . get_option('fs_db_version', 'Not set') . "\n";
    echo "Migration completed: " . (get_option('fs_migration_completed', false) ? 'Yes' : 'No') . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\nTest completed.\n";
