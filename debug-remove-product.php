<?php
/**
 * Debug script for testing product removal functionality
 * 
 * This script helps debug the campaign product removal issue
 * 
 * @package FlashSale_Core
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Debug the remove product functionality
 */
function debug_remove_product_functionality() {
    echo "<h1>Debug: Remove Product Functionality</h1>";
    
    // Check if we're in admin and have the right permissions
    if (!is_admin() || !current_user_can('manage_options')) {
        echo "<p style='color: red;'>❌ Must be admin with manage_options capability</p>";
        return;
    }
    
    echo "<h2>1. Check Core Classes</h2>";
    
    // Check if core classes exist
    $fs_exists = function_exists('FS');
    echo "<p><strong>FS() function exists:</strong> " . ($fs_exists ? '✅ Yes' : '❌ No') . "</p>";
    
    if ($fs_exists) {
        $campaigns_exists = method_exists(FS(), 'campaigns');
        echo "<p><strong>FS()->campaigns() exists:</strong> " . ($campaigns_exists ? '✅ Yes' : '❌ No') . "</p>";
        
        if ($campaigns_exists) {
            $remove_method_exists = method_exists(FS()->campaigns(), 'remove_campaign_product');
            echo "<p><strong>remove_campaign_product method exists:</strong> " . ($remove_method_exists ? '✅ Yes' : '❌ No') . "</p>";
        }
    }
    
    echo "<h2>2. Check Database Tables</h2>";
    
    global $wpdb;
    
    // Check if campaign_products table exists
    $products_table = $wpdb->prefix . 'fs_campaign_products';
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$products_table'") == $products_table;
    echo "<p><strong>Campaign products table exists:</strong> " . ($table_exists ? '✅ Yes' : '❌ No') . "</p>";
    echo "<p><strong>Table name:</strong> $products_table</p>";
    
    if ($table_exists) {
        // Show table structure
        $columns = $wpdb->get_results("DESCRIBE $products_table");
        echo "<h3>Table Structure:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column->Field}</td>";
            echo "<td>{$column->Type}</td>";
            echo "<td>{$column->Null}</td>";
            echo "<td>{$column->Key}</td>";
            echo "<td>{$column->Default}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Show sample data
        $sample_data = $wpdb->get_results("SELECT * FROM $products_table LIMIT 5");
        echo "<h3>Sample Data (first 5 rows):</h3>";
        if (!empty($sample_data)) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            $first_row = $sample_data[0];
            echo "<tr>";
            foreach ($first_row as $key => $value) {
                echo "<th>$key</th>";
            }
            echo "</tr>";
            
            foreach ($sample_data as $row) {
                echo "<tr>";
                foreach ($row as $value) {
                    echo "<td>$value</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No data found in table.</p>";
        }
    }
    
    echo "<h2>3. Check AJAX Handlers</h2>";
    
    // Check if admin class exists
    $admin_class_exists = class_exists('FS_Admin');
    echo "<p><strong>FS_Admin class exists:</strong> " . ($admin_class_exists ? '✅ Yes' : '❌ No') . "</p>";
    
    if ($admin_class_exists) {
        $remove_method_exists = method_exists('FS_Admin', 'handle_ajax_request');
        echo "<p><strong>handle_ajax_request method exists:</strong> " . ($remove_method_exists ? '✅ Yes' : '❌ No') . "</p>";
    }
    
    // Check if AJAX actions are registered
    global $wp_filter;
    $ajax_registered = isset($wp_filter['wp_ajax_fs_admin_action']);
    echo "<p><strong>wp_ajax_fs_admin_action hook registered:</strong> " . ($ajax_registered ? '✅ Yes' : '❌ No') . "</p>";
    
    if ($ajax_registered) {
        echo "<p><strong>Registered callbacks:</strong></p>";
        echo "<ul>";
        foreach ($wp_filter['wp_ajax_fs_admin_action']->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function'])) {
                    $class = is_object($callback['function'][0]) ? get_class($callback['function'][0]) : $callback['function'][0];
                    $method = $callback['function'][1];
                    echo "<li>Priority $priority: {$class}::{$method}</li>";
                } else {
                    echo "<li>Priority $priority: {$callback['function']}</li>";
                }
            }
        }
        echo "</ul>";
    }
    
    echo "<h2>4. Test Manual Removal</h2>";
    
    if ($table_exists && !empty($sample_data)) {
        $test_row = $sample_data[0];
        $campaign_id = $test_row->promotion_id;
        $product_id = $test_row->product_id;
        
        echo "<p><strong>Test data:</strong> Campaign ID: $campaign_id, Product ID: $product_id</p>";
        
        // Test the removal method directly
        if ($fs_exists && $campaigns_exists && $remove_method_exists) {
            echo "<p><strong>Testing FS()->campaigns()->remove_campaign_product($campaign_id, $product_id)...</strong></p>";
            
            // Don't actually remove, just test the query
            $test_query = $wpdb->prepare(
                "SELECT COUNT(*) FROM $products_table WHERE promotion_id = %d AND product_id = %d",
                $campaign_id, $product_id
            );
            $count_before = $wpdb->get_var($test_query);
            echo "<p>Records found before removal: $count_before</p>";
            
            if ($count_before > 0) {
                echo "<p style='color: green;'>✅ Test data exists - removal should work</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ No test data found for this combination</p>";
            }
        }
    }
    
    echo "<h2>5. JavaScript Debug Instructions</h2>";
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>To debug the JavaScript:</strong></p>";
    echo "<ol>";
    echo "<li>Open browser developer tools (F12)</li>";
    echo "<li>Go to Console tab</li>";
    echo "<li>Try to remove a product from a campaign</li>";
    echo "<li>Look for console.log messages showing:</li>";
    echo "<ul>";
    echo "<li>Product ID being removed</li>";
    echo "<li>Campaign ID from form</li>";
    echo "<li>AJAX response</li>";
    echo "</ul>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>6. Next Steps</h2>";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>If the issue persists:</strong></p>";
    echo "<ol>";
    echo "<li>Check the browser console for JavaScript errors</li>";
    echo "<li>Check WordPress error logs for PHP errors</li>";
    echo "<li>Verify the campaign ID is being passed correctly in the form</li>";
    echo "<li>Verify the product ID is being extracted correctly from the table row</li>";
    echo "</ol>";
    echo "</div>";
}

// Run the debug if accessed directly
if (isset($_GET['debug_remove_product'])) {
    debug_remove_product_functionality();
}
