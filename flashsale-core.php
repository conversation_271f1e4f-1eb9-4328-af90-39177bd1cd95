<?php
/**
 * Plugin Name: CanhcamPromotion Core
 * Plugin URI: https://vietanhit.com/canhcampromotion
 * Description: Core plugin cho hệ thống khu<PERSON>ến mãi CanhcamPromotion với addon architecture.
 * Version: 2.0.0
 * Author: Viet Anh
 * Author URI: https://vietanhit.com/
 * License: GPL-2.0+
 * License URI: http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain: canhcampromotion-core
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Plugin constants
 */
define('FLASHSALE_CORE_VERSION', '2.0.0');
define('FLASHSALE_CORE_PLUGIN_FILE', __FILE__);
define('FLASHSALE_CORE_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('FLASHSALE_CORE_PLUGIN_URL', plugin_dir_url(__FILE__));
define('FLASHSALE_CORE_PLUGIN_BASENAME', plugin_basename(__FILE__));
define('FLASHSALE_CORE_TEXT_DOMAIN', 'canhcampromotion-core');

// CanhcamPromotion constants (new branding)
define('CANHCAMPROMOTION_CORE_VERSION', '2.0.0');
define('CANHCAMPROMOTION_CORE_PLUGIN_FILE', __FILE__);
define('CANHCAMPROMOTION_CORE_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('CANHCAMPROMOTION_CORE_PLUGIN_URL', plugin_dir_url(__FILE__));
define('CANHCAMPROMOTION_CORE_PLUGIN_BASENAME', plugin_basename(__FILE__));
define('CANHCAMPROMOTION_CORE_TEXT_DOMAIN', 'canhcampromotion-core');

// Legacy constants for backward compatibility
define('AITFS_PATH', FLASHSALE_CORE_PLUGIN_DIR);
define('AITFS_URL', FLASHSALE_CORE_PLUGIN_URL);
define('AIT_DEV_VERSION', FLASHSALE_CORE_VERSION);

/**
 * Debug function for development
 */
if (!function_exists('fs_debug')) {
    function fs_debug($data, $die = false) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            echo '<pre>';
            print_r($data);
            echo '</pre>';
            if ($die) {
                die();
            }
        }
    }
}

/**
 * Check if WooCommerce is active
 */
function flashsale_core_check_woocommerce() {
    if (!class_exists('WooCommerce')) {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error"><p>';
            echo __('CanhcamPromotion Core requires WooCommerce to be installed and active.', 'canhcampromotion-core');
            echo '</p></div>';
        });
        return false;
    }
    return true;
}

/**
 * Plugin activation hook
 */
function activate_flashsale_core() {
    // Check WooCommerce dependency
    if (!flashsale_core_check_woocommerce()) {
        deactivate_plugins(plugin_basename(__FILE__));
        wp_die(__('CanhcamPromotion Core requires WooCommerce to be installed and active.', 'canhcampromotion-core'));
    }

    require_once FLASHSALE_CORE_PLUGIN_DIR . 'includes/class-fs-activator.php';
    FS_Activator::activate();
}

/**
 * Plugin deactivation hook
 */
function deactivate_flashsale_core() {
    require_once FLASHSALE_CORE_PLUGIN_DIR . 'includes/class-fs-deactivator.php';
    FS_Deactivator::deactivate();
}

register_activation_hook(__FILE__, 'activate_flashsale_core');
register_deactivation_hook(__FILE__, 'deactivate_flashsale_core');

/**
 * Load plugin textdomain
 */
function flashsale_core_load_textdomain() {
    load_plugin_textdomain(
        'canhcampromotion-core',
        false,
        dirname(plugin_basename(__FILE__)) . '/languages/'
    );
}
add_action('plugins_loaded', 'flashsale_core_load_textdomain');

/**
 * Initialize the plugin
 */
function init_flashsale_core() {
    // Prevent duplicate initialization
    if (defined('FLASHSALE_CORE_INITIALIZED')) {
        return;
    }
    define('FLASHSALE_CORE_INITIALIZED', true);
    
    // Check WooCommerce dependency
    if (!flashsale_core_check_woocommerce()) {
        return;
    }
    
    // Load core classes
    require_once FLASHSALE_CORE_PLUGIN_DIR . 'includes/class-fs-core.php';
    
    // Initialize the core
    FS_Core::get_instance();
}
add_action('plugins_loaded', 'init_flashsale_core', 10);

/**
 * Plugin loaded hook for addons
 */
do_action('flashsale_core_loaded');
do_action('canhcampromotion_core_loaded');

/**
 * Create alias class for CanhcamPromotion compatibility
 */
if (!class_exists('CanhcamPromotion_Core')) {
    class CanhcamPromotion_Core {
        public static function get_instance() {
            return FS_Core::get_instance();
        }

        public function addons() {
            return FS_Core::get_instance()->addon_manager;
        }
    }
}

/**
 * CCP function alias for CanhcamPromotion compatibility
 */
if (!function_exists('CCP')) {
    function CCP() {
        return CanhcamPromotion_Core::get_instance();
    }
}
