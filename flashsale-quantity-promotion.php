<?php
/**
 * Plugin Name: CanhcamPromotion - Quantity Promotion Addons
 * Plugin URI: https://vietanhit.com/canhcampromotion-quantity-promotion
 * Description: <PERSON>don khuyến mãi theo số lượng sản phẩm cho CanhcamPromotion - Thiết lập khuyến mãi giảm % hoặc số tiền theo từng khoảng số lượng mua của sản phẩm
 * Version: 1.0.0
 * Author: Viet Anh
 * Author URI: https://vietanhit.com
 * Text Domain: canhcampromotion-quantity-promotion
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Requires Plugins: canhcampromotion-core
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('FSQP_VERSION', '1.0.0');
define('FSQP_PLUGIN_FILE', __FILE__);
define('FSQP_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('FSQP_PLUGIN_URL', plugin_dir_url(__FILE__));
define('FSQP_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main plugin class
 */
class FlashSale_Quantity_Promotion {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('plugins_loaded', [$this, 'init']);
        register_activation_hook(__FILE__, [$this, 'activate']);
        register_deactivation_hook(__FILE__, [$this, 'deactivate']);
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Check if FlashSale Core is active
        if (!$this->is_flashsale_core_active()) {
            add_action('admin_notices', [$this, 'flashsale_core_missing_notice']);
            return;
        }
        
        // Check if WooCommerce is active
        if (!$this->is_woocommerce_active()) {
            add_action('admin_notices', [$this, 'woocommerce_missing_notice']);
            return;
        }
        
        // Load plugin components
        $this->load_textdomain();
        $this->includes();
        $this->init_components();
        
        // Register addon with FlashSale Core
        $this->register_addon();
    }
    
    /**
     * Check if CanhcamPromotion Core is active
     */
    private function is_flashsale_core_active() {
        return class_exists('FS_Core') || function_exists('FS');
    }
    
    /**
     * Check if WooCommerce is active
     */
    private function is_woocommerce_active() {
        return class_exists('WooCommerce');
    }
    
    /**
     * Load text domain
     */
    private function load_textdomain() {
        load_plugin_textdomain(
            'canhcampromotion-quantity-promotion',
            false,
            dirname(FSQP_PLUGIN_BASENAME) . '/languages'
        );
    }
    
    /**
     * Include required files
     */
    private function includes() {
        require_once FSQP_PLUGIN_DIR . 'includes/class-fsqp-database.php';
        require_once FSQP_PLUGIN_DIR . 'includes/class-fsqp-admin.php';
        require_once FSQP_PLUGIN_DIR . 'includes/class-fsqp-public.php';
        require_once FSQP_PLUGIN_DIR . 'includes/class-fsqp-ajax.php';
        require_once FSQP_PLUGIN_DIR . 'includes/class-fsqp-campaign-handler.php';
    }
    
    /**
     * Initialize components
     */
    private function init_components() {
        new FSQP_Database();
        new FSQP_Admin();
        new FSQP_Public();
        new FSQP_Ajax();
        new FSQP_Campaign_Handler();
    }
    
    /**
     * Register addon with CanhcamPromotion Core
     */
    private function register_addon() {
        // Wait for CanhcamPromotion Core to be fully loaded
        add_action('flashsale_core_loaded', [$this, 'do_register_addon']);
        add_action('canhcampromotion_core_loaded', [$this, 'do_register_addon']);

        // Also try immediate registration if core is already loaded
        if (function_exists('FS') && FS()->addons()) {
            $this->do_register_addon();
        }
    }

    /**
     * Actually register the addon
     */
    public function do_register_addon() {
        // Prevent multiple registrations
        static $registered = false;
        if ($registered) return;

        // Check if either core system is available
        if ((function_exists('FS') && FS()->addons()) ||
            (function_exists('CCP') && method_exists(CCP(), 'addons'))) {

            $registered = true;

            // Try FlashSale Core first
            if (function_exists('FS') && FS()->addons()) {
                FS()->addons()->register_addon('quantity-promotion', [
                    'name' => __('Quantity Promotion', 'canhcampromotion-quantity-promotion'),
                    'version' => FSQP_VERSION,
                    'type' => 'promotion',
                    'description' => __('Khuyến mãi theo số lượng sản phẩm - Thiết lập khuyến mãi giảm % hoặc số tiền theo từng khoảng số lượng mua', 'canhcampromotion-quantity-promotion'),
                    'author' => 'Viet Anh',
                    'file' => FSQP_PLUGIN_FILE,
                    'settings' => [
                        'enable_logging' => get_option('fsqp_enable_logging', 1),
                        'apply_to_variations' => get_option('fsqp_apply_to_variations', 1),
                        'show_savings_message' => get_option('fsqp_show_savings_message', 1)
                    ],
                    'dependencies' => [],
                    'status' => 1
                ]);
            }

            // Register campaign type
            add_filter('fs_campaign_types', [$this, 'register_campaign_type']);
            add_filter('ccp_campaign_types', [$this, 'register_campaign_type']);
        }
    }

    /**
     * Register quantity promotion campaign type
     */
    public function register_campaign_type($types) {
        $types['quantity-promotion'] = [
            'name' => __('Quantity Promotion', 'canhcampromotion-quantity-promotion'),
            'description' => __('Khuyến mãi theo số lượng sản phẩm', 'canhcampromotion-quantity-promotion'),
            'icon' => 'dashicons-chart-bar',
            'template' => $this->get_template_path(),
            'handler_class' => 'FSQP_Campaign_Handler'
        ];

        return $types;
    }

    /**
     * Get template path for campaign form
     */
    private function get_template_path() {
        // Always use addon's own template for quantity promotion
        $addon_template = FSQP_PLUGIN_DIR . 'admin/views/quantity-promotion-form.php';

        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('FSQP: Looking for template at: ' . $addon_template);
            error_log('FSQP: Template exists: ' . (file_exists($addon_template) ? 'YES' : 'NO'));
        }

        if (file_exists($addon_template)) {
            return $addon_template;
        }

        // Fallback: try to find in core plugin directory (legacy support)
        if (defined('FLASHSALE_CORE_PLUGIN_DIR')) {
            $core_template = FLASHSALE_CORE_PLUGIN_DIR . 'admin/views/campaign-types/quantity-promotion.php';
            if (file_exists($core_template)) {
                return $core_template;
            }
        }

        if (defined('CANHCAMPROMOTION_CORE_PLUGIN_DIR')) {
            $core_template = CANHCAMPROMOTION_CORE_PLUGIN_DIR . 'admin/views/campaign-types/quantity-promotion.php';
            if (file_exists($core_template)) {
                return $core_template;
            }
        }

        // If nothing found, return addon template path anyway
        return $addon_template;
    }
    
    /**
     * Render addon settings
     */
    public function render_settings($settings = []) {
        include FSQP_PLUGIN_DIR . 'admin/views/settings.php';
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Load required files first
        $this->includes();

        // Only create logs table (FlashSale Core tables already exist)
        $database = new FSQP_Database();
        $database->create_tables();

        // Set default options
        add_option('fsqp_version', FSQP_VERSION);
        add_option('fsqp_enable_logging', 1);
        add_option('fsqp_apply_to_variations', 1);

        // Verify CanhcamPromotion Core tables exist
        if (!$this->verify_flashsale_core_tables()) {
            deactivate_plugins(plugin_basename(__FILE__));
            wp_die(__('CanhcamPromotion Core tables not found. Please ensure CanhcamPromotion Core is properly installed.', 'canhcampromotion-quantity-promotion'));
        }
    }

    /**
     * Verify FlashSale Core tables exist
     */
    private function verify_flashsale_core_tables() {
        global $wpdb;

        $required_tables = [
            $wpdb->prefix . 'fs_campaigns',
            $wpdb->prefix . 'fs_campaign_products'
        ];

        foreach ($required_tables as $table) {
            if ($wpdb->get_var("SHOW TABLES LIKE '$table'") !== $table) {
                return false;
            }
        }

        return true;
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up scheduled events
        wp_clear_scheduled_hook('fsqp_cleanup_logs');
    }
    
    /**
     * CanhcamPromotion Core missing notice
     */
    public function flashsale_core_missing_notice() {
        echo '<div class="notice notice-error"><p>';
        echo sprintf(
            __('CanhcamPromotion - Quantity Promotion Addons requires %s to be installed and activated.', 'canhcampromotion-quantity-promotion'),
            '<strong>CanhcamPromotion Core</strong>'
        );
        echo '</p></div>';
    }

    /**
     * WooCommerce missing notice
     */
    public function woocommerce_missing_notice() {
        echo '<div class="notice notice-error"><p>';
        echo sprintf(
            __('CanhcamPromotion - Quantity Promotion Addons requires %s to be installed and activated.', 'canhcampromotion-quantity-promotion'),
            '<strong>WooCommerce</strong>'
        );
        echo '</p></div>';
    }
}

/**
 * Get main plugin instance
 */
function FSQP() {
    return FlashSale_Quantity_Promotion::instance();
}

// Initialize plugin
FSQP();
