# Cache Optimization - FlashSale Core v2

## Vấn đề đã giải quyết

Tr<PERSON>ớc đây, trong quá trình render một trang sản phẩm, hàm `get_product_promotions()` được gọi nhiều lần từ các hook khác nhau:

1. `display_promotion_info()` - Hiển thị thông tin khuyến mãi
2. `display_promotion_badge()` - Hiển thị badge khuyến mãi  
3. `modify_price_display()` - Thay đổi hiển thị giá
4. `get_sale_price()` - Lấy giá sale
5. `get_flashsale_price()` - Lấy giá flashsale
6. `apply_flashsale_cart_price()` - Áp dụng giá trong giỏ hàng

Mỗi lần gọi đều phải:
- Query database để lấy active campaigns
- Query database để lấy campaign products
- X<PERSON> lý logic tính toán khuyến mãi

## Giải pháp Cache

### 1. Campaign Cache
- Đã có sẵn: `$active_campaigns` cache cho danh sách campaigns đang hoạt động
- Chỉ query database 1 lần cho tất cả campaigns

### 2. Product Promotions Cache (MỚI)
- Thêm `$product_promotions_cache` để cache kết quả theo product_id
- Mỗi sản phẩm chỉ được xử lý 1 lần trong cùng request
- Cache được lưu theo format: `$product_promotions_cache[$product_id] = $promotions`

### 3. Auto Cache Invalidation
- Cache tự động được clear khi:
  - Campaign được tạo/cập nhật/xóa (`fs_campaign_*` hooks)
  - Sản phẩm được cập nhật/xóa (`woocommerce_update_product`, `woocommerce_delete_product`)

### 4. Duplicate Prevention (MỚI)
- **Vấn đề**: Có thể có nhiều records trong `fs_campaign_products` cho cùng một `product_id` + `campaign_id`
- **Nguyên nhân**: Product variants, multiple configurations, hoặc data duplicates
- **Giải pháp**: Chỉ lấy 1 record tốt nhất (highest discount_value) cho mỗi combination
- **Kết quả**: Tránh hiển thị duplicate progress bars trong `display_promotion_info()`

## Kết quả

- **Giảm số lần query database**: Từ N lần xuống 1 lần cho mỗi sản phẩm
- **Tăng tốc độ render**: Đặc biệt trên trang shop với nhiều sản phẩm
- **Giảm tải server**: Ít query database hơn
- **Tương thích ngược**: Không thay đổi API, chỉ tối ưu performance
- **Fix duplicate progress bars**: Chỉ hiển thị 1 thanh tiến trình cho mỗi campaign

## API Cache

### Clear Cache Methods
```php
// Clear tất cả cache
FS_Public::clear_all_caches();

// Clear cache cho sản phẩm cụ thể
FS_Public::clear_product_promotions_cache($product_id);

// Clear cache cho tất cả sản phẩm
FS_Public::clear_product_promotions_cache();

// Clear chỉ campaigns cache
FS_Public::clear_campaigns_cache();
```

### Hooks tự động clear cache
- `fs_campaign_created`
- `fs_campaign_updated` 
- `fs_campaign_deleted`
- `woocommerce_update_product`
- `woocommerce_delete_product`

## Lưu ý

Cache chỉ tồn tại trong phạm vi 1 request HTTP. Mỗi request mới sẽ bắt đầu với cache trống, đảm bảo dữ liệu luôn fresh và không có vấn đề về memory leak.
