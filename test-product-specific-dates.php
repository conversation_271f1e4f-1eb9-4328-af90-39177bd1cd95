<?php
/**
 * Test script for product-specific date functionality
 * Add this to wp-config.php or run in WordPress admin context
 */

function test_product_specific_dates() {
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }
    
    echo "<h1>Product-Specific Date Functionality Test</h1>";
    echo "<p><strong>Goal:</strong> Verify that product-specific start/end dates take priority over campaign dates</p>";
    
    // Step 1: Database structure check
    echo "<h2>Step 1: Database Structure Check</h2>";
    
    global $wpdb;
    $products_table = $wpdb->prefix . 'fs_campaign_products';
    
    // Check if start_date and end_date columns exist
    $columns = $wpdb->get_results("DESCRIBE {$products_table}");
    $has_start_date = false;
    $has_end_date = false;
    
    echo "<p><strong>Checking {$products_table} table structure:</strong></p>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>Column</th><th>Type</th><th>Status</th>";
    echo "</tr>";
    
    foreach ($columns as $column) {
        $status = '✅ OK';
        if ($column->Field === 'start_date') {
            $has_start_date = true;
            $status = '✅ Product-specific start date';
        } elseif ($column->Field === 'end_date') {
            $has_end_date = true;
            $status = '✅ Product-specific end date';
        }
        
        echo "<tr>";
        echo "<td><code>{$column->Field}</code></td>";
        echo "<td>{$column->Type}</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if (!$has_start_date || !$has_end_date) {
        echo "<div style='background: #fee; border: 1px solid #f00; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h3 style='color: #d00; margin-top: 0;'>❌ Missing Columns</h3>";
        echo "<p>The following columns are missing from {$products_table}:</p>";
        echo "<ul>";
        if (!$has_start_date) echo "<li><code>start_date</code> - Product-specific start date</li>";
        if (!$has_end_date) echo "<li><code>end_date</code> - Product-specific end date</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #efe; border: 1px solid #0a0; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h3 style='color: #0a0; margin-top: 0;'>✅ Database Structure OK</h3>";
        echo "<p>All required columns exist for product-specific dates.</p>";
        echo "</div>";
    }
    
    // Step 2: Test data scenarios
    echo "<h2>Step 2: Date Priority Logic Test</h2>";
    
    $test_scenarios = [
        [
            'name' => 'Product has specific dates',
            'campaign_start' => '2024-01-01 00:00:00',
            'campaign_end' => '2024-12-31 23:59:59',
            'product_start' => '2024-06-01 00:00:00',
            'product_end' => '2024-06-30 23:59:59',
            'expected_start' => '2024-06-01 00:00:00',
            'expected_end' => '2024-06-30 23:59:59',
            'description' => 'Product dates should take priority'
        ],
        [
            'name' => 'Product has no specific dates',
            'campaign_start' => '2024-01-01 00:00:00',
            'campaign_end' => '2024-12-31 23:59:59',
            'product_start' => null,
            'product_end' => null,
            'expected_start' => '2024-01-01 00:00:00',
            'expected_end' => '2024-12-31 23:59:59',
            'description' => 'Campaign dates should be used'
        ],
        [
            'name' => 'Product has only start date',
            'campaign_start' => '2024-01-01 00:00:00',
            'campaign_end' => '2024-12-31 23:59:59',
            'product_start' => '2024-06-01 00:00:00',
            'product_end' => null,
            'expected_start' => '2024-06-01 00:00:00',
            'expected_end' => '2024-12-31 23:59:59',
            'description' => 'Mix of product and campaign dates'
        ],
        [
            'name' => 'Product has only end date',
            'campaign_start' => '2024-01-01 00:00:00',
            'campaign_end' => '2024-12-31 23:59:59',
            'product_start' => null,
            'product_end' => '2024-06-30 23:59:59',
            'expected_start' => '2024-01-01 00:00:00',
            'expected_end' => '2024-06-30 23:59:59',
            'description' => 'Mix of campaign and product dates'
        ]
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>Scenario</th><th>Campaign Dates</th><th>Product Dates</th><th>Expected Result</th><th>Status</th>";
    echo "</tr>";
    
    foreach ($test_scenarios as $scenario) {
        // Test the logic
        $effective_start = !empty($scenario['product_start']) ? $scenario['product_start'] : $scenario['campaign_start'];
        $effective_end = !empty($scenario['product_end']) ? $scenario['product_end'] : $scenario['campaign_end'];
        
        $start_correct = ($effective_start === $scenario['expected_start']);
        $end_correct = ($effective_end === $scenario['expected_end']);
        $status = ($start_correct && $end_correct) ? '✅ PASS' : '❌ FAIL';
        
        echo "<tr>";
        echo "<td><strong>{$scenario['name']}</strong><br><small>{$scenario['description']}</small></td>";
        echo "<td>Start: {$scenario['campaign_start']}<br>End: {$scenario['campaign_end']}</td>";
        echo "<td>Start: " . ($scenario['product_start'] ?: 'NULL') . "<br>End: " . ($scenario['product_end'] ?: 'NULL') . "</td>";
        echo "<td>Start: {$scenario['expected_start']}<br>End: {$scenario['expected_end']}</td>";
        echo "<td style='color: " . ($status === '✅ PASS' ? 'green' : 'red') . ";'>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Step 3: Current time validation
    echo "<h2>Step 3: Current Time Validation Test</h2>";
    
    $current_time = current_time('mysql');
    echo "<p><strong>Current WordPress time:</strong> $current_time</p>";
    
    $validation_scenarios = [
        [
            'name' => 'Future promotion',
            'start_date' => date('Y-m-d H:i:s', strtotime('+1 day')),
            'end_date' => date('Y-m-d H:i:s', strtotime('+7 days')),
            'should_be_active' => false,
            'reason' => 'Start date is in the future'
        ],
        [
            'name' => 'Active promotion',
            'start_date' => date('Y-m-d H:i:s', strtotime('-1 day')),
            'end_date' => date('Y-m-d H:i:s', strtotime('+7 days')),
            'should_be_active' => true,
            'reason' => 'Within active date range'
        ],
        [
            'name' => 'Expired promotion',
            'start_date' => date('Y-m-d H:i:s', strtotime('-7 days')),
            'end_date' => date('Y-m-d H:i:s', strtotime('-1 day')),
            'should_be_active' => false,
            'reason' => 'End date has passed'
        ]
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>Scenario</th><th>Start Date</th><th>End Date</th><th>Expected</th><th>Actual</th><th>Status</th>";
    echo "</tr>";
    
    foreach ($validation_scenarios as $scenario) {
        $is_active = true;
        
        if (!empty($scenario['start_date']) && $current_time < $scenario['start_date']) {
            $is_active = false;
        }
        
        if (!empty($scenario['end_date']) && $current_time > $scenario['end_date']) {
            $is_active = false;
        }
        
        $status = ($is_active === $scenario['should_be_active']) ? '✅ PASS' : '❌ FAIL';
        
        echo "<tr>";
        echo "<td><strong>{$scenario['name']}</strong><br><small>{$scenario['reason']}</small></td>";
        echo "<td>{$scenario['start_date']}</td>";
        echo "<td>{$scenario['end_date']}</td>";
        echo "<td>" . ($scenario['should_be_active'] ? 'Active' : 'Inactive') . "</td>";
        echo "<td>" . ($is_active ? 'Active' : 'Inactive') . "</td>";
        echo "<td style='color: " . ($status === '✅ PASS' ? 'green' : 'red') . ";'>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Step 4: Implementation details
    echo "<h2>Step 4: Implementation Details</h2>";
    
    echo "<div style='background: #f9f9f9; padding: 15px; border-left: 4px solid #0073aa;'>";
    echo "<h3>Code Changes Made:</h3>";
    echo "<ol>";
    echo "<li><strong>Date Priority Logic:</strong> Product-specific dates take priority over campaign dates</li>";
    echo "<li><strong>Active Promotion Check:</strong> Only include promotions within effective date range</li>";
    echo "<li><strong>Countdown Timer:</strong> Uses product-specific end date if available</li>";
    echo "<li><strong>Progress Bar:</strong> Respects product-specific date ranges</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3 style='color: #856404; margin-top: 0;'>🔧 How It Works</h3>";
    echo "<ol>";
    echo "<li><strong>Date Resolution:</strong> <code>\$effective_start = !empty(\$product_start) ? \$product_start : \$campaign_start</code></li>";
    echo "<li><strong>Active Check:</strong> <code>current_time() >= \$effective_start && current_time() <= \$effective_end</code></li>";
    echo "<li><strong>Pricing:</strong> Only apply discounts if product is within effective date range</li>";
    echo "<li><strong>Display:</strong> Show countdown based on effective end date</li>";
    echo "</ol>";
    echo "</div>";
    
    // Step 5: Testing instructions
    echo "<h2>Step 5: Manual Testing Instructions</h2>";
    
    echo "<div style='background: #e3f2fd; border: 1px solid #1976d2; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #1976d2; margin-top: 0;'>🧪 Testing Steps</h3>";
    echo "<ol>";
    echo "<li><strong>Create a flash sale campaign</strong> with dates: 2024-01-01 to 2024-12-31</li>";
    echo "<li><strong>Add a product</strong> with specific dates: Today to Tomorrow</li>";
    echo "<li><strong>Add another product</strong> without specific dates (use campaign dates)</li>";
    echo "<li><strong>Check product pages:</strong>";
    echo "<ul>";
    echo "<li>Product with specific dates should show countdown to tomorrow</li>";
    echo "<li>Product without specific dates should show countdown to Dec 31</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>Wait until tomorrow</strong> and check:</li>";
    echo "<ul>";
    echo "<li>Product with specific dates should no longer show discount</li>";
    echo "<li>Product without specific dates should still show discount</li>";
    echo "</ul>";
    echo "</li>";
    echo "</ol>";
    echo "</div>";
    
    // Step 6: Summary
    echo "<h2>Step 6: Summary</h2>";
    
    echo "<div style='background: #e8f5e8; border: 1px solid #27ae60; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #27ae60; margin-top: 0;'>🎯 Benefits Achieved</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>Flexible Scheduling:</strong> Each product can have its own promotion period</li>";
    echo "<li>✅ <strong>Accurate Pricing:</strong> Discounts only apply within effective date range</li>";
    echo "<li>✅ <strong>Correct Countdown:</strong> Timer shows product-specific end date</li>";
    echo "<li>✅ <strong>Fallback Logic:</strong> Uses campaign dates when product dates not set</li>";
    echo "<li>✅ <strong>Performance:</strong> Date checks happen during promotion retrieval</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-top: 15px;'>";
    echo "<h3 style='color: #856404; margin-top: 0;'>⚠️ Important Notes</h3>";
    echo "<ul>";
    echo "<li>Product-specific dates always take priority over campaign dates</li>";
    echo "<li>If only one date is set (start OR end), the other comes from campaign</li>";
    echo "<li>Promotions outside their effective date range are completely excluded</li>";
    echo "<li>Progress bars and countdown timers use effective dates</li>";
    echo "<li>Cache is cleared when promotions are updated</li>";
    echo "</ul>";
    echo "</div>";
}

// Add admin page
add_action('admin_menu', function() {
    if (current_user_can('manage_options')) {
        add_submenu_page(
            null,
            'Test Product-Specific Dates',
            'Test Product-Specific Dates',
            'manage_options',
            'test-product-specific-dates',
            'test_product_specific_dates'
        );
    }
});

// Add quick access
add_action('admin_notices', function() {
    if (current_user_can('manage_options') && 
        (strpos($_SERVER['REQUEST_URI'], 'flashsale') !== false || 
         strpos($_SERVER['REQUEST_URI'], 'campaign') !== false)) {
        echo '<div class="notice notice-info"><p>';
        echo '<strong>Product-Specific Dates:</strong> ';
        echo '<a href="?page=test-product-specific-dates" target="_blank" class="button button-small">Test Date Logic</a>';
        echo '</p></div>';
    }
});
